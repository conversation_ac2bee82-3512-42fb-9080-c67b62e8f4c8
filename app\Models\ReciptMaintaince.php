<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Stancl\Tenancy\Database\Concerns\CentralConnection;

class ReciptMaintaince extends Model
{
    use HasFactory, CentralConnection;
      protected $table = 'recipt_maintainces';
      protected $fillable = [
        'Code',
        'Date',
        'Note',
        'Serial_Num',
        'Total',
        'Product_Numbers',
        'Total_Qty',
        'Total_Discount',
        'Total_Cost',
        'Total_Bf_Taxes',
        'Total_Taxes',
        'Draw',
        'Company',
        'Device_Type',
        'Device_Case',
        'Coin',
        'Cost_Center',
        'Account',
        'User',
        'Status',
        'Pattern_Image',
        'Time',
        'Payment_Method',
        'Password',
        'Pay',
        'Eng_Note',
        'Reason',
        'Report_Client',
        'Work',
        'Store',
        'Eng',
        'Recipient',
        'Branch',
        'RefuseReason',
        'NoteRecived',
        'Returned',
        'CustomerGroup',

    ];

           public function Company()
    {
        return $this->belongsTo(ManufactureCompany::class,'Company');
    }

               public function RefuseReason()
    {
        return $this->belongsTo(Reasons::class,'RefuseReason');
    }

               public function Device_Type()
    {
        return $this->belongsTo(DevicesTypesy::class,'Device_Type');
    }

               public function Device_Case()
    {
        return $this->belongsTo(DesviceCases::class,'Device_Case');
    }

               public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }

               public function Cost_Center()
    {
        return $this->belongsTo(CostCenter::class,'Cost_Center');
    }

               public function Account()
    {
        return $this->belongsTo(AcccountingManual::class,'Account');
    }

               public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }

        public function ReciptMaintainceErrors()
    {
        return $this->hasOne(ReciptMaintainceErrors::class);
    }

            public function ProductMaintaincBill()
    {
        return $this->hasOne(ProductMaintaincBill::class);
    }


    public function Recipient()
    {
        return $this->belongsTo(Employess::class,'Recipient');
    }

    public function Eng()
    {
        return $this->belongsTo(Employess::class,'Eng');
    }

    public function Store()
    {
        return $this->belongsTo(Stores::class,'Store');
    }

    public function Branch()
    {
        return $this->belongsTo(Branches::class,'Branch');
    }



           public function CustomerGroup()
    {
        return $this->belongsTo(CustomersGroup::class,'CustomerGroup');
    }

}
