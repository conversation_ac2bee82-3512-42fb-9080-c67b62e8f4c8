<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Stancl\Tenancy\Database\Concerns\CentralConnection;

class ReciptVoucher extends Model
{
    use HasFactory, CentralConnection;
           protected $table = 'recipt_vouchers';
      protected $fillable = [
        'Code',
        'Date',
        'Draw',
        'Coin',
        'Cost_Center',
        'Total_Creditor',
        'Safe',
        'Note',
        'Shift',
        'Store',
        'User',
              'Status',
          'File',


    ];

        public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }
        public function Cost_Center()
    {
        return $this->belongsTo(CostCenter::class,'Cost_Center');
    }

             public function Store()
    {
        return $this->belongsTo(Stores::class,'Store');
    }

        public function Safe()
    {
        return $this->belongsTo(AcccountingManual::class,'Safe');
    }
                public function User()
    {
        return $this->belongsTo(Employess::class,'User');
    }
            public function ReciptVoucherDetails()
    {
        return $this->hasOne(ReciptVoucherDetails::class);
    }




}
