<?php $__env->startSection('content'); ?>
<?php
use App\Models\AccountsDefaultData;
$Def=AccountsDefaultData::orderBy('id','desc')->first();
use App\Models\DefaultDataShowHide;
$show=DefaultDataShowHide::orderBy('id','desc')->first(); 
?>
<title><?php echo e(trans('admin.Receipt_Voucher')); ?></title>
<style>
    .total {
    background-color: #29cded!important;
    }
     body {
    overflow-x: hidden;
   }
</style>

  <main id="js-page-content" role="main" class="page-content">
                    <ol class="breadcrumb page-breadcrumb">
                        <li class="breadcrumb-item"><a href="javascript:void(0);"><?php echo e(trans('admin.Accounts')); ?> </a></li>
                        <li class="breadcrumb-item active"><?php echo e(trans('admin.Receipt_Voucher')); ?></li>
                        <li class="position-absolute pos-top pos-right d-none d-sm-block"><span
                                class="js-get-date"></span></li>
                    </ol>
      <form id="form" action="<?php echo e(url('AddReceipt_Voucher')); ?>" method="post">
         <?php echo csrf_field(); ?>  
                <?php echo view('honeypot::honeypotFormFields'); ?>
                    <div class="row">
                        <div class="col-lg-12">
                            <div id="panel-2" class="panel">
                                <div class="panel-hdr">
                                    <h2>
                                        <span class="fw-300"><i>  <?php echo e(trans('admin.Receipt_Voucher')); ?> </i></span>
                                    </h2>
                                </div>
                                <div class="panel-container show">
                                       <span id="ex"> <?php echo $__env->make('admin.layouts.messages', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>  
                                    <div class="panel-content">
                                      <div class="form-row">
                                        <div class="form-group col-lg-2">
                                            <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Code')); ?> </label>
                            <input type="text"  value="<?php echo e($Code); ?>" class="form-control " disabled>
                            <input type="hidden" name="Code"  value="<?php echo e($Code); ?>">
                                        </div>
                                     <?php if(auth()->guard('admin')->user()->emp == 0): ?>
                      <div class="form-group col-lg-2">
                                    <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Date')); ?></label>
                                    <input type="date" name="Date" value="<?php echo e(date('Y-m-d')); ?>" class="form-control" required>
                                </div>
                                        <?php else: ?>
                                        
                                         <?php if(auth()->guard('admin')->user()->Date == 1): ?>
                                   <div class="form-group col-lg-2">
                                    <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Date')); ?></label>
                                    <input type="date" name="Date" value="<?php echo e(date('Y-m-d')); ?>" class="form-control" required>
                                </div>
                                        <?php else: ?>
                                 <div class="form-group col-lg-2">
                                    <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Date')); ?></label>
                                    <input type="date" name="Date" value="<?php echo e(date('Y-m-d')); ?>" class="form-control" required readonly>
                                </div>
                                        <?php endif; ?>
                                        
                                        <?php endif; ?>
                                        <div class="form-group col-lg-3">
                                            <label class="form-label" for=""><?php echo e(trans('admin.Currency')); ?></label>
                                            <select class="select2 form-control w-100" name="Coin" required>
                                    <option value=""><?php echo e(trans('admin.Currency')); ?></option>             
                                                <?php $__currentLoopData = $Coins; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $coin): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                         <option value="<?php echo e($coin->id); ?>" <?php if($coin->id == $Def->Coin): ?> selected <?php endif; ?>>
                       <?php echo e(app()->getLocale() == 'ar' ?$coin->Arabic_Name :$coin->English_Name); ?>        
                                                </option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                        </div>
                                        <div class="form-group col-lg-2">
                                            <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Draw')); ?></label>
                       <input type="number" step="any" name="Draw" value="<?php echo e($Def->Draw); ?>" class="form-control" required />
                                        </div>
                                        <div class="form-group col-lg-3">
                                            <label class="form-label" for=""><?php echo e(trans('admin.Cost_Center')); ?></label>
                                            <select class="select2 form-control w-100" name="Cost_Center">
                                        <option value=""><?php echo e(trans('admin.Cost_Center')); ?></option>        
                                            <?php $__currentLoopData = $CostCenters; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cost): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($cost->id); ?>">
                       <?php echo e(app()->getLocale() == 'ar' ?$cost->Arabic_Name :$cost->English_Name); ?>        
                                                </option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                        </div> 
                                          
                                             
                                         <?php if($show->Show_File_ReciptVoucher == 1): ?>
                                     <div class="form-group col-lg-12">
                                    <label class="form-label" for="simpleinput"><?php echo e(trans('admin.File')); ?></label>
                                    <input type="file" name="File"  class="form-control">
                                </div>    
                                <?php endif; ?>         
                                          
                                    </div>
                            </div>
                        </div>
                    </div>
                    </div>
                    </div>
                    <div class="row">
                        <div class="col-xl-12">
                            <div id="panel-1" class="panel">
                               
                                <div class="panel-container show">
                                    <div class="panel-content">
                                        <div id="mobile-overflow">
                                        <table 
                                        class="table table-bordered table-hover table-striped w-100 mobile-width table-color1">
                                        <thead>
                                            <tr>
                                               
                                             <th> <?php echo e(trans('admin.Creditor')); ?></th>
                                            <th>  <?php echo e(trans('admin.Account_Name')); ?></th>
                                            <th><?php echo e(trans('admin.Statement')); ?></th>
                                                
                                        <?php if($Def->Account_Balance == 1): ?>        
                                                      <th><?php echo e(trans('admin.Account_Credit')); ?></th>
                                                <?php endif; ?>
                                            <th><?php echo e(trans('admin.Actions')); ?></th>
                                               
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                              
                                                <td>
                                                    <div class="form-group">
                                                       
                                                <div class="form-group">
                                 <input onkeyup="PLUS()" type="number" step="any" id="creditor" class="form-control">
                                                </div>
                                                    </div>
                                                </td>
                                               
                                                <td>
                                                  <div class="form-group">
                              <select onchange="PLUS()" class="select2 form-control w-100" id="AccountCode">
                                                   <option value=""><?php echo e(trans('admin.Account')); ?></option>      
                                             <?php $__currentLoopData = $Accounts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $acc): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                  <option value="<?php echo e($acc->id); ?>"> <?php echo e(app()->getLocale() == 'ar' ?$acc->Name :$acc->NameEn); ?> </option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </select>
                                                    
                                                  <input type="hidden" id="AccountName" class="form-control">
                                                       <input type="hidden" id="AccCode" class="form-control">
                                                      <input type="hidden" id="AccID" class="form-control">
                                                </div>
                                                </td>
                                               
                                                <td>
                                                        <div class="form-group">
                                                    <input type="text" onkeyup="PLUS()" id="statement" class="form-control">
                                                </div>
                                                    
                                                </td>
                                              <?php if($Def->Account_Balance == 1): ?>            
                                              <td>
                                           <input type="text" id="AccountCredit" value="0" class="form-control " disabled>              
                                                       
                                                </td>  
                                            <?php else: ?>
                                                <input type="hidden" id="AccountCredit" value="0" class="form-control " disabled>    
                                            <?php endif; ?>    
                                                <td>
                                                    <button type="button" onclick="Insert()" class="btn btn-default" id="add-data" style="display: none"><i class="fal fa-plus"></i></button>
                                                </td>
                                               
                                            </tr>
                                        </tbody>
                                        </table>
                                         </div>
                                        <!-- datatable start -->
                                        <div id="mobile-overflow">
                                        <table id="dt-basic-example"
                                            class="table table-bordered table-hover table-striped w-100 mobile-width table-color2">
                                            <thead>
                                                <tr>
                                               
                                                 <th><?php echo e(trans('admin.Creditor')); ?></th>
                                                <th><?php echo e(trans('admin.Account_Code')); ?></th>
                                                <th><?php echo e(trans('admin.Account_Name')); ?></th>
                                                <th><?php echo e(trans('admin.Statement')); ?></th>
                                                <th><?php echo e(trans('admin.Actions')); ?></th>
                                                </tr>
                                            </thead>
                                            <tbody id="data-dt">
                                             
                                            </tbody>
                                            <tfoot>
                                                <tr>
                                                <th><?php echo e(trans('admin.Creditor')); ?></th>
                                                <th><?php echo e(trans('admin.Account_Code')); ?></th>
                                                <th><?php echo e(trans('admin.Account_Name')); ?></th>
                                                <th><?php echo e(trans('admin.Statement')); ?></th>
                                                <th><?php echo e(trans('admin.Actions')); ?></th>
                                                </tr>
                                            </tfoot>
                                        </table>
                                         </div>
                                        <!-- datatable end -->
                                        <div class="form-row">
                                      <div class="form-group col-lg-3">
                                     <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Total_Creditor')); ?></label>
                                            <input type="text" id="totalCred" disabled  class="form-control total">
                                    <input type="hidden" id="totalCredHide" name="Total_Creditor"  class="form-control">
                                            
                                        </div>
                                          <div class="form-group col-lg-3">
                                            <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Notes')); ?></label>
                                            <input type="text" name="Note"  class="form-control">
                                        </div>
                                            <div class="form-group col-lg-4">
                                                <div style="width: 100%;display:inline-block;">
                                                <label class="form-label" for=""><?php echo e(trans('admin.Safe')); ?> </label>
                       <select  class="js-data-example-ajax form-control w-100" onchange="safe()" id="Safe" name="Safe" required></select>                        
                                            </div>    
                                            </div>
                                            
                                            <div class="form-group col-lg-2">
                                                <label class="form-label"> <?php echo e(trans('admin.Safe_Balance')); ?> </label>
                                                <button type="button" class="form-control btn btn-primary" id="SafeBalance">
                                                
                                                </button>
                                            </div>
                                        </div>
                                        <div class="buttons mt-3" id="SHOW" style="display: none">
 <input type="hidden" id="sp" name="SP">  
           <button type="button" class="btn btn-primary"onclick="SPS()"> <i class="fal fa-folder"></i> <?php echo e(trans('admin.Save')); ?> </button>
          <button type="button" class="btn btn-primary"onclick="SPP()"><i class="fal fa-save"></i>  <?php echo e(trans('admin.SaveandPrint')); ?> </button>
                                 
                                          </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
      </form>
                </main>





<?php $__env->stopSection(); ?>


<?php $__env->startPush('js'); ?>

    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/datagrid/datatables/datatables.bundle.css')); ?>">
    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/formplugins/summernote/summernote.css')); ?>">
    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/formplugins/select2/select2.bundle.css')); ?>">

  <script src="<?php echo e(asset('Admin/js/datagrid/datatables/datatables.bundle.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/datagrid/datatables/datatables.export.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/formplugins/summernote/summernote.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/formplugins/select2/select2.bundle.js')); ?>"></script>
    

     <script>
        var autoSave = $('#autoSave');
        var interval;
        var timer = function()
        {
            interval = setInterval(function()
            {
                //start slide...
                if (autoSave.prop('checked'))
                    saveToLocal();

                clearInterval(interval);
            }, 3000);
        };

        //save
        var saveToLocal = function()
        {
            localStorage.setItem('summernoteData', $('#saveToLocal').summernote("code"));
            console.log("saved");
        }

        //delete 
        var removeFromLocal = function()
        {
            localStorage.removeItem("summernoteData");
            $('#saveToLocal').summernote('reset');
        }

        $(document).ready(function()
        {
            //init default
            $('.js-summernote').summernote(
            {
                height: 200,
                tabsize: 2,
                placeholder: "Type here...",
                dialogsFade: true,
                toolbar: [
                    ['style', ['style']],
                    ['font', ['strikethrough', 'superscript', 'subscript']],
                    ['font', ['bold', 'italic', 'underline', 'clear']],
                    ['fontsize', ['fontsize']],
                    ['fontname', ['fontname']],
                    ['color', ['color']],
                    ['para', ['ul', 'ol', 'paragraph']],
                    ['height', ['height']]
                    ['table', ['table']],
                    ['insert', ['link', 'picture', 'video']],
                    ['view', ['fullscreen', 'codeview', 'help']]
                ],
                callbacks:
                {
                    //restore from localStorage
                    onInit: function(e)
                    {
                        $('.js-summernote').summernote("code", localStorage.getItem("summernoteData"));
                    },
                    onChange: function(contents, $editable)
                    {
                        clearInterval(interval);
                        timer();
                    }
                }
            });

            //load emojis
            $.ajax(
            {
                url: 'https://api.github.com/emojis',
                async: false
            }).then(function(data)
            {
                window.emojis = Object.keys(data);
                window.emojiUrls = data;
            });

            //init emoji example
            $(".js-hint2emoji").summernote(
            {
                height: 100,
                toolbar: false,
                placeholder: 'type starting with : and any alphabet',
                hint:
                {
                    match: /:([\-+\w]+)$/,
                    search: function(keyword, callback)
                    {
                        callback($.grep(emojis, function(item)
                        {
                            return item.indexOf(keyword) === 0;
                        }));
                    },
                    template: function(item)
                    {
                        var content = emojiUrls[item];
                        return '<img src="' + content + '" width="20" /> :' + item + ':';
                    },
                    content: function(item)
                    {
                        var url = emojiUrls[item];
                        if (url)
                        {
                            return $('<img />').attr('src', url).css('width', 20)[0];
                        }
                        return '';
                    }
                }
            });

            //init mentions example
            $(".js-hint2mention").summernote(
            {
                height: 100,
                toolbar: false,
                placeholder: "type starting with @",
                hint:
                {
                    mentions: ['jayden', 'sam', 'alvin', 'david'],
                    match: /\B@(\w*)$/,
                    search: function(keyword, callback)
                    {
                        callback($.grep(this.mentions, function(item)
                        {
                            return item.indexOf(keyword) == 0;
                        }));
                    },
                    content: function(item)
                    {
                        return '@' + item;
                    }
                }
            });

        });

    </script>
    
     
   <script>
      
        function Insert(){
         var creditor = document.getElementById('creditor').value;
               var accountCode = document.getElementById('AccCode').value;
        var accountID = document.getElementById('AccID').value;
        var accountName = document.getElementById('AccountName').value;
        var statement = document.getElementById('statement').value;

        var table =  ` <tr> 
                                 <td class="TCred">
                                ${creditor}
                               </td>

                                <td>${accountCode}</td>
                                <td>${accountName}</td>
                                <td>${statement}
             <input type="hidden" name="Creditor[]" value="${creditor}">
    <input type="hidden" name="Account[]" value="${accountID}">
    <input type="hidden" name="Statement[]" value="${statement}">
                                 </td>
                                <td>
                    <button id="Del" type="button" class="btn btn-default"><i class="fal fa-trash"></i></button>
                                </td>




                             </tr>`;
            
             if(accountCode != ''  && accountName != ''){
                             document.getElementById('data-dt').innerHTML += table;
             
            var Dept = $('#creditor').val('');
        var Code = $('#AccountCode').val('');
   document.getElementById("add-data").style.display = "none";    
             }
    var sum = 0;        
$('.TCred').each(function(){
  sum += parseFloat($(this).text());
});                  
         
   $('#totalCred').val(sum);        
    $('#totalCredHide').val(sum);     
  
 
    var safee=$('#Safe').val();
        
        
        if(safee != '' && sum != 0){
        document.getElementById("SHOW").style.display="block";
        }else{
            
           document.getElementById("SHOW").style.display="none";   
        } 
            
            
$('#dt-basic-example #data-dt').on('click', '#Del', function(e){
   $(this).closest('tr').remove();
    
    
                
    var sum = 0;        
$('.TCred').each(function(){
  sum += parseFloat($(this).text());
});                  
         
   $('#totalCred').val(sum);        
    $('#totalCredHide').val(sum);     
  
    var safee=$('#Safe').val();
        
        
        if(safee != '' && sum != 0){
        document.getElementById("SHOW").style.display="block";
        }else{
            
           document.getElementById("SHOW").style.display="none";   
        } 
      
    
})    
            
            
         
   
            
        }
        
     

      
             
        

    </script>

<script>
   $(document).ready(function() {
   
                  $('#AccountCode').on('change', function(){
                      var countryId = $(this).val();
                      if(countryId) {
                          $.ajax({
                              url: 'AccountNameFilter/'+countryId,
                              type:"GET",
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },
   
                              success:function(data) {
                                  $.each(data, function(key, value){
   
                $('#AccCode').val(data.code); 
                    $('#AccountName').val(data.name); 
                    $('#AccID').val(data.ID); 
                                  });
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });
                      } else {
   
                          $('select[name="state"]').empty();
                      }
   
                  });
   
              });
</script>  

<script>
    function PLUS(){
        
        var Dept = $('#creditor').val();
        var Code = $('#AccountCode').val();


        
    if(Dept != '' &&  Code != null)
        {
            
             if(parseFloat(Dept)  <=  0){
         
          document.getElementById("add-data").style.display = "none";        
            
        }else{
            
          document.getElementById("add-data").style.display = "block";    
            
        }  
        
           
        
    }else{
        
        document.getElementById("add-data").style.display = "none";      
        
    }    
        
       
        
    }

</script>    

<script>
   $(document).ready(function() {
   
                  $('#Safe').on('change', function(){
                      var countryId = $(this).val();
                      if(countryId) {
                          $.ajax({
                              url: 'SafeBalanceFilter/'+countryId,
                              type:"GET",
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },
   
                              success:function(data) {
                                  $.each(data, function(key, value){
   
        
                    $('#SafeBalance').text(key); 
                    $('#SafeBalance').text(value); 
                                  });
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });
                      } else {
   
                          $('select[name="state"]').empty();
                      }
   
                  });
   
              });
</script>  

<!-- Submit Script -->
<script>

        function safe(){
        
            
    var sum = 0;        
$('.TCred').each(function(){
  sum += parseFloat($(this).text());
});   
       var safee=$('#Safe').val();
        
        
        if(safee != '' && sum != 0){
        document.getElementById("SHOW").style.display="block";
        }else{
            
           document.getElementById("SHOW").style.display="none";   
        } 
            
            
     
    }
    
        function SPP(){
           $('#sp').val(1);  
           var x= $('#sp').val();   
        
        if(x == 1){
              document.getElementById("SHOW").style.display = "none"; 
   document.getElementById('form').submit();   
        }
    }
    
    
       function SPS(){
           $('#sp').val(0);  
           var x= $('#sp').val();   
        
        if(x == 0){
              document.getElementById("SHOW").style.display = "none"; 
   document.getElementById('form').submit();   
        }
    }

    
 
</script>
  
   
    <!-- Search Selecet -->
 <script>
        $(document).ready(function()
        {
            $(function()
            {
                $('.select2').select2();

                $(".select2-placeholder-multiple").select2(
                {
                    placeholder: "Select State"
                });
                $(".js-hide-search").select2(
                {
                    minimumResultsForSearch: 1 / 0
                });
                $(".js-max-length").select2(
                {
                    maximumSelectionLength: 2,
                    placeholder: "Select maximum 2 items"
                });
                $(".select2-placeholder").select2(
                {
                    placeholder: "Select a state",
                    allowClear: true
                });

                $(".js-select2-icons").select2(
                {
                    minimumResultsForSearch: 1 / 0,
                    templateResult: icon,
                    templateSelection: icon,
                    escapeMarkup: function(elm)
                    {
                        return elm
                    }
                });

                function icon(elm)
                {
                    elm.element;
                    return elm.id ? "<i class='" + $(elm.element).data("icon") + " mr-2'></i>" + elm.text : elm.text
                }

 
       
                
                
  $('#Safe').select2({
    placeholder: "select...",
    ajax: {
        type: "GET",
        dataType: 'json',
        url: 'AllSafes',
        processResults: function (data) {
          return {
            results: $.map(data, function(obj, index) {
              return { id: index, text: obj };
            })
          };
            
            	console.log(data);
            
        },
       data: function (params) {  
   
            
                   var query = {
                            search: params.term,
                        };
                                
            
              $.ajax({
                              url: 'AllSafesJ/'+params.term,
                              type:"GET",
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },

                              success:function(data) {
                                          $('#Safe').empty();  
                                  $.each(data, function(key, value){
   
                         $('#Safe').append('<option value="'+ key +'">' + value + '</option>');
                   
                                  });
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });
            
            
            
        }
    }
  });

    
$('#Safe').on('select2:select', function (e) {
	console.log("select done", e.params.data);
});
                         
                
             
            });
        });



    </script>

<!-- Account Balance -->
<script>
   $(document).ready(function() {
   
                  $('#AccountCode').on('change', function(){
                      var countryId = $(this).val();
                      if(countryId) {
                          $.ajax({
                              url: 'AccountBalanceSOFilter/'+countryId,
                              type:"GET",
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },
   
                              success:function(data) {
                                  $.each(data, function(key, value){
   
        
                    $('#AccountCredit').val(key); 
                    $('#AccountCredit').val(value); 
                                  });
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });
                      } else {
   
                          $('select[name="state"]').empty();
                      }
   
                  });
   
              });
    
     $(document).ready(function() { 
 
                      var countryId = $('#AccountCode').val();
                      if(countryId) {
                          $.ajax({
                              url: 'AccountBalanceSFilter/'+countryId,
                              type:"GET",
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },
   
                              success:function(data) {
                                  $.each(data, function(key, value){
   
        
                    $('#AccountCredit').val(key); 
                    $('#AccountCredit').val(value); 
                                  });
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });
                      } else {
   
                          $('select[name="state"]').empty();
                      }
     
     });
</script>  


    
<?php $__env->stopPush(); ?>
<?php echo $__env->make('admin.index', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp2\htdocs\erp\resources\views/admin/Accounts/Receipt_Voucher.blade.php ENDPATH**/ ?>