<?php echo $__env->make('site.layouts.header', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php echo $__env->make('site.layouts.navbar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
   <?php echo $__env->yieldContent('content'); ?>

<?php echo $__env->make('site.layouts.footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<?php
use App\Models\MainEComDesign;
$main=MainEComDesign::orderBy('id','desc')->first();
?>

<style>
    .switch-box{
        
        display: none !important;
    }
</style>    
<?php if($main->Font_Type == 1): ?>
   <!-- google font cairo -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;800;1000&display=swap" rel="stylesheet">
    <style>
        body , h1,h2,h3,h4,h5,h6,div,span,strong,p,a,th,button {
            font-family: 'Cairo', sans-serif  !important;
        }
    </style>

<?php elseif($main->Font_Type == 2): ?>
     <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
        <link href="https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,700;1,400;1,700&family=Mada:wght@200;300;400;500;800;1000&family=Mada:wght@300;500;700&family=Mada:wght@200;500;800;900&display=swap" rel="stylesheet">
    
    <style>
        body  , h1,h2,h3,h4,h5,h6,div,span,strong,p,a,th,button {
            font-family: 'Mada', sans-serif  !important;
        }
    </style>

<?php elseif($main->Font_Type == 3): ?>
   <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,700;1,400;1,700&family=Alexandria:wght@200;300;400;500;800;1000&family=Alexandria:wght@300;500;700&family=Alexandria:wght@200;500;800;900&display=swap" rel="stylesheet">
    
    <style>
        body , h1,h2,h3,h4,h5,h6,div,span,strong,p,a,th,button {
            font-family: 'Alexandria', sans-serif  !important;
        }
    </style>

<?php elseif($main->Font_Type == 4): ?>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,700;1,400;1,700&family=Marhey:wght@200;300;400;500;800;1000&family=Marhey:wght@300;500;700&family=Marhey:wght@200;500;800;900&display=swap" rel="stylesheet">
    
    <style>
        body , h1,h2,h3,h4,h5,h6,div,span,strong,p,a,th,button {
            font-family: 'Marhey', sans-serif  !important;
        }
    </style>

<?php elseif($main->Font_Type == 5): ?>
     <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Reem+Kufi+Fun:ital,wght@0,700;1,400;1,700&family=Reem+Kufi+Fun:wght@200;300;400;500;800;1000&family=Reem+Kufi+Fun:wght@300;500;700&family=Reem+Kufi+Fun:wght@200;500;800;900&display=swap" rel="stylesheet">
    
    <style>
        body, h1,h2,h3,h4,h5,h6,div,span,strong,p,a,th,button {
            font-family: 'Reem Kufi Fun', sans-serif !important;
        }
    </style>

<?php endif; ?><?php /**PATH C:\xampp2\htdocs\erp\resources\views/site/index.blade.php ENDPATH**/ ?>