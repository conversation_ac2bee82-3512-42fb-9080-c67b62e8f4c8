<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAboutsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('abouts', function (Blueprint $table) {
            $table->id();
            
            // About page content
            $table->string('Arabic_Title')->nullable();
            $table->string('English_Title')->nullable();
            $table->text('Arabic_Content')->nullable();
            $table->text('English_Content')->nullable();
            $table->string('Image')->nullable();
            $table->string('Video_Link')->nullable();
            
            // Mission, Vision, Values
            $table->string('Arabic_Mission_Title')->nullable();
            $table->string('English_Mission_Title')->nullable();
            $table->text('Arabic_Mission_Content')->nullable();
            $table->text('English_Mission_Content')->nullable();
            
            $table->string('Arabic_Vision_Title')->nullable();
            $table->string('English_Vision_Title')->nullable();
            $table->text('Arabic_Vision_Content')->nullable();
            $table->text('English_Vision_Content')->nullable();
            
            $table->string('Arabic_Values_Title')->nullable();
            $table->string('English_Values_Title')->nullable();
            $table->text('Arabic_Values_Content')->nullable();
            $table->text('English_Values_Content')->nullable();
            
            // Company statistics
            $table->string('Years_Experience')->default('0')->nullable();
            $table->string('Happy_Customers')->default('0')->nullable();
            $table->string('Projects_Completed')->default('0')->nullable();
            $table->string('Team_Members')->default('0')->nullable();
            
            // SEO
            $table->text('Meta_Description')->nullable();
            $table->text('Meta_Keywords')->nullable();
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('abouts');
    }
}
