<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateChatIssuesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('chat_issues', function (Blueprint $table) {
            $table->id();
            
            // Chat message information
            $table->string('Name')->nullable();     // Name of the person sending message
            $table->date('Date')->nullable();       // Message date
            $table->string('Time')->nullable();     // Message time
            $table->text('Desc')->nullable();       // Message description/content
            $table->string('Image')->nullable();    // Attached image
            $table->string('Issue')->nullable();    // Reference to issues table
            $table->string('Type')->nullable();     // Message type (1=client, 2=support)
            $table->string('Appear')->nullable();   // Message visibility status
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('chat_issues');
    }
}
