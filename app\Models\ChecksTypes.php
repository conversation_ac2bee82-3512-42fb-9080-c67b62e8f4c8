<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Stancl\Tenancy\Database\Concerns\CentralConnection;

class ChecksTypes extends Model
{
    use HasFactory, CentralConnection;
      protected $table = 'checks_types';
      protected $fillable = [
        'Arabic_Name',
        'English_Name',

    ];

           public function IncomChecks()
    {
        return $this->hasOne(IncomChecks::class);
    }

            public function ExportChecks()
    {
        return $this->hasOne(ExportChecks::class);
    }

              public function Purchases()
    {
        return $this->hasOne(Purchases::class);
    }

              public function Sales()
    {
        return $this->hasOne(Sales::class);
    }



}
