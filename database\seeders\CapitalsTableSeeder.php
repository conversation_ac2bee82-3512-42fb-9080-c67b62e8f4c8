<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class CapitalsTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('capitals')->delete();
        
        \DB::table('capitals')->insert(array (
            0 => 
            array (
                'id' => 1,
                'Authorized_Capital' => '1',
                'Source_Capital' => NULL,
                'Shares_Number' => NULL,
                'Nominal_Value_of_Shares' => NULL,
                'Actual_Share_Value' => NULL,
                'Actual_Capital' => NULL,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            1 => 
            array (
                'id' => 2,
                'Authorized_Capital' => '100000',
                'Source_Capital' => '150000',
                'Shares_Number' => '10',
                'Nominal_Value_of_Shares' => '15000',
                'Actual_Share_Value' => '15000',
                'Actual_Capital' => '150000',
                'created_at' => '2021-10-13 15:36:23',
                'updated_at' => '2021-10-13 15:36:23',
            ),
        ));
        
        
    }
}