<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateJournalizingsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('journalizings', function (Blueprint $table) {
            $table->id();
            
            // Journal entry basic information
            $table->string('Code')->nullable();         // Journal entry code
            $table->string('Type')->nullable();         // Journal entry type (Arabic)
            $table->string('TypeEn')->nullable();       // Journal entry type (English)
            $table->string('Code_Type')->nullable();    // Type code
            $table->date('Date')->nullable();           // Entry date
            $table->string('Draw')->nullable();         // Drawing/document number
            
            // Financial information
            $table->string('Coin')->nullable();         // Currency reference
            $table->string('Cost_Center')->nullable();  // Cost center reference
            $table->decimal('Total_Debaitor', 15, 2)->nullable();  // Total debit amount
            $table->decimal('Total_Creditor', 15, 2)->nullable();  // Total credit amount
            
            // Additional information
            $table->text('Note')->nullable();           // Journal entry notes
            $table->string('Branch')->nullable();       // Branch reference
            $table->string('Status')->nullable();       // Entry status
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('journalizings');
    }
}
