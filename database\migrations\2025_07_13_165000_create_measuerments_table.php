<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMeasuermentsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('measuerments', function (Blueprint $table) {
            $table->id();
            
            // Measurement unit information
            $table->string('Name')->nullable();         // Unit name (Arabic)
            $table->string('NameEn')->nullable();       // Unit name (English)
            $table->text('Note')->nullable();           // Unit notes
            $table->string('Code')->nullable();         // Unit code
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('measuerments');
    }
}
