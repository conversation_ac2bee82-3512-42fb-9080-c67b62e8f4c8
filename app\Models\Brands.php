<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Stancl\Tenancy\Database\Concerns\CentralConnection;

class Brands extends Model
{
    use HasFactory, CentralConnection;

             protected $table = 'brands';
      protected $fillable = [
        'Name',
        'NameEn',
        'Image',
        'Note',
        'Sales_Show',
        'Store_Show',

    ];

          public function Products()
    {
        return $this->hasOne(Products::class);
    }



}
