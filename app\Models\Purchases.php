<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Stancl\Tenancy\Database\Concerns\CentralConnection;

class Purchases extends Model
{
    use HasFactory, CentralConnection;
      protected $table = 'purchases';
      protected $fillable = [
        'Code',
        'Date',
        'Draw',
        'Payment_Method',
        'Status',
        'ShipStatus',
        'Vendor_Bill_Date',
        'Refernce_Number',
        'Note',
        'Product_Numbers',
        'Total_Qty',
        'Total_Discount',
        'Total_BF_Taxes',
        'Total_Taxes',
        'Total_Price',
        'Pay',
        'P_Order_Num',
        'Safe',
        'Vendor',
        'Delegate',
        'Store',
        'Coin',
        'Cost_Center',
        'User',
          'Ship',
                 'Check_Type',
        'Due_Date',
        'Check_Number',
        'Later_Due',
                 'Sent',
             'TaxBill',
        'TaxCode',
                'Time',
        'Branch',
        'CustomerGroup',
          'File',
    ];

         public function Safe()
    {
        return $this->belongsTo(AcccountingManual::class,'Safe');
    }
          public function Vendor()
    {
        return $this->belongsTo(AcccountingManual::class,'Vendor');
    }
          public function Delegate()
    {
        return $this->belongsTo(Employess::class,'Delegate');
    }
          public function Store()
    {
        return $this->belongsTo(Stores::class,'Store');
    }
          public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }
          public function Cost_Center()
    {
        return $this->belongsTo(CostCenter::class,'Cost_Center');
    }
          public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }

              public function ProductsPurchases()
    {
        return $this->hasOne(ProductsPurchases::class);
    }

                  public function RecivedPurcht()
    {
        return $this->hasOne(RecivedPurcht::class);
    }

                             public function ReturnPurch()
    {
        return $this->hasOne(ReturnPurch::class);
    }

                  public function Ship()
    {
        return $this->belongsTo(AcccountingManual::class,'Ship');
    }

                     public function Check_Type()
    {
        return $this->belongsTo(ChecksTypes::class,'Check_Type');
    }

         public function Branch()
    {
        return $this->belongsTo(Branches::class,'Branch');
    }


           public function CustomerGroup()
    {
        return $this->belongsTo(CustomersGroup::class,'CustomerGroup');
    }

}
