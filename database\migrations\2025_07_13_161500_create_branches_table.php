<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateBranchesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('branches', function (Blueprint $table) {
            $table->id();
            
            // Branch information
            $table->string('Arabic_Name')->nullable();  // Arabic branch name
            $table->string('English_Name')->nullable(); // English branch name
            $table->string('Letter')->nullable();       // Branch letter code
            $table->string('Code')->nullable();         // Branch code
            $table->string('Location')->nullable();     // Branch location
            $table->string('Budget')->nullable();       // Branch budget reference
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('branches');
    }
}
