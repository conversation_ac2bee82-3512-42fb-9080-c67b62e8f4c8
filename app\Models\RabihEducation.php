<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Stancl\Tenancy\Database\Concerns\CentralConnection;

class RabihEducation extends Model
{
    use HasFactory, CentralConnection;
      protected $table = 'rabih_education';
      protected $fillable = [
        'Arabic_Name',
        'English_Name',
        'Video',
        'Package',
    ];

         public function Package()
    {
        return $this->belongsTo(Packages::class,'Package');
    }
    
    
}
