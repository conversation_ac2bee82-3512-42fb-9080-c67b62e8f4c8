<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Stancl\Tenancy\Database\Concerns\CentralConnection;

class AccountsDefaultData extends Model
{
    use HasFactory, CentralConnection;
            protected $table = 'accounts_default_data';
      protected $fillable = [
        'Draw',
        'Coin',
        'Sure_Recipts',
        'Show_Group',
        'Account_Balance',
        'Salary',
        'Commission',

    ];


      public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }


}
