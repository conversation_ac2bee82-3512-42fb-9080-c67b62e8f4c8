<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUsersMovesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('users_moves', function (Blueprint $table) {
            $table->id();
            $table->string('User')->nullable(); // References admin ID
            $table->string('Date')->nullable();
            $table->string('Time')->nullable();
            $table->string('Screen')->nullable(); // Arabic screen name
            $table->string('ScreenEn')->nullable(); // English screen name
            $table->string('Type')->nullable(); // Arabic action type
            $table->string('TypeEn')->nullable(); // English action type
            $table->string('Explain')->nullable(); // Arabic explanation
            $table->string('ExplainEn')->nullable(); // English explanation
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('users_moves');
    }
}
