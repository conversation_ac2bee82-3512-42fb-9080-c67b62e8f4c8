<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Stancl\Tenancy\Database\Concerns\CentralConnection;

class Intro extends Model
{
    use HasFactory, CentralConnection;
      protected $table = 'intros';
      protected $fillable = [
        'Arabic_About',
        'English_About',
        'Phone_1',
        'Phone_2',
        'Phone_3',
        'Phone_4',
        'Arabic_Terms',
        'English_Terms',

    ];
}
