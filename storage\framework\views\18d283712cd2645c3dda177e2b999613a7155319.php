<?php $__env->startSection('content'); ?>
<?php
use App\Models\ProductSalesOrder;
use App\Models\CompanyData;
use App\Models\ProductUnits;
use App\Models\DefaultDataShowHide;
$Def=CompanyData::orderBy('id','desc')->first();
$show=DefaultDataShowHide::orderBy('id','desc')->first();
?>
 <title><?php echo e(trans('admin.SalesOrder')); ?></title>
               
     <main id="js-page-content" role="main" class="page-content">
                    <ol class="breadcrumb page-breadcrumb">
                        <li class="breadcrumb-item"><a href="javascript:void(0);"><?php echo e(trans('admin.Sales')); ?></a></li>
                        <li class="breadcrumb-item active"> <?php echo e(trans('admin.SalesOrder')); ?> </li>
                        <li class="position-absolute pos-top pos-right d-none d-sm-block">
                        <span class="js-get-date"></span></li>
                    </ol>
                    
                    <!-- data entry -->
          
                    <div class="row hide-table">
                        <div class="col-xl-12">
                            <div id="panel-1" class="panel">
                                 <div class="modal-body">
                                    <div data-size="A4">
                                       <div class="row invoive-info">
                                            <div style="text-align: center;" class="col-md-4 col-sm-4 col-4 invoice-client-info">
                                                <div class="style-info">
                                                    <h1 style="text-align: center;" class="m-0">
                                               <?php if(!empty($Def->Name)): ?>
                          <?php echo e(app()->getLocale() == 'ar' ?$Def->Name :$Def->NameEn); ?>

                      <?php else: ?>
                       <?php echo e(trans('admin.Ost')); ?>

                      <?php endif; ?>         
                                                    </h1>
                              <h3 style="text-align: center;" class="m-10">
                         <?php if(!empty($Def->Print_Text)): ?>
                    <?php echo e(app()->getLocale() == 'ar' ?$Def->Print_Text :$Def->Print_Text_En); ?>   
                      <?php endif; ?>                
                                                    </h3>
                                                </div>
                                            </div>
                                            <div style="text-align: center;" class="col-md-4 col-sm-4 col-4 invoice-client-info">
                      <?php if(!empty($Def->Name_Sales_Order_Bill)): ?>
                 <h1>    <?php echo e(app()->getLocale() == 'ar' ?$Def->Name_Sales_Order_Bill :$Def->Name_Sales_Order_Bill_En); ?>     </h1>
                      <?php else: ?>
              <h1><?php echo e(trans('admin.SalesOrder')); ?></h1>
                      <?php endif; ?>              
                                            </div>
                                   <div style="text-align: center;" class="col-md-4 col-sm-4 col-4 invoice-client-info">
                         <div class="style-info">
                       <?php if(!empty($Def->Logo)): ?>         
    <img style="height: 50px; width: 150px;" class="img-fluid" src="<?php echo e(URL::to($Def->Logo)); ?>" alt="Logo" />
                                       
                    <?php else: ?>  
 <img style="height: 50px; width: 150px;" class="img-fluid" src="https://Osterp.com/site/img/theme/logo.png" alt="Logo" />
                      <?php endif; ?>
               
                                       </div>
                                            </div>
                                        </div>
                                        <hr />
                                        <div class="row"></div>
                                      
                                        <div class="row">
                                            <div class="col-md-3 col-3">
                                                <h5> <?php echo e(trans('admin.Code')); ?> : <span class="style-data">
                                            <?php echo e($item->Code); ?>        
                                                </span></h5>	
                                            </div>
                                            <div class="col-md-3 col-3">
                               <h5> <?php echo e(trans('admin.Date')); ?>  : <span  class="style-data"><?php echo e($item->Date); ?></span></h5>
                                            </div>
                                            <div class="col-md-3 col-3">
                                                <h5> <?php echo e(trans('admin.Coin')); ?>  : <span  class="style-data">
                                                <?php echo e(app()->getLocale() == 'ar' ?$item->Coin()->first()->Arabic_Name :$item->Coin()->first()->English_Name); ?>             
                                                    </span></h5>
                                            </div>
                                            <div class="col-md-3 col-3 no-print">
                                                <h5> <?php echo e(trans('admin.Draw')); ?>   : <span  class="style-data"> <?php echo e($item->Draw); ?>    </span></h5>
                                            </div>
                                            
                                                   <div class="col-md-3 col-3">
                                                <h5> <?php echo e(trans('admin.Payment_Method')); ?>   : <span  class="style-data">       <?php if($item->Payment_Method == 'Cash'): ?>
                                                       <?php echo e(trans('admin.Cash')); ?> 
                                                    <?php elseif($item->Payment_Method == 'Later'): ?>
                                                       <?php echo e(trans('admin.Later')); ?> 
                                                         <?php elseif($item->Payment_Method == 'Installment'): ?>
                                                       <?php echo e(trans('admin.Installment')); ?> 
                                                    <?php endif; ?>      </span></h5>
                                            </div>
                                            
                                      <?php if($item->Payment_Method == 'Installment'): ?>        
                                  <div class="col-md-3 col-3">
                                      
        <h5> <?php echo e(trans('admin.Presenter')); ?>   : 
            
            
            <span  class="style-data">     
            
            
                 <?php echo e($item->presenter); ?>

            
            
            </span>
                                      </h5>
                                            </div>
                                 <div class="col-md-3 col-3">
                                      
        <h5> <?php echo e(trans('admin.annual_interest')); ?>   : 
            
            
            <span  class="style-data">     
            
            
                 <?php echo e($item->annual_interest); ?>

            
            
            </span>
                                      </h5>
                                            </div>
                                             <div class="col-md-3 col-3">
                                      
        <h5> <?php echo e(trans('admin.monthly_installment')); ?>   : 
            
            
            <span  class="style-data">     
            
            
                 <?php echo e($item->monthly_installment); ?>

            
            
            </span>
                                      </h5>
                                            </div>
                                             <div class="col-md-3 col-3">
                                      
        <h5> <?php echo e(trans('admin.Years_Number')); ?>   : 
            
            
            <span  class="style-data">     
            
            
                 <?php echo e($item->Years_Number); ?>

            
            
            </span>
                                      </h5>
                                            </div>
                                             <div class="col-md-3 col-3">
                                      
        <h5> <?php echo e(trans('admin.total')); ?>   : 
            
            
            <span  class="style-data">     
            
            
                 <?php echo e($item->total); ?>

            
            
            </span>
                                      </h5>
                                            </div>
                                             <div class="col-md-3 col-3">
                                      
        <h5> <?php echo e(trans('admin.installment_Num')); ?>   : 
            
            
            <span  class="style-data">     
            
            
                 <?php echo e($item->installment_Num); ?>

            
            
            </span>
                                      </h5>
                                            </div>
                                             <div class="col-md-3 col-3">
                                      
        <h5> <?php echo e(trans('admin.Date_First_installment')); ?>   : 
            
            
            <span  class="style-data">     
            
            
                 <?php echo e($item->Date_First_installment); ?>

            
            
            </span>
                                      </h5>
                                            </div>
                                            <?php endif; ?>

                                                   <div class="col-md-3 col-3">
                                                <h5> <?php echo e(trans('admin.Status')); ?>   : <span  class="style-data">          <?php if($item->Status == 1): ?>
                                                       <?php echo e(trans('admin.Recived')); ?> 
                                                    <?php elseif($item->Status == 0): ?>
                                                       <?php echo e(trans('admin.Pending')); ?> 
                                                    <?php endif; ?>     </span></h5>
                                            </div>
                                            
                                                   <div class="col-md-3 col-3 no-print">
                                                <h5> <?php echo e(trans('admin.Executor')); ?>   : <span  class="style-data"> 
                                                 <?php if(!empty($item->Executor)): ?>      
                                                     <?php echo e(app()->getLocale() == 'ar' ?$item->Executor()->first()->Name :$item->Executor()->first()->NameEn); ?>     
                                                 <?php endif; ?>   
                                                    </span></h5>
                                            </div>
                                            
                                                   <div class="col-md-3 col-3 no-print">
                                                <h5> <?php echo e(trans('admin.Refernce_Number')); ?>   : <span  class="style-data"> <?php echo e($item->Refernce_Number); ?>    </span></h5>
                                            </div>
                                            
                                                   <div class="col-md-3 col-3 no-print">
                                                <h5> <?php echo e(trans('admin.Safe')); ?>   : <span  class="style-data">       <?php echo e(app()->getLocale() == 'ar' ?$item->Safe()->first()->Name :$item->Safe()->first()->NameEn); ?>       </span></h5>
                                            </div>
                                            
                                                   <div class="col-md-3 col-3">
                                                <h5> <?php echo e(trans('admin.Client')); ?>   : <span  class="style-data">             <?php echo e(app()->getLocale() == 'ar' ?$item->Client()->first()->Name :$item->Client()->first()->NameEn); ?>          </span></h5>
                                            </div>
                                            
                                            
                                                   <div class="col-md-3 col-3 no-print">
                                                <h5> <?php echo e(trans('admin.Delegate')); ?>   : <span  class="style-data"> 
                                                     <?php if(!empty($item->Delegate)): ?>  
                                                       <?php echo e(app()->getLocale() == 'ar' ?$item->Delegate()->first()->Name :$item->Delegate()->first()->NameEn); ?>       
                                                    <?php endif; ?>
                                                    </span></h5>
                                            </div>
                                            
                                                   <div class="col-md-3 col-3 no-print">
                                                <h5> <?php echo e(trans('admin.Store')); ?>   : <span  class="style-data">     <?php echo e(app()->getLocale() == 'ar' ?$item->Store()->first()->Name :$item->Store()->first()->NameEn); ?>       </span></h5>
                                            </div>
                                            
                                                   <div class="col-md-3 col-3 no-print">
                                                <h5> <?php echo e(trans('admin.User')); ?>   : <span  class="style-data">    <?php echo e(app()->getLocale() == 'ar' ?$item->User()->first()->name :$item->User()->first()->nameEn); ?>        </span></h5>
                                            </div>
      	
                                        </div>
                                        <div class="row">
                                            <div class="col-md-3 col-3 no-print">
                                     <h5> <?php echo e(trans('admin.Cost_Center')); ?> : <span class="style-data">
                                         <?php if(!empty($item->Cost_Center)): ?>        
                                                 <?php echo e(app()->getLocale() == 'ar' ?$item->Cost_Center()->first()->Arabic_Name :$item->Cost_Center()->first()->English_Name); ?>       
                                                    <?php endif; ?>
                                         </span></h5>	
                                            </div>
                                            <div class="col-md-6 col-6">
                                                <h5> <?php echo e(trans('admin.Notes')); ?>   : <span  class="style-data">
                                                <?php echo e($item->Note); ?>    
                                                    </span></h5>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12">
                                                <div class="table-responsive-lg">
                                                    <table class="table table-bordered mt-5">
                                                        <thead>
                                                            <tr>
                                                                   <?php if($show->Barcode_Print == 1): ?>
                                                                <th   class="border-top-0 table-scale-border-bottom fw-700"> <?php echo e(trans('admin.Code')); ?></th>
                                                                <?php endif; ?>
                                                                <th   class="border-top-0 table-scale-border-bottom fw-700"> <?php echo e(trans('admin.Name')); ?></th>
                
                                                                <?php if($show->Unit_Print == 1): ?>
                                                                <th class="border-top-0 table-scale-border-bottom fw-700">  <?php echo e(trans('admin.Unit')); ?></th>
                                                                <?php endif; ?>
                                                                
                                                                <th class="border-top-0 table-scale-border-bottom fw-700">  <?php echo e(trans('admin.Qty')); ?></th>
                                                                <th class="border-top-0 table-scale-border-bottom fw-700"> <?php echo e(trans('admin.Price')); ?></th>
                                                                
                                                                      <?php if($show->Discount_Print == 1): ?>
                                                                   <th   class="border-top-0 table-scale-border-bottom fw-700"> <?php echo e(trans('admin.Discount')); ?></th>
                                                                <?php endif; ?>
                                                                  <?php if($show->Total_BF_Print == 1): ?>
                                                                <th  class="border-top-0 table-scale-border-bottom fw-700 no-print"> <?php echo e(trans('admin.Total_Bf_Tax')); ?></th>
                                                                <?php endif; ?>
                                                                 <?php if($show->Tax_Print == 1): ?>
                                                                <th class="border-top-0 table-scale-border-bottom fw-700">  <?php echo e(trans('admin.Total_Tax')); ?></th>
                                                                <?php endif; ?>
                                                                <th class="border-top-0 table-scale-border-bottom fw-700">  <?php echo e(trans('admin.Total')); ?></th>
                                                                <th class="border-top-0 table-scale-border-bottom fw-700 no-print"> <?php echo e(trans('admin.Store')); ?></th>
                                                                
                                                            </tr>
                                                        </thead>
                
                 <?php
                $details=ProductSalesOrder::orderBy('id','asc')->where('SalesOrder',$item->id)->get();                             
                ?>  
          
                        <tbody>
                                                    <?php $__currentLoopData = $details; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $detail): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>       
                                                        <?php if($detail->Product()->first()->P_Type != 'Serial'): ?>            
                                                            <tr>
              <?php if($show->Barcode_Print == 1): ?>    <td><?php echo e($detail->Product_Code); ?></td> <?php endif; ?>
                                    <td>
                                                               <?php echo e(app()->getLocale() == 'ar' ?$detail->Product()->first()->P_Ar_Name :$detail->Product()->first()->P_En_Name); ?>            
                                    
     <?php if(!empty($detail->V1)): ?>   (<?php echo e(app()->getLocale() == 'ar' ?$detail->V1()->first()->Name :$detail->V1()->first()->NameEn); ?>  )  <?php endif; ?> 
                                  <?php if(!empty($detail->V2)): ?>    ((<?php echo e(app()->getLocale() == 'ar' ?$detail->V2()->first()->Name :$detail->V2()->first()->NameEn); ?>  ))  <?php endif; ?>        
                                    </td>
                                                                
                               <?php
     $Raty=ProductUnits::where('Product',$detail->Product)->where('Unit',$detail->Unit)->first();                         
                ?>                                        
          <?php if($show->Unit_Print == 1): ?>    <td>
                                                           <?php echo e(app()->getLocale() == 'ar' ?$detail->Unit()->first()->Name :$detail->Unit()->first()->NameEn); ?>

                                                                
                                                                (<?php echo e($Raty->Rate); ?>)
                                                                </td> <?php endif; ?>
                                    <td><?php echo e($detail->Qty); ?></td>
                                    <td><?php echo e($detail->Price); ?></td>
             <?php if($show->Discount_Print == 1): ?>           <td><?php echo e($detail->Discount); ?></td>  <?php endif; ?>
                <?php if($show->Total_BF_Print == 1): ?>        <td class="no-print"><?php echo e($detail->Total_Bf_Tax); ?></td>  <?php endif; ?>
                <?php if($show->Tax_Print == 1): ?>        <td><?php echo e($detail->Total_Tax); ?></td>  <?php endif; ?>
                                    <td><?php echo e($detail->Total); ?></td>
                                    <td class="no-print"><?php echo e(app()->getLocale() == 'ar' ?$detail->Store()->first()->Name :$detail->Store()->first()->NameEn); ?></td>
                 
                                                            </tr>
                                                        <?php endif; ?>    
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>                  
                                
                   
                             <?php
                $details=ProductSalesOrder::where('SalesOrder',$item->id)->select('Product')->distinct()->get();                             
                ?>                                      
                
                                                            
                                                                               <?php $__currentLoopData = $details; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $detail): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>   
                                                           <?php if($detail->Product()->first()->P_Type == 'Serial'): ?>             
                                                   <?php
    $d=ProductSalesOrder::where('SalesOrder',$item->id)->where('Product',$detail->Product)->orderBy('id','desc')->first();     
                                   
                $details=ProductSalesOrder::where('SalesOrder',$item->id)->where('Product',$detail->Product)->get();             
                            $qqqty=0;                                
                        $Disscc=0;                                    
                        $BFTAX=0;                                    
                        $TOTTAX=0;                                    
                        $TOTAAAAL=0;           
                ?>     
                                                               
          
                                                  <?php $__currentLoopData = $details; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $detail): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>   
                                                           <?php if($detail->Product()->first()->P_Type == 'Serial'): ?> 
                                                           <?php $qqqty +=$detail->Qty;  ?>
                                                           <?php $Disscc +=$detail->Discount; ?>
                                                           <?php $BFTAX +=$detail->Total_Bf_Tax; ?>
                                                           <?php $TOTTAX +=$detail->Total_Tax; ?>
                                                           <?php $TOTAAAAL +=$detail->Total; ?>
                                                            
                                                             <?php endif; ?>
                                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>  
                                          
                                                         
                                                            <tr>
                                  <?php if($show->Barcode_Print == 1): ?>  
                                             <td>
                                         
                                                 <div class="row"> 
                                                        <?php $__currentLoopData = $details; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $detail): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>   
                                                           <?php if($detail->Product()->first()->P_Type == 'Serial'): ?>  
                                                    <div class="col-md-12"> 
                                               <?php echo e($detail->Product_Code); ?> 
                                                     </div> 
                                                           <?php $qqqty +=$detail->Qty;  ?>
                                                           <?php $Disscc +=$detail->Discount; ?>
                                                           <?php $BFTAX +=$detail->Total_Bf_Tax; ?>
                                                           <?php $TOTTAX +=$detail->Total_Tax; ?>
                                                           <?php $TOTAAAAL +=$detail->Total; ?>
                                                 <?php endif; ?>                             
                                      <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>  
                                        </div>       
                                                 
                                                </td>
                                                                   
                            <?php else: ?>                                    
                                      <td style="display: none">
                                         
                                                 <div class="row"> 
                                                        <?php $__currentLoopData = $details; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $detail): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>   
                                                           <?php if($detail->Product()->first()->P_Type == 'Serial'): ?>  
                                                    <div class="col-md-12"> 
                                               <?php echo e($detail->Product_Code); ?> 
                                                     </div> 
                                                           <?php $qqqty +=$detail->Qty;  ?>
                                                           <?php $Disscc +=$detail->Discount; ?>
                                                           <?php $BFTAX +=$detail->Total_Bf_Tax; ?>
                                                           <?php $TOTTAX +=$detail->Total_Tax; ?>
                                                           <?php $TOTAAAAL +=$detail->Total; ?>
                                                 <?php endif; ?>                             
                                      <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>  
                                        </div>       
                                                 
                                                </td>                               
                                            <?php endif; ?> 
                                                              <?php echo e(app()->getLocale() == 'ar' ?$d->Product()->first()->P_Ar_Name :$d->Product()->first()->P_En_Name); ?>            
                                    
     <?php if(!empty($d->V1)): ?>   (<?php echo e(app()->getLocale() == 'ar' ?$d->V1()->first()->Name :$d->V1()->first()->NameEn); ?>  )  <?php endif; ?> 
                                  <?php if(!empty($d->V2)): ?>    ((<?php echo e(app()->getLocale() == 'ar' ?$d->V2()->first()->Name :$d->V2()->first()->NameEn); ?>  ))  <?php endif; ?>       
                                    </td>
                                                                
                               <?php
     $Raty=ProductUnits::where('Product',$d->Product)->where('Unit',$d->Unit)->first();                         
                ?>                                        
          <?php if($show->Unit_Print == 1): ?>   
                                                <td>
                                                                         <?php echo e(app()->getLocale() == 'ar' ?$d->Unit()->first()->Name :$d->Unit()->first()->NameEn); ?>

                                                                
                                                                (<?php echo e($Raty->Rate); ?>)
                                            </td>
                                <?php endif; ?>
                                    <td><?php echo e($qqqty); ?></td>
                        
                                                             <td><?php echo e($d->Price); ?></td>            
                                                                
                            
             <?php if($show->Discount_Print == 1): ?>           <td><?php echo e($Disscc); ?></td>  <?php endif; ?>
                <?php if($show->Total_BF_Print == 1): ?>        <td class="no-print"><?php echo e($BFTAX); ?></td>  <?php endif; ?>
                <?php if($show->Tax_Print == 1): ?>        <td><?php echo e($TOTTAX); ?></td>  <?php endif; ?>
                                    <td><?php echo e($TOTAAAAL); ?></td>
                                    <td class="no-print"><?php echo e(app()->getLocale() == 'ar' ?$d->Store()->first()->Name :$d->Store()->first()->NameEn); ?></td>
                                   
                                                            </tr>
                                                        
                                             
                                                     
                                           
                                   <?php endif; ?>                             
                                      <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>     
                                                            
                                                      
                 
                                                        </tbody>
                                                   
                                                    </table>
                            <table class="table table-bordered mt-5">
                      
                                                        <tbody>               
                                                             <tr>
                                                          
                                                    <!--             
                                                             <td><?php echo e(trans('admin.Product_Numbers')); ?></td>
                                    <td><?php echo e($item->Product_Numbers); ?></td>
                                    
                                    <td><?php echo e(trans('admin.Total_Qty')); ?></td>
                                    <td><?php echo e($item->Total_Qty); ?></td>
                                    
                                    <td><?php echo e(trans('admin.Total_Discount')); ?></td>
                                    <td><?php echo e($item->Total_Discount); ?></td>
                                    
                                     <td class="no-print"><?php echo e(trans('admin.Total_Bf_Taxes')); ?></td>
                                    <td class="no-print"><?php echo e($item->Total_BF_Taxes); ?></td>
                                    
                                    <td><?php echo e(trans('admin.Total_Taxes')); ?></td>
                                    <td><?php echo e($item->Total_Taxes); ?></td>
                                    -->
                                    <td><?php echo e(trans('admin.Total_Price')); ?></td>
                                    <td><?php echo e($item->Total_Price); ?></td>
                                    
                                    <td><?php echo e(trans('admin.Paid')); ?></td>
                                    <td><?php echo e($item->Pay); ?></td> 
                            
                                  <td><?php echo e(trans('admin.Residual')); ?></td>
                                    <td><?php echo e(round($item->Total_Price) - $item->Pay); ?></td> 
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                      <div class="row">
                            
             <?php if(app()->getLocale() == 'ar' ): ?>                                                             
       <?php
           $f = new NumberFormatter("ar", NumberFormatter::SPELLOUT);
echo $f->format(round($item->Total_Price));
       ?>       
            <?php else: ?>
            <?php
           $f = new NumberFormatter("en", NumberFormatter::SPELLOUT);
echo $f->format(round($item->Total_Price));
       ?>                                                                       
            <?php endif; ?>       
                                        </div>
                                        <div class="row">
                                                 <div  class="col-md-6 col-sm-6 col-6 invoice-client-info text-center">
                                                    <p class="mt-2 text-muted mb-0">
                                        <?php echo e(trans('admin.Credit')); ?> :  <span id="AccountCredit"></span>
                                                        <input type="hidden" id="Credit" value="<?php echo e($item->Client); ?>">  
                                                    </p>
                                                </div>
                                                <div class="col-md-6 col-sm-6 col-6 invoice-client-info text-center" >
                                                    
                                       <?php
                                    $x=$item->Code;
                                    $y=DNS1D::getBarcodePNG($x, 'C39');                                            
                                    ?>   
   <img src="data:image/png;base64,<?php echo e($y); ?>" id="barcode"  class="height-3 mt-1" />                    
                                                    
                                                </div>
                                               
                                          
                                        </div>
                                    </div>
                                </div>
                                                   <div class="row">
                                       
                                             <?php if(!empty($Def->Print_Text_Footer_Sales)): ?>
         
            <?php echo app()->getLocale() == 'ar' ?$Def->Print_Text_Footer_Sales :$Def->Print_Text_Footer_Sales_En; ?>

                      <?php endif; ?>          
                                        </div>    
                                                      <div class="row">
                                       
                                             <?php if(!empty($Def->Seal)): ?>

                   <img src="<?php echo e(URL::to($Def->Seal)); ?>" class="img-fluid"  />             
                      <?php endif; ?>          
                                                         
                                                         
                                        </div>  
                                <div class="modal-footer">
                         <a href="<?php echo e(url('OstAdmin')); ?>" class="btn btn-secondary" data-dismiss="modal"><?php echo e(trans('admin.Back')); ?></a>
             <button type="button" class="btn btn-default" onclick="window.print()"><i class="fal fa-print"></i></button>  
                                </div>
                                
                            </div>
                        </div>
         </div>
</main>

       


<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/datagrid/datatables/datatables.bundle.css')); ?>">
    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/formplugins/summernote/summernote.css')); ?>">
    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/formplugins/select2/select2.bundle.css')); ?>">


    <style>
        th{
            width:135px!important;
        }
    </style>

 <script src="<?php echo e(asset('Admin/js/datagrid/datatables/datatables.export.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/datagrid/datatables/datatables.bundle.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/formplugins/summernote/summernote.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/formplugins/select2/select2.bundle.js')); ?>"></script>
    <script>
        //_fnFeatureHtmlLength();
        $(document).ready(function () {
            // Setup - add a text input to each footer cell
            $('#dt-basic-example thead tr').clone(true).appendTo('#dt-basic-example thead');
            $('#dt-basic-example thead tr:eq(1) th').each(function (i) {
                var title = $(this).text();
                $(this).html('<input type="text" class="form-control form-control-sm" placeholder="Search ' + title + '" />');

                $('input', this).on('keyup change', function () {
                    if (table.column(i).search() !== this.value) {
                        table
                            .column(i)
                            .search(this.value)
                            .draw();
                    }
                });
            });
            var table = $('#dt-basic-example').DataTable(
                {
                    responsive: true,
                    orderCellsTop: true,
                    fixedHeader: true,
                    lengthChange: true,

                    dom: "<'row mb-3'<'col-sm-12 col-md-3 d-flex align-items-center justify-content-start'f><'col-sm-12 col-md-9 d-flex align-items-center justify-content-end'B>>" +
                        "<'row'<'col-sm-12'tr>>" +
                        "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",

                    buttons: [
                        {
                            extend: 'pageLength',
                            className: 'btn-outline-default'
                        },
                        {
                            extend: 'colvis',
                            text: 'Column Visibility',
                            titleAttr: 'Col visibility',
                            className: 'btn-outline-default'
                        },
                        {
                            extend: 'pdfHtml5',
                            text: 'PDF',
                            titleAttr: 'Generate PDF',
                            className: 'btn-outline-danger btn-sm mr-1'
                        },
                        {
                            extend: 'excelHtml5',
                            text: 'Excel',
                            titleAttr: 'Generate Excel',
                            className: 'btn-outline-success btn-sm mr-1'
                        },
                        {
                            extend: 'csvHtml5',
                            text: 'CSV',
                            titleAttr: 'Generate CSV',
                            className: 'btn-outline-primary btn-sm mr-1'
                        },
                        {
                            extend: 'copyHtml5',
                            text: 'Copy',
                            titleAttr: 'Copy to clipboard',
                            className: 'btn-outline-primary btn-sm mr-1'
                        },
                        {
                            extend: 'print',
                            text: 'Print',
                            titleAttr: 'Print Table',
                            className: 'btn-outline-primary btn-sm'
                        }
                    ],
                });
            $('.js-thead-colors a').on('click', function () {
                var theadColor = $(this).attr("data-bg");
                console.log(theadColor);
                $('#dt-basic-example thead').removeClassPrefix('bg-').addClass(theadColor);
            });

            $('.js-tbody-colors a').on('click', function () {
                var theadColor = $(this).attr("data-bg");
                console.log(theadColor);
                $('#dt-basic-example').removeClassPrefix('bg-').addClass(theadColor);
            });

        });

    </script>
  
  <script>
    $(document).ready(function()
    {
        $(function()
        {
            $('.select2').select2();

            $(".select2-placeholder-multiple").select2(
            {
                placeholder: "Select State"
            });
            $(".js-hide-search").select2(
            {
                minimumResultsForSearch: 1 / 0
            });
            $(".js-max-length").select2(
            {
                maximumSelectionLength: 2,
                placeholder: "Select maximum 2 items"
            });
            $(".select2-placeholder").select2(
            {
                placeholder: "Select a state",
                allowClear: true
            });

            $(".js-select2-icons").select2(
            {
                minimumResultsForSearch: 1 / 0,
                templateResult: icon,
                templateSelection: icon,
                escapeMarkup: function(elm)
                {
                    return elm
                }
            });

            function icon(elm)
            {
                elm.element;
                return elm.id ? "<i class='" + $(elm.element).data("icon") + " mr-2'></i>" + elm.text : elm.text
            }

            $(".js-data-example-ajax").select2(
            {
                ajax:
                {
                    url: "https://api.github.com/search/repositories",
                    dataType: 'json',
                    delay: 250,
                    data: function(params)
                    {
                        return {
                            q: params.term, // search term
                            page: params.page
                        };
                    },
                    processResults: function(data, params)
                    {
                        // parse the results into the format expected by Select2
                        // since we are using custom formatting functions we do not need to
                        // alter the remote JSON data, except to indicate that infinite
                        // scrolling can be used
                        params.page = params.page || 1;

                        return {
                            results: data.items,
                            pagination:
                            {
                                more: (params.page * 30) < data.total_count
                            }
                        };
                    },
                    cache: true
                },
                placeholder: 'Search for a repository',
                escapeMarkup: function(markup)
                {
                    return markup;
                }, // let our custom formatter work
                minimumInputLength: 1,
                templateResult: formatRepo,
                templateSelection: formatRepoSelection
            });

            function formatRepo(repo)
            {
                if (repo.loading)
                {
                    return repo.text;
                }

                var markup = "<div class='select2-result-repository clearfix d-flex'>" +
                    "<div class='select2-result-repository__avatar mr-2'><img src='" + repo.owner.avatar_url + "' class='width-2 height-2 mt-1 rounded' /></div>" +
                    "<div class='select2-result-repository__meta'>" +
                    "<div class='select2-result-repository__title fs-lg fw-500'>" + repo.full_name + "</div>";

                if (repo.description)
                {
                    markup += "<div class='select2-result-repository__description fs-xs opacity-80 mb-1'>" + repo.description + "</div>";
                }

                markup += "<div class='select2-result-repository__statistics d-flex fs-sm'>" +
                    "<div class='select2-result-repository__forks mr-2'><i class='fal fa-lightbulb'></i> " + repo.forks_count + " Forks</div>" +
                    "<div class='select2-result-repository__stargazers mr-2'><i class='fal fa-star'></i> " + repo.stargazers_count + " Stars</div>" +
                    "<div class='select2-result-repository__watchers mr-2'><i class='fal fa-eye'></i> " + repo.watchers_count + " Watchers</div>" +
                    "</div>" +
                    "</div></div>";

                return markup;
            }

            function formatRepoSelection(repo)
            {
                return repo.full_name || repo.text;
            }
        });
    });

</script>
<script>
    var autoSave = $('#autoSave');
    var interval;
    var timer = function()
    {
        interval = setInterval(function()
        {
            //start slide...
            if (autoSave.prop('checked'))
                saveToLocal();

            clearInterval(interval);
        }, 3000);
    };

    //save
    var saveToLocal = function()
    {
        localStorage.setItem('summernoteData', $('#saveToLocal').summernote("code"));
        console.log("saved");
    }

    //delete 
    var removeFromLocal = function()
    {
        localStorage.removeItem("summernoteData");
        $('#saveToLocal').summernote('reset');
    }

    $(document).ready(function()
    {
        //init default
        $('.js-summernote').summernote(
        {
            height: 200,
            tabsize: 2,
            placeholder: "Type here...",
            dialogsFade: true,
            toolbar: [
                ['style', ['style']],
                ['font', ['strikethrough', 'superscript', 'subscript']],
                ['font', ['bold', 'italic', 'underline', 'clear']],
                ['fontsize', ['fontsize']],
                ['fontname', ['fontname']],
                ['color', ['color']],
                ['para', ['ul', 'ol', 'paragraph']],
                ['height', ['height']]
                ['table', ['table']],
                ['insert', ['link', 'picture', 'video']],
                ['view', ['fullscreen', 'codeview', 'help']]
            ],
            callbacks:
            {
                //restore from localStorage
                onInit: function(e)
                {
                    $('.js-summernote').summernote("code", localStorage.getItem("summernoteData"));
                },
                onChange: function(contents, $editable)
                {
                    clearInterval(interval);
                    timer();
                }
            }
        });

        //load emojis
        $.ajax(
        {
            url: 'https://api.github.com/emojis',
            async: false
        }).then(function(data)
        {
            window.emojis = Object.keys(data);
            window.emojiUrls = data;
        });

        //init emoji example
        $(".js-hint2emoji").summernote(
        {
            height: 100,
            toolbar: false,
            placeholder: 'type starting with : and any alphabet',
            hint:
            {
                match: /:([\-+\w]+)$/,
                search: function(keyword, callback)
                {
                    callback($.grep(emojis, function(item)
                    {
                        return item.indexOf(keyword) === 0;
                    }));
                },
                template: function(item)
                {
                    var content = emojiUrls[item];
                    return '<img src="' + content + '" width="20" /> :' + item + ':';
                },
                content: function(item)
                {
                    var url = emojiUrls[item];
                    if (url)
                    {
                        return $('<img />').attr('src', url).css('width', 20)[0];
                    }
                    return '';
                }
            }
        });

        //init mentions example
        $(".js-hint2mention").summernote(
        {
            height: 100,
            toolbar: false,
            placeholder: "type starting with @",
            hint:
            {
                mentions: ['jayden', 'sam', 'alvin', 'david'],
                match: /\B@(\w*)$/,
                search: function(keyword, callback)
                {
                    callback($.grep(this.mentions, function(item)
                    {
                        return item.indexOf(keyword) == 0;
                    }));
                },
                content: function(item)
                {
                    return '@' + item;
                }
            }
        });

    });

</script>
<script type="text/javascript">


    $(".show-table").click(function(){
        $(".hide-table").show();
    });
    
</script>

	<style>
	@media  print {
		body * {
			visibility: hidden;
		}
		.modal-body * {
			visibility: visible;
			overflow: visible;
		}
		
        

	}
	</style>

<!-- Account Balance -->
<script>
   $(document).ready(function() {

                      var countryId = $('#Credit').val();
                      if(countryId) {
                          $.ajax({
                              url: 'AccountBalanceFilter/'+countryId,
                              type:"GET",
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },
   
                              success:function(data) {
                                  $.each(data, function(key, value){
        
                    $('#AccountCredit').text(parseFloat(key).toFixed(2)); 
                    $('#AccountCredit').text(parseFloat(value).toFixed(2)); 
                                      
                                      
                                      
                                  });
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });
                      } else {
   
                          $('select[name="state"]').empty();
                      }

   
              });
</script>  

<?php $__env->stopPush(); ?>
<?php echo $__env->make('admin.index', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp2\htdocs\erp\resources\views/admin/Sales/SalesOrderPrint.blade.php ENDPATH**/ ?>