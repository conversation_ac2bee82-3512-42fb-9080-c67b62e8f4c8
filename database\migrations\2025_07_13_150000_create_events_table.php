<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateEventsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('events', function (Blueprint $table) {
            $table->id();
            
            // Event date information
            $table->date('Start_Date')->nullable();
            $table->date('End_Date')->nullable();
            
            // Event names
            $table->string('Event_Ar_Name')->nullable();
            $table->string('Event_En_Name')->nullable();
            
            // Event type and classification
            $table->string('Type')->nullable();
            $table->string('Type_ID')->nullable();
            $table->string('Type_Code')->nullable();
            
            // References to related entities
            $table->string('Emp')->nullable();      // Employee reference
            $table->string('Client')->nullable();   // Client reference
            $table->string('Product')->nullable();  // Product reference
            $table->string('Customer')->nullable(); // Customer reference
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('events');
    }
}
