<?php $__env->startSection('content'); ?>
<?php
use App\Models\ProductUnits;
use App\Models\Countris;
use App\Models\Wishlist;
use App\Models\Compare;
use App\Models\ItemsGroups;
use App\Models\Coins;
    if(empty(session()->get('ChangeCountryy'))) {
    
         $Coin=Coins::where('Draw',1)->first();
    $Country=Countris::where('Coin',$Coin->id)->first();
    session()->put('ChangeCountryy',$Country->id);  
      }
$Ses=Countris::find(session()->get('ChangeCountryy'));

use App\Models\MainEComDesign;
use App\Models\SupPagesPartTwoEComDesign;
$main=MainEComDesign::orderBy('id','desc')->first();
$sub=SupPagesPartTwoEComDesign::orderBy('id','desc')->first();
?>


 <style>
.page-title-area {
    background-color: <?php echo e($main->Breadcumb_BG_Color); ?> !important;
}
     
    .shop-area {
      background-color: <?php echo e($main->Sub_Page_BG_Color); ?> !important;
} 
     
     
     .single-shop-products {
         background: <?php echo e($sub->Shop_Product_BG_Color); ?> !important;
     }
     
     
     .single-shop-products .shop-products-image .tag {

    background: <?php echo e($sub->Shop_Product_Group_BG_Color); ?> !important;
    color:<?php echo e($sub->Shop_Product_Group_Txt_Color); ?> !important;
         
     }
     
          .single-shop-products .shop-products-image .tag:hover {

    background: <?php echo e($sub->Shop_Product_Group_Hover_BG_Color); ?> !important;
    color:<?php echo e($sub->Shop_Product_Group_Hover_Txt_Color); ?> !important;
         
     }
     
     
     .single-shop-products .shop-products-image .shop-action li a i {

    background: <?php echo e($sub->Shop_Product_Icon_BG_Color); ?> !important;
    color:<?php echo e($sub->Shop_Product_Icon_Txt_Color); ?> !important;
         
     }
     
         .single-shop-products .shop-products-image .shop-action li a i:hover {

    background: <?php echo e($sub->Shop_Product_Icon_Hover_BG_Color); ?> !important;
    color:<?php echo e($sub->Shop_Product_Icon_Hover_Txt_Color); ?> !important;
         
     }
     
     
         .single-shop-products .shop-products-image .shop-action li i {

    color:<?php echo e($sub->Shop_Product_Icon_Txt_Color); ?> !important;
         
     }
     
     .single-shop-products .shop-products-content h3 a{
           color:<?php echo e($sub->Shop_Product_Txt_Color); ?> !important;
         
     }
     
     .single-shop-products .shop-products-content .rating li i {
         
             color:<?php echo e($sub->Shop_Product_Rate_Color); ?> !important;
     }
     
     .single-shop-products .shop-products-content span{
         
           color:<?php echo e($sub->Shop_Product_Price_Color); ?> !important;
     }
     
          .single-shop-products .shop-products-content span del{
         
           color:<?php echo e($sub->Shop_Product_Price_Color); ?> !important;
     }
     
     .widget-area .widget_search form .search-field {
    background-color: <?php echo e($sub->Shop_Filter_Search_BG_Color); ?> !important;
    border: 1px solid <?php echo e($sub->Shop_Filter_Title_Color); ?> !important;
     }
     
     .widget-area .widget_search form button {

    background-color: <?php echo e($sub->Shop_Filter_Search_Icon_BG_Color); ?> !important;
    color: <?php echo e($sub->Shop_Filter_Search_Icon_Txt_Color); ?> !important;
     }
     
          .widget-area .widget_search form button:hover {

    background-color: <?php echo e($sub->Shop_Filter_Search_Icon_BG_Hover_Color); ?> !important;
    color: <?php echo e($sub->Shop_Filter_Search_Icon_Txt_Hover_Color); ?> !important;
     }
     
     .widget-area .widget .widget-title {

    color: <?php echo e($sub->Shop_Filter_Title_Color); ?> !important;
    border-bottom: 1px solid <?php echo e($sub->Shop_Filter_Title_Color); ?> !important;
     }
     
     .widget-area .widget .widget-title::before {

    background: <?php echo e($sub->Shop_Filter_Title_Color); ?> !important;
     }
     
     .widget-area .widget_categories .categories li a {
    color: <?php echo e($sub->Shop_Filter_Txt_Color); ?> !important;
    border: 1px solid <?php echo e($sub->Shop_Filter_Txt_Color); ?> !important;
     }
     
     .widget-area .widget_best-seller-products .item .info .title a{
         
           color:<?php echo e($sub->Shop_Product_Txt_Color); ?> !important; 
     }
     
         .widget-area .widget_best-seller-products .item .info .title a:hover{
         
           color:<?php echo e($sub->Shop_Product_Txt_Color); ?> !important; 
     }
     
     .widget-area .widget_best-seller-products .item .info span{
         
       
    color:<?php echo e($sub->Shop_Product_Price_Color); ?> !important;
     }
     
          
     .widget-area .widget_best-seller-products .item .info span del{
         
       
    color:<?php echo e($sub->Shop_Product_Price_Color); ?> !important;
     }
     
     .widget-area .widget_best-seller-products .item .info .rating li i{
       color:<?php echo e($sub->Shop_Product_Rate_Color); ?> !important;   
     }
     
         
     .widget-area .widget_categories .categories li a::before {
    background-color: <?php echo e($sub->Shop_Product_Group_BG_Color); ?> !important;  
     }
</style>






    <title><?php echo e(trans('admin.Shop')); ?></title>



        <!-- Start Page Banner -->
        <div class="page-title-area">
            <div class="container">
                <div class="page-title-content">
                    <h2 style="color: <?php echo e($main->Breadcumb_Txt_Color); ?> !important"><?php echo e(trans('admin.Shop')); ?></h2>

                    <ul>
                        <li><a style="color: <?php echo e($main->Breadcumb_Txt_Color); ?> !important" href="<?php echo e(url('/')); ?>"><?php echo e(trans('admin.Home')); ?></a></li>
                        <li style="color: <?php echo e($main->Breadcumb_Txt_Color); ?> !important"><?php echo e(trans('admin.Shop')); ?></li>
                    </ul>
                </div>
            </div>
        </div>
        <!-- End Page Banner -->

        <!-- Start Shop Area -->
        <section class="shop-area bg-ffffff pt-50 pb-50">
            <div class="container">
                <div class="row">
                    <div class="col-lg-4 col-md-12">
                        <aside class="widget-area">
                            <div class="widget widget_search">
                                <form class="search-form" action="<?php echo e(url('ShopFilterName')); ?>" method="get">
                                    <label>
                                        <span class="screen-reader-text">Search for:</span>
                                        <input type="search" name="search" class="search-field" placeholder="<?php echo e(trans('admin.Search_For_Products')); ?>">
                                    </label>
                                    <button type="submit">
                                        <i class='bx bx-search-alt'></i>
                                    </button>
                                </form>
                            </div>
                    
                            <div class="widget widget_categories">
                                <h3 class="widget-title"><?php echo e(trans('admin.Brands')); ?></h3>
      

  
                                <ul class="categories">
                                    

     
                                        <?php $__currentLoopData = $Brands; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $brand): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li>
                                        <a href="<?php echo e(url('ShopFilterBrand/'.$brand->id)); ?>" class="nav-link">
                                            <i class="bx bx-certification"></i>
                                      <?php echo e(app()->getLocale() == 'ar' ?$brand->Name :$brand->NameEn); ?> 
                                        </a>
                                    </li>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                
                                </ul>
                            </div>
                            
                            
                                   <div class="widget widget_categories">
                                <h3 class="widget-title"><?php echo e(trans('admin.Categories')); ?></h3>
    
                                <ul class="categories">
                                                  <?php $__currentLoopData = $Groups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $group): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li>
                                        <a href="<?php echo e(url('FilterShopCat/'.$group->id)); ?>" class="nav-link">
                                            <i class="bx bx-certification"></i>
                                        <?php echo e(app()->getLocale() == 'ar' ?$group->Name :$group->NameEn); ?>  
                                        </a>
                                    </li>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                
                                </ul>
                            </div>
                            
                            
           

                            <div class="widget widget_best-seller-products">
                                <h3 class="widget-title"><?php echo e(trans('admin.New_Arrivals')); ?></h3>

                                <?php $__currentLoopData = $ProductsNew; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pro): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <article class="item">
                                  

                                    <a href="<?php echo e(url('ProductDetails/'.$pro->id)); ?>" class="thumb">
                                        <span class="fullimage cover bg1" role="img" style="background-image: url(<?php echo e(URL::to($pro->Image)); ?>)"></span>
                                    </a>
                                    <div class="info">
                                        <h4 class="title usmall">
                                            <a href="<?php echo e(url('ProductDetails/'.$pro->id)); ?>"> <?php echo e(app()->getLocale() == 'ar' ?$pro->P_Ar_Name :$pro->P_En_Name); ?>  </a>
                                        </h4>
                                                   <span>
                                
     <?php  $Price=ProductUnits::where('Product',$pro->id)->where('Def',1)->first();   ?>  
     <?php if($pro->Offer == 1): ?>
<del><?php echo e($Ses->Coin()->first()->Symbol); ?><?php echo e(round($Price->Price / $Ses->Coin()->first()->Draw)); ?></del>
<?php echo e($Ses->Coin()->first()->Symbol); ?><?php echo e(round($pro->OfferPrice / $Ses->Coin()->first()->Draw)); ?>

        <?php else: ?>
<?php echo e($Ses->Coin()->first()->Symbol); ?><?php echo e(round($Price->Price / $Ses->Coin()->first()->Draw)); ?>

        <?php endif; ?>     
                                
                                
                                </span>
                                        <ul class="rating">
                                                <?php if(empty($pro->rate)): ?>
                                                          <li><i class='bx bx-star'></i></li>
                                    <li><i class='bx bx-star'></i></li>
                                    <li><i class='bx bx-star'></i></li>
                                    <li><i class='bx bx-star'></i></li>
                                    <li><i class='bx bx-star'></i></li>
                                            <?php elseif($pro->rate == 1): ?> 
                                                          <li><i class='bx bxs-star'></i></li>
                                    <li><i class='bx bx-star'></i></li>
                                    <li><i class='bx bx-star'></i></li>
                                    <li><i class='bx bx-star'></i></li>
                                    <li><i class='bx bx-star'></i></li>
                                            <?php elseif($pro->rate == 2): ?>  
                                                 <li><i class='bx bxs-star'></i></li>
                                    <li><i class='bx bxs-star'></i></li>
                                    <li><i class='bx bx-star'></i></li>
                                    <li><i class='bx bx-star'></i></li>
                                    <li><i class='bx bx-star'></i></li>
                                            <?php elseif($pro->rate == 3): ?> 
                                                           <li><i class='bx bxs-star'></i></li>
                                    <li><i class='bx bxs-star'></i></li>
                                    <li><i class='bx bxs-star'></i></li>
                                    <li><i class='bx bx-star'></i></li>
                                    <li><i class='bx bx-star'></i></li>
                                            <?php elseif($pro->rate == 4): ?>  
                                                               <li><i class='bx bxs-star'></i></li>
                                    <li><i class='bx bxs-star'></i></li>
                                    <li><i class='bx bxs-star'></i></li>
                                    <li><i class='bx bxs-star'></i></li>
                                    <li><i class='bx bx-star'></i></li>
                                            <?php elseif($pro->rate == 5): ?>
                                                             <li><i class='bx bxs-star'></i></li>
                                    <li><i class='bx bxs-star'></i></li>
                                    <li><i class='bx bxs-star'></i></li>
                                    <li><i class='bx bxs-star'></i></li>
                                    <li><i class='bx bxs-star'></i></li>
                                            <?php endif; ?>  
                                        </ul>
                                    </div>
                                </article>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                          
                            </div>

                         
                        </aside>
                    </div>
                    
                    <div class="col-lg-8 col-md-12">
                        <div class="products-filter-options">
                 
        
                        <div id="products-collections-filter" class="row">
                            
                               <?php $__currentLoopData = $Products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pro): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="col-lg-4 col-sm-6">
                                <div class="single-shop-products">
                                    <div class="shop-products-image">
                                        <a href="<?php echo e(url('ProductDetails/'.$pro->id)); ?>"><img src="<?php echo e(URL::to($pro->Image)); ?>" alt="image"></a>
                                        <div class="tag">             <?php echo e(app()->getLocale() == 'ar' ?$pro->Group()->first()->Name :$pro->Group()->first()->NameEn); ?>  </div>
                                        <ul class="shop-action">
                                      <li>
                                        <a href="<?php echo e(url('ProductDetails/'.$pro->id)); ?>">
                                            <i class="flaticon-shopping-cart"></i>
                                        </a>
                                    </li>
                                    
                                   <?php if(!empty(auth()->guard('client')->user()->id)): ?>            
                                    <?php            
                                       $Wish=Wishlist::where('Product',$pro->id)
                                            ->where('User',auth()->guard('client')->user()->id)->first(); 
                                       
                                                       $Comp=Compare::where('Product',$pro->id)
                                            ->where('User',auth()->guard('client')->user()->id)->first(); 
                                                
                                    ?>
          
                                    <li>
                                        
                                        <?php if(empty($Wish)): ?>     
                                        <a href="<?php echo e(url('AddWish/'.$pro->id)); ?>"><i class="flaticon-heart"></i></a>
                                             <?php else: ?>   
                                       
                                        <i class="bx bxs-heart"></i>
                                            <?php endif; ?> 
                                    </li>
                                    <li>
                                       <?php if(empty($Comp)): ?>      
                                        <a href="<?php echo e(url('AddCompare/'.$pro->id)); ?>"><i class="bx bx-git-compare"></i></a>
                                        <?php else: ?>
                                        
                                        <i class="bx bx-git-compare">  <i class="bx bx-badge-check"></i></i>
                                      
                                          <?php endif; ?>  
                                    </li>
                          <?php endif; ?>   
                                        </ul>
                                    </div>
        
                                    <div class="shop-products-content">
                                        <h3>
                                        <a href="<?php echo e(url('ProductDetails/'.$pro->id)); ?>">   <?php echo e(app()->getLocale() == 'ar' ?$pro->P_Ar_Name :$pro->P_En_Name); ?>     </a>
                                        </h3>
                                        <ul class="rating">
                                      <?php if(empty($pro->rate)): ?>
                                                          <li><i class='bx bx-star'></i></li>
                                    <li><i class='bx bx-star'></i></li>
                                    <li><i class='bx bx-star'></i></li>
                                    <li><i class='bx bx-star'></i></li>
                                    <li><i class='bx bx-star'></i></li>
                                            <?php elseif($pro->rate == 1): ?> 
                                                          <li><i class='bx bxs-star'></i></li>
                                    <li><i class='bx bx-star'></i></li>
                                    <li><i class='bx bx-star'></i></li>
                                    <li><i class='bx bx-star'></i></li>
                                    <li><i class='bx bx-star'></i></li>
                                            <?php elseif($pro->rate == 2): ?>  
                                                 <li><i class='bx bxs-star'></i></li>
                                    <li><i class='bx bxs-star'></i></li>
                                    <li><i class='bx bx-star'></i></li>
                                    <li><i class='bx bx-star'></i></li>
                                    <li><i class='bx bx-star'></i></li>
                                            <?php elseif($pro->rate == 3): ?> 
                                                           <li><i class='bx bxs-star'></i></li>
                                    <li><i class='bx bxs-star'></i></li>
                                    <li><i class='bx bxs-star'></i></li>
                                    <li><i class='bx bx-star'></i></li>
                                    <li><i class='bx bx-star'></i></li>
                                            <?php elseif($pro->rate == 4): ?>  
                                                               <li><i class='bx bxs-star'></i></li>
                                    <li><i class='bx bxs-star'></i></li>
                                    <li><i class='bx bxs-star'></i></li>
                                    <li><i class='bx bxs-star'></i></li>
                                    <li><i class='bx bx-star'></i></li>
                                            <?php elseif($pro->rate == 5): ?>
                                                             <li><i class='bx bxs-star'></i></li>
                                    <li><i class='bx bxs-star'></i></li>
                                    <li><i class='bx bxs-star'></i></li>
                                    <li><i class='bx bxs-star'></i></li>
                                    <li><i class='bx bxs-star'></i></li>
                                            <?php endif; ?>  
               
                                        </ul>
                                                                <span>
                                
     <?php  $Price=ProductUnits::where('Product',$pro->id)->where('Def',1)->first();   ?>  
     <?php if($pro->Offer == 1): ?>
<del><?php echo e($Ses->Coin()->first()->Symbol); ?><?php echo e(round($Price->Price / $Ses->Coin()->first()->Draw)); ?></del>
<?php echo e($Ses->Coin()->first()->Symbol); ?><?php echo e(round($pro->OfferPrice / $Ses->Coin()->first()->Draw)); ?>

        <?php else: ?>
<?php echo e($Ses->Coin()->first()->Symbol); ?><?php echo e(round($Price->Price / $Ses->Coin()->first()->Draw)); ?>

        <?php endif; ?>     
                                
                                
                                </span>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    
                           
                            
        
                            <div class="col-lg-12 col-md-12">
                                <div class="pagination-area">
                                <?php echo e($Products->Links()); ?>

                              
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
        </section>
        <!-- End Shop Area -->
<!-- Pagination -->
<style> 
.page-item disabled,
.page-link,
.page-item active,
.page-item
    {
    		    background: <?php echo e($main->Pagination_BG_Color); ?> !important;
   color: <?php echo e($main->Pagination_Txt_Color); ?> !important;    
    }

    .page-item.active .page-link{
          		    background: <?php echo e($main->Pagination_Active_BG_Color); ?> !important;
   color: <?php echo e($main->Pagination_Active_Txt_Color); ?> !important;    
border-color:<?php echo e($main->Pagination_Active_Txt_Color); ?> !important;
        
    }

</style>

<?php $__env->stopSection(); ?>
<?php echo $__env->make('site.index', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp2\htdocs\erp\resources\views/site/Shop.blade.php ENDPATH**/ ?>