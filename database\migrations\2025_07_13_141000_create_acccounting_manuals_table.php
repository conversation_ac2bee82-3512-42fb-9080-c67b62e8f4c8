<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAcccountingManualsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('acccounting_manuals', function (Blueprint $table) {
            $table->id();
            
            // Basic account information
            $table->string('Code')->nullable();
            $table->string('Name')->nullable();
            $table->string('NameEn')->nullable();
            $table->string('Type')->nullable();
            $table->string('Parent')->nullable();
            $table->text('Note')->nullable();
            $table->string('User')->nullable();
            $table->string('Account_Code')->nullable();
            $table->string('Pro_Group')->nullable();
            $table->string('SearchCode')->nullable();
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('acccounting_manuals');
    }
}
