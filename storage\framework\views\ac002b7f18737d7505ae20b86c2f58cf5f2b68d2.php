<?php $__env->startSection('content'); ?>
<?php
use App\Models\ProductsPurchases;
use App\Models\AcccountingManual;
use App\Models\Employess;
use App\Models\GeneralDaily;
use App\Models\Stores;
use App\Models\Coins;
use App\Models\CostCenter;
use App\Models\Admin;
use App\Models\CompanyData;
$Def=CompanyData::orderBy('id','desc')->first();
?>
  <title><?php echo e(trans('admin.PurchasesSechdule')); ?></title>


     <main id="js-page-content" role="main" class="page-content">
                    <ol class="breadcrumb page-breadcrumb">
                        <li class="breadcrumb-item"><a href="javascript:void(0);"><?php echo e(trans('admin.Purchases')); ?></a></li>
                        <li class="breadcrumb-item active"> <?php echo e(trans('admin.PurchasesSechdule')); ?> </li>
                        <li class="position-absolute pos-top pos-right d-none d-sm-block"><span
                                class="js-get-date"></span></li>
                    </ol>
                    
                     <!-- Filter -->
                    <div class="row">
                        <div class="col-lg-12">
                            <div id="panel-2" class="panel first-color">
                                <div class="panel-hdr">
                                    <h2>
                                        <span class="fw-300"><i> <?php echo e(trans('admin.PurchasesSechdule')); ?>  </i></span>
                                    </h2>
                                </div>
                                <div class="panel-container show">
                                    <div class="panel-content">
                                        <form action="<?php echo e(url('FilterBillPurchases')); ?>" method="get" class="form-row">
                                            <div class="form-group col-md-2">
                                                <label class="form-label" for="simpleinput"><?php echo e(trans('admin.From')); ?></label>
                                                <input type="date" value="<?php echo e(date('Y-m-d')); ?>" name="From" class="form-control">
                                            </div>
                                            <div class="form-group col-md-2">
                                                <label class="form-label" for="simpleinput"><?php echo e(trans('admin.To')); ?></label>
                                                <input type="date" value="<?php echo e(date('Y-m-d')); ?>" name="To" class="form-control">
                                            </div>
                                            <div class="form-group col-md-2">
                                                <label class="form-label" for="">  <?php echo e(trans('admin.Store')); ?> </label>
                                                <select class="select2 form-control w-100" name="Store">
                                            <option value=""> <?php echo e(trans('admin.Store')); ?></option>
                                            <?php $__currentLoopData = $Stores; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $stor): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
                                                <option value="<?php echo e($stor->id); ?>">
                                              <?php echo e(app()->getLocale() == 'ar' ?$stor->Name :$stor->NameEn); ?>   
                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>   
                                                </select>
                                            </div>
                                            <div class="form-group col-md-2">
                                                <label class="form-label" for="">  <?php echo e(trans('admin.Account')); ?> </label>
                                                <select class="select2 form-control w-100" name="Vendor">
                                          <option value=""> <?php echo e(trans('admin.Account')); ?></option>            
                                           <?php $__currentLoopData = $Vendors; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $vend): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
                                                <option value="<?php echo e($vend->id); ?>">
                                                     
                                     <?php echo e(app()->getLocale() == 'ar' ?$vend->Name :$vend->NameEn); ?>    
                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>  
                                                </select>
                                            </div>
                                            <div class="form-group col-md-2">
                                                <label class="form-label" for="">  <?php echo e(trans('admin.Coin')); ?> </label>
                                                <select class="select2 form-control w-100" name="Coin">
                     <option value=""> <?php echo e(trans('admin.Coin')); ?></option>
                                            <?php $__currentLoopData = $Coins; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $coin): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
                                                <option value="<?php echo e($coin->id); ?>">
                                              <?php echo e(app()->getLocale() == 'ar' ?$coin->Arabic_Name :$coin->English_Name); ?>    
                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>  
                                                </select>
                                            </div>
                                            <div class="form-group col-md-2">
                                                <label class="form-label" for="">  <?php echo e(trans('admin.Safe')); ?> </label>
                                                <select class="select2 form-control w-100" name="Safe">
                                             <option value=""> <?php echo e(trans('admin.Safe')); ?></option>
                                            <?php $__currentLoopData = $Safes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $safe): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
                                                <option value="<?php echo e($safe->id); ?>">
                                               <?php echo e(app()->getLocale() == 'ar' ?$safe->Name :$safe->NameEn); ?>   
                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>   
                                                </select>
                                            </div>
                                            <div class="form-group col-md-2">
                                                <label class="form-label" for="">   <?php echo e(trans('admin.Payment_Method')); ?> </label>
                                                <select class="select2 form-control w-100" name="Payment_Method">
                                                   <option value=""> <?php echo e(trans('admin.Account')); ?></option>     
                                                 <option value="Cash"><?php echo e(trans('admin.Cash')); ?> </option>
                                                <option value="Later"><?php echo e(trans('admin.Later')); ?></option>
                                                </select>
                                            </div>
                                            <div class="form-group col-md-2">
                                                <label class="form-label" for="">   <?php echo e(trans('admin.Cost_Center')); ?> </label>
                                                <select class="select2 form-control w-100" name="Cost_Center">
                                 
                                                <option value=""> <?php echo e(trans('admin.Cost_Center')); ?></option>     
                                            <?php $__currentLoopData = $CostCenters; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cost): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
                                                <option value="<?php echo e($cost->id); ?>">
                                       <?php echo e(app()->getLocale() == 'ar' ?$cost->Arabic_Name :$cost->English_Name); ?>  
                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>              
                                                </select>
                                            </div>
                                            <div class="form-group col-md-2">
                                                <label class="form-label" for="">  <?php echo e(trans('admin.Delegate')); ?>  </label>
                                                <select class="select2 form-control w-100" name="Delegate">
                                                    <option value=""> <?php echo e(trans('admin.Delegate')); ?></option>      
                                                 <?php $__currentLoopData = $Employess; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $emp): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
                                                <option value="<?php echo e($emp->id); ?>">
                                            <?php echo e(app()->getLocale() == 'ar' ?$emp->Name :$emp->NameEn); ?>   
                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>  
                                                </select>
                                            </div>
                                          <div class="form-group col-md-2">
                                                <label class="form-label" for="">  <?php echo e(trans('admin.User')); ?>  </label>
                                                <select class="select2 form-control w-100" name="User">
                                                    <option value=""> <?php echo e(trans('admin.User')); ?></option>      
                                                 <?php $__currentLoopData = $Users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
                                                <option value="<?php echo e($user->id); ?>">
                                                   <?php echo e(app()->getLocale() == 'ar' ?$user->Name :$user->NameEn); ?>     
                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>  
                                                </select>
                                            </div>    
                                            <div class="form-group col-lg-2">
                                      <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Code')); ?> </label>
                                                <input type="text" name="Code" class="form-control">
                                            </div>
        <div class="form-group col-lg-2">
                                      <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Refernce_Number')); ?> </label>
                                                <input type="text" name="Refernce_Number" class="form-control">
                                            </div>
                                            <div class="form-group col-md-2">
                                                <div class="buttons mt-4">
    <button type="submit" class="btn btn-primary show-table w-100"> <i class="fal fa-folder"></i> <?php echo e(trans('admin.Show')); ?></button>
                                                </div>
                                            </div>

                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
            <!-- Data -->
                    <div class="row hide-table">
                        <div class="col-xl-12">
                            <div id="panel-1" class="panel second-color">
                                <div class="panel-hdr">
                                    <h2>
                                    <?php echo e(trans('admin.PurchasesSechdule')); ?>

                                    </h2>
                                  
                                    <div class="panel-toolbar">
                                        <button class="btn btn-primary btn-sm" data-toggle="dropdown">Table
                                            Style</button>
                                        <div
                                            class="dropdown-menu dropdown-menu-animated dropdown-menu-right position-absolute pos-top">
                                            <button class="dropdown-item active" data-action="toggle"
                                                data-class="table-bordered" data-target="#dt-basic-example"> Bordered
                                                Table </button>
                                            <button class="dropdown-item" data-action="toggle" data-class="table-sm"
                                                data-target="#dt-basic-example"> Smaller Table </button>
                                            <button class="dropdown-item" data-action="toggle" data-class="table-dark"
                                                data-target="#dt-basic-example"> Table Dark </button>
                                            <button class="dropdown-item active" data-action="toggle"
                                                data-class="table-hover" data-target="#dt-basic-example"> Table Hover
                                            </button>
                                            <button class="dropdown-item active" data-action="toggle"
                                                data-class="table-stripe" data-target="#dt-basic-example"> Table
                                                Stripped </button>
                                            <div class="dropdown-divider m-0"></div>
                                            <div class="dropdown-multilevel dropdown-multilevel-left">
                                                <div class="dropdown-item">
                                                    tbody color
                                                </div>
                                                <div class="dropdown-menu no-transition-delay">
                                                    <div class="js-tbody-colors dropdown-multilevel dropdown-multilevel-left d-flex flex-wrap"
                                                        style="width: 15.9rem; padding: 0.5rem">
                                                        <a href="javascript:void(0);" data-bg="bg-primary-100"
                                                            class="btn d-inline-block bg-primary-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-200"
                                                            class="btn d-inline-block bg-primary-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-300"
                                                            class="btn d-inline-block bg-primary-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-400"
                                                            class="btn d-inline-block bg-primary-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-500"
                                                            class="btn d-inline-block bg-primary-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-600"
                                                            class="btn d-inline-block bg-primary-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-700"
                                                            class="btn d-inline-block bg-primary-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-800"
                                                            class="btn d-inline-block bg-primary-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-900"
                                                            class="btn d-inline-block bg-primary-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-100"
                                                            class="btn d-inline-block bg-success-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-200"
                                                            class="btn d-inline-block bg-success-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-300"
                                                            class="btn d-inline-block bg-success-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-400"
                                                            class="btn d-inline-block bg-success-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-500"
                                                            class="btn d-inline-block bg-success-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-600"
                                                            class="btn d-inline-block bg-success-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-700"
                                                            class="btn d-inline-block bg-success-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-800"
                                                            class="btn d-inline-block bg-success-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-900"
                                                            class="btn d-inline-block bg-success-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-100"
                                                            class="btn d-inline-block bg-info-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-200"
                                                            class="btn d-inline-block bg-info-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-300"
                                                            class="btn d-inline-block bg-info-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-400"
                                                            class="btn d-inline-block bg-info-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-500"
                                                            class="btn d-inline-block bg-info-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-600"
                                                            class="btn d-inline-block bg-info-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-700"
                                                            class="btn d-inline-block bg-info-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-800"
                                                            class="btn d-inline-block bg-info-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-900"
                                                            class="btn d-inline-block bg-info-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-100"
                                                            class="btn d-inline-block bg-danger-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-200"
                                                            class="btn d-inline-block bg-danger-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-300"
                                                            class="btn d-inline-block bg-danger-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-400"
                                                            class="btn d-inline-block bg-danger-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-500"
                                                            class="btn d-inline-block bg-danger-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-600"
                                                            class="btn d-inline-block bg-danger-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-700"
                                                            class="btn d-inline-block bg-danger-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-800"
                                                            class="btn d-inline-block bg-danger-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-900"
                                                            class="btn d-inline-block bg-danger-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-100"
                                                            class="btn d-inline-block bg-warning-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-200"
                                                            class="btn d-inline-block bg-warning-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-300"
                                                            class="btn d-inline-block bg-warning-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-400"
                                                            class="btn d-inline-block bg-warning-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-500"
                                                            class="btn d-inline-block bg-warning-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-600"
                                                            class="btn d-inline-block bg-warning-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-700"
                                                            class="btn d-inline-block bg-warning-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-800"
                                                            class="btn d-inline-block bg-warning-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-900"
                                                            class="btn d-inline-block bg-warning-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-100"
                                                            class="btn d-inline-block bg-fusion-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-200"
                                                            class="btn d-inline-block bg-fusion-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-300"
                                                            class="btn d-inline-block bg-fusion-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-400"
                                                            class="btn d-inline-block bg-fusion-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-500"
                                                            class="btn d-inline-block bg-fusion-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-600"
                                                            class="btn d-inline-block bg-fusion-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-700"
                                                            class="btn d-inline-block bg-fusion-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-800"
                                                            class="btn d-inline-block bg-fusion-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-900"
                                                            class="btn d-inline-block bg-fusion-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg=""
                                                            class="btn d-inline-block bg-white border width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="dropdown-multilevel dropdown-multilevel-left">
                                                <div class="dropdown-item">
                                                    thead color
                                                </div>
                                                <div class="dropdown-menu no-transition-delay">
                                                    <div class="js-thead-colors dropdown-multilevel dropdown-multilevel-left d-flex flex-wrap"
                                                        style="width: 15.9rem; padding: 0.5rem">
                                                        <a href="javascript:void(0);" data-bg="bg-primary-100"
                                                            class="btn d-inline-block bg-primary-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-200"
                                                            class="btn d-inline-block bg-primary-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-300"
                                                            class="btn d-inline-block bg-primary-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-400"
                                                            class="btn d-inline-block bg-primary-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-500"
                                                            class="btn d-inline-block bg-primary-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-600"
                                                            class="btn d-inline-block bg-primary-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-700"
                                                            class="btn d-inline-block bg-primary-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-800"
                                                            class="btn d-inline-block bg-primary-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-900"
                                                            class="btn d-inline-block bg-primary-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-100"
                                                            class="btn d-inline-block bg-success-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-200"
                                                            class="btn d-inline-block bg-success-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-300"
                                                            class="btn d-inline-block bg-success-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-400"
                                                            class="btn d-inline-block bg-success-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-500"
                                                            class="btn d-inline-block bg-success-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-600"
                                                            class="btn d-inline-block bg-success-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-700"
                                                            class="btn d-inline-block bg-success-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-800"
                                                            class="btn d-inline-block bg-success-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-900"
                                                            class="btn d-inline-block bg-success-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-100"
                                                            class="btn d-inline-block bg-info-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-200"
                                                            class="btn d-inline-block bg-info-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-300"
                                                            class="btn d-inline-block bg-info-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-400"
                                                            class="btn d-inline-block bg-info-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-500"
                                                            class="btn d-inline-block bg-info-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-600"
                                                            class="btn d-inline-block bg-info-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-700"
                                                            class="btn d-inline-block bg-info-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-800"
                                                            class="btn d-inline-block bg-info-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-900"
                                                            class="btn d-inline-block bg-info-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-100"
                                                            class="btn d-inline-block bg-danger-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-200"
                                                            class="btn d-inline-block bg-danger-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-300"
                                                            class="btn d-inline-block bg-danger-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-400"
                                                            class="btn d-inline-block bg-danger-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-500"
                                                            class="btn d-inline-block bg-danger-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-600"
                                                            class="btn d-inline-block bg-danger-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-700"
                                                            class="btn d-inline-block bg-danger-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-800"
                                                            class="btn d-inline-block bg-danger-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-900"
                                                            class="btn d-inline-block bg-danger-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-100"
                                                            class="btn d-inline-block bg-warning-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-200"
                                                            class="btn d-inline-block bg-warning-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-300"
                                                            class="btn d-inline-block bg-warning-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-400"
                                                            class="btn d-inline-block bg-warning-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-500"
                                                            class="btn d-inline-block bg-warning-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-600"
                                                            class="btn d-inline-block bg-warning-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-700"
                                                            class="btn d-inline-block bg-warning-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-800"
                                                            class="btn d-inline-block bg-warning-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-900"
                                                            class="btn d-inline-block bg-warning-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-100"
                                                            class="btn d-inline-block bg-fusion-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-200"
                                                            class="btn d-inline-block bg-fusion-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-300"
                                                            class="btn d-inline-block bg-fusion-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-400"
                                                            class="btn d-inline-block bg-fusion-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-500"
                                                            class="btn d-inline-block bg-fusion-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-600"
                                                            class="btn d-inline-block bg-fusion-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-700"
                                                            class="btn d-inline-block bg-fusion-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-800"
                                                            class="btn d-inline-block bg-fusion-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-900"
                                                            class="btn d-inline-block bg-fusion-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg=""
                                                            class="btn d-inline-block bg-white border width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                                <div class="panel-container show">
                              <span id="ex"> <?php echo $__env->make('admin.layouts.messages', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>  
                                    <div class="panel-content">
                                        
                                        <!-- datatable start -->
                                        <div style="overflow:auto;">
        <table id="dt-basic-example" class="table table-bordered table-hover table-striped ">
                                            <thead class="bg-highlight">
                                                <tr>
                                                    <th><?php echo e(trans('admin.Code')); ?></th>
                                                    <th><?php echo e(trans('admin.Date')); ?></th>
                                                   
                                                    <th> <?php echo e(trans('admin.Payment_Method')); ?></th>
                                                   
                                                    <th> <?php echo e(trans('admin.Safe')); ?> </th>
                                                    <th> <?php echo e(trans('admin.Vendor')); ?> </th>
                                                   
                                                    <th><?php echo e(trans('admin.Details')); ?></th>
                                                    <th><?php echo e(trans('admin.Data')); ?></th>
                                                    <th><?php echo e(trans('admin.Actions')); ?> </th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php $__currentLoopData = $items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <tr>
                                                    <td><?php echo e($item->Code); ?></td>
                                                    <td><?php echo e($item->Date); ?></td>
                                                    
                                                    <td>
                                                    
                                                    <?php if($item->Payment_Method == 'Cash'): ?>
                                                       <?php echo e(trans('admin.Cash')); ?> 
                                                    <?php elseif($item->Payment_Method == 'Later'): ?>
                                                       <?php echo e(trans('admin.Later')); ?> 
                                         <?php elseif($item->Payment_Method == 'Check'): ?>
   
                             <button type="button" class="btn btn-default" data-toggle="modal" data-target="#Check<?php echo e($item->id); ?>"><?php echo e(trans('admin.Check')); ?> </button> 
                                                    <?php endif; ?>    
                                                    </td>
                                                      
                                                        <td>
                                                    <?php $Sa=AcccountingManual::find($item->Safe);  ?>
                                                            <?php echo e(app()->getLocale() == 'ar' ?$Sa->Name :$Sa->NameEn); ?>         
                                                  
                                                       </td>
                                                        <td>
                                                    <?php $Sv=AcccountingManual::find($item->Vendor);  ?>
                                                      <?php echo e(app()->getLocale() == 'ar' ?$Sv->Name :$Sv->NameEn); ?>            
                                                    </td>
                                                       
                                                    
                                                    <td>
                                                        <button type="button" class="btn btn-default" data-toggle="modal" data-target="#default-example-modal-center-open<?php echo e($item->id); ?>">
                                                            <?php echo e(trans('admin.Details')); ?>

                                                        </button>
                                                    </td>
                                                     <td>
                                                        <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#show-data<?php echo e($item->id); ?>">
                                                           <?php echo e(trans('admin.Data')); ?>

                                                        </button>
                                                    </td>
                                                    <td class="text-center" colspan="2">
                                     <div class="row">
                                     <div class="col-md-2">         
                                           <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('ارتجاع فاتوره مشتريات')): ?>                 
             <a href="<?php echo e(url('ReturnPurch/'.$item->id)); ?>" class="btn btn-default" data-toggle="tooltip" data-placement="top" title="<?php echo e(trans('admin.Return')); ?>" data-original-title="<?php echo e(trans('admin.Return')); ?>" ><i class="fal fa-arrow-right"></i></a>
                                        <?php endif; ?>
                                         </div> 
                                        <div class="col-md-2">       
                 <a href="<?php echo e(url('PurchPrint/'.$item->id)); ?>" class="btn btn-default" data-toggle="tooltip" data-placement="top" title="<?php echo e(trans('admin.Print')); ?>" data-original-title="<?php echo e(trans('admin.Print')); ?>" >
                                                    <i class="fal fa-print" ></i>    
                                                    </a>      
                                         </div>            
                                        <div class="col-md-2">                      
       <a href="<?php echo e(url('Barcode/'.$item->id)); ?>" class="btn btn-default"  data-toggle="tooltip" data-placement="top" title="<?php echo e(trans('admin.Barcode_Print')); ?>" data-original-title="<?php echo e(trans('admin.Barcode_Print')); ?> "><i class="fal fa-barcode"></i></a>                                        </div>
                                        
                                         
                                        <div class="col-md-2">       
                                                          <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تعديل فاتوره مشتريات')): ?>                 
             <a href="<?php echo e(url('EditPuechasesBill/'.$item->id)); ?>" class="btn btn-default"  data-toggle="tooltip" data-placement="top" title="<?php echo e(trans('admin.Edit')); ?>" data-original-title="<?php echo e(trans('admin.Edit')); ?>"><i class="fal fa-edit"></i></a>
                                        <?php endif; ?>
                                         </div>
                                            <div class="col-md-2">   
                                                 <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('حذف فاتوره مشتريات')): ?>         
             <button type="button" class="btn btn-default" data-toggle="modal" data-target="#Delete<?php echo e($item->id); ?>" ><i class="fal fa-trash-alt" data-toggle="tooltip" data-placement="top" title="<?php echo e(trans('admin.Delete')); ?>" data-original-title="<?php echo e(trans('admin.Delete')); ?>"></i></button>
                                        <?php endif; ?>                
                                         </div>        
                                                        
                                                 </div>           
                                                        
                                                    </td>
                                                </tr>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                
                                            </tbody>
                                            <tfoot>
                                                <tr>
                                                    <th><?php echo e(trans('admin.Code')); ?></th>
                                                    <th><?php echo e(trans('admin.Date')); ?></th>
                                                 
                                                    <th> <?php echo e(trans('admin.Payment_Method')); ?></th>
                                                    
                                                    <th> <?php echo e(trans('admin.Safe')); ?> </th>
                                                    <th> <?php echo e(trans('admin.Vendor')); ?> </th>
                                                   
                                                    <th><?php echo e(trans('admin.Details')); ?></th>
                                                    <th><?php echo e(trans('admin.Data')); ?></th>
                                                    <th><?php echo e(trans('admin.Actions')); ?> </th>
                                                </tr>
                                            </tfoot>
                        
                                        </table>
                                       
                                        <?php echo e($items->Links()); ?>

                                        </div>
                                        <!-- datatable end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </main>

  <?php $__currentLoopData = $items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

                        <!-- Modal Details -->
    <div class="modal fade" id="default-example-modal-center-open<?php echo e($item->id); ?>" tabindex="-1" role="dialog"
                aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered modal-xl" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h4 class="modal-title">
                               <?php echo e(trans('admin.Details')); ?> 
                            </h4>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true"><i class="fal fa-times"></i></span>
                            </button>
                        </div>
                        <div class="modal-body">
     
                            
                <?php
                $details=ProductsPurchases::where('Purchase',$item->id)->get();                         
                ?>            
                        <div class="mt-3">
                            <div style="overflow:auto;">
                            <table id=""
                            class="table table-bordered table-hover table-striped " >
                            <thead>
                                <tr>
                                    <th><?php echo e(trans('admin.Code')); ?> </th>
                                    <th><?php echo e(trans('admin.Name')); ?> </th>
                                    <th><?php echo e(trans('admin.Group')); ?> </th>
                                    <th><?php echo e(trans('admin.Brand')); ?> </th>
                                     <th><?php echo e(trans('admin.Unit')); ?> </th>
                                    <th><?php echo e(trans('admin.Qty')); ?> </th>
                                    <th><?php echo e(trans('admin.Price')); ?> </th>
                                    <th><?php echo e(trans('admin.Discount')); ?> </th>
                                    <th><?php echo e(trans('admin.Total_Bf_Tax')); ?> </th>
                                    <th><?php echo e(trans('admin.Total_Tax')); ?> </th>
                                    <th><?php echo e(trans('admin.Total')); ?> </th>
                                    <th><?php echo e(trans('admin.Store')); ?> </th>
                                    <th><?php echo e(trans('admin.Exp_Date')); ?> </th>
                                    <th><?php echo e(trans('admin.Later_Due')); ?> </th>

                                </tr>
                            </thead>
                            <tbody id="">
                                <?php $__currentLoopData = $details; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $detail): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td><?php echo e($detail->Product_Code); ?></td>
                                    <td>
                                                    <?php echo e(app()->getLocale() == 'ar' ?$detail->Product()->first()->P_Ar_Name :$detail->Product()->first()->P_En_Name); ?>            
                                    
     <?php if(!empty($detail->V1)): ?>   (<?php echo e(app()->getLocale() == 'ar' ?$detail->V1()->first()->Name :$detail->V1()->first()->NameEn); ?>  )  <?php endif; ?> 
                                  <?php if(!empty($detail->V2)): ?>    ((<?php echo e(app()->getLocale() == 'ar' ?$detail->V2()->first()->Name :$detail->V2()->first()->NameEn); ?>  ))  <?php endif; ?>           
                                    </td>
                                    <td>
                                         <?php if(!empty($detail->Product()->first()->Group)): ?>       <?php echo e(app()->getLocale() == 'ar' ?$detail->Product()->first()->Group()->first()->Name :$detail->Product()->first()->Group()->first()->NameEn); ?>    <?php endif; ?>
                                    </td>
                                         <td>
                                            <?php if(!empty($detail->Product()->first()->Brand)): ?> 
                                      <?php echo e(app()->getLocale() == 'ar' ?$detail->Product()->first()->Brand()->first()->Name :$detail->Product()->first()->Brand()->first()->NameEn); ?>  
                                            <?php endif; ?> 
                                    </td>
                                    
                                    <td>   <?php echo e(app()->getLocale() == 'ar' ?$detail->Unit()->first()->Name :$detail->Unit()->first()->NameEn); ?>  </td>
                                    <td>
    
                                         <?php echo e($detail->Qty); ?>

                            
                                    </td>
                                    <td><?php echo e($detail->Price); ?></td>
                                    <td><?php echo e($detail->Discount); ?></td>
                                    <td><?php echo e($detail->Total_Bf_Tax); ?></td>
                                    <td><?php echo e($detail->Total_Tax); ?></td>
                                    <td><?php echo e($detail->Total); ?></td>
                                    <td>     <?php echo e(app()->getLocale() == 'ar' ?$detail->Store()->first()->Name :$detail->Store()->first()->NameEn); ?>   </td>
                                    <td><?php echo e($detail->Exp_Date); ?></td>
                                    <td><?php echo e($detail->Later_Due); ?></td>
                      

                                </tr>
                                 <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
  
                        </table>
                        </div>
                        <div style="overflow:auto;">
                        <table class="table table-bordered table-hover table-striped  mt-4" >
                            <tbody>
                                <tr>
                                    <td><?php echo e(trans('admin.Product_Numbers')); ?></td>
                                    <td><?php echo e($item->Product_Numbers); ?></td>
                                    
                                    <td><?php echo e(trans('admin.Total_Qty')); ?></td>
                                    <td><?php echo e($item->Total_Qty); ?></td>
                                    
                                    <td><?php echo e(trans('admin.Total_Discount')); ?></td>
                                    <td><?php echo e($item->Total_Discount); ?></td>
                                    
                                     <td><?php echo e(trans('admin.Total_Bf_Taxes')); ?></td>
                                    <td><?php echo e($item->Total_BF_Taxes); ?></td>
                                    
                                    <td><?php echo e(trans('admin.Total_Taxes')); ?></td>
                                    <td><?php echo e($item->Total_Taxes); ?></td>
                                    </tr>
                                          <tr>
                                     <?php if($item->Payment_Method == 'Cash'): ?>
                                    <?php if(!empty($item->Pay)): ?>        
                                    <td><?php echo e(trans('admin.Total_Price')); ?></td>
                                    <td><?php echo e(number_format((float)$item->Total_Price, 2, '.', '')); ?></td>
                                    
                                    <td><?php echo e(trans('admin.Paid')); ?></td>
                                    <td><?php echo e($item->Pay); ?></td>  
                                    
                                    <td><?php echo e(trans('admin.Residual')); ?></td>
                                    <td><?php echo e(round($item->Total_Price) - round($item->Pay)); ?></td>  
                                    
                                            <td><?php echo e(trans('admin.Total_Net')); ?></td>
                                    <td><?php echo e($item->Total_Price + $item->Total_Taxes - $item->Total_Discount); ?></td> 
                            
                                    <?php else: ?>
                                    <td><?php echo e(trans('admin.Total_Price')); ?></td>
                                    <td><?php echo e($item->Total_Price); ?></td>
                                    
                                    <td><?php echo e(trans('admin.Paid')); ?></td>
                                    <td><?php echo e($item->Pay); ?></td> 
                                    
                                    <td><?php echo e(trans('admin.Total_Net')); ?></td>
                                    <td><?php echo e($item->Total_Price + $item->Total_Taxes - $item->Total_Discount); ?></td> 
                            
                                    <?php endif; ?>
                                    
                                    <?php else: ?>
                                    <td><?php echo e(trans('admin.Total_Price')); ?></td>
                                    <td><?php echo e($item->Total_Price); ?></td>
                                    
                                    <td><?php echo e(trans('admin.Paid')); ?></td>
                                    <td><?php echo e($item->Pay); ?></td> 
                                    
                                            <td><?php echo e(trans('admin.Total_Net')); ?></td>
                                    <td><?php echo e($item->Total_Price + $item->Total_Taxes - $item->Total_Discount); ?></td> 
                            
                                    <?php endif; ?>
                                    
                                    
                            
                            
                                </tr>
                            </tbody>
                        </table>
                        </div>
                        </div>
                       
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal"><?php echo e(trans('admin.Close')); ?></button>
                                                                                 <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تعديل فاتوره مشتريات')): ?>                 
             <a href="<?php echo e(url('EditPuechasesBill/'.$item->id)); ?>" class="btn btn-default"  data-toggle="tooltip" data-placement="top" title="تعديل" data-original-title="تعديل"><i class="fal fa-edit"></i></a>
                                        <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
             
            </div>

    <!-- Modal show data -->
    <div class="modal fade" id="show-data<?php echo e($item->id); ?>" tabindex="-1" role="dialog"
                aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered modal-xl" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h4 class="modal-title">
                             <?php echo e(trans('admin.Data')); ?>

                            </h4>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true"><i class="fal fa-times"></i></span>
                            </button>
                        </div>
                        <div class="modal-body">
      <div style="overflow:auto;">
        <table id="" class="table table-bordered table-hover table-striped " style="width:120%;">
                                            <thead class="bg-highlight">
                                                <tr>
                                                  
                                                    <th> <?php echo e(trans('admin.Draw')); ?> </th>  
                                                   
                                                    <th><?php echo e(trans('admin.Status')); ?> </th>
                                                    <th> <?php echo e(trans('admin.Vendor_Bill_Date')); ?> </th>
                                                    <th> <?php echo e(trans('admin.Refernce_Number')); ?> </th>
                                                    <th> <?php echo e(trans('admin.Notes')); ?> </th>
                                                   
                                                    <th> <?php echo e(trans('admin.Delegate')); ?> </th>
                                                    <th> <?php echo e(trans('admin.Store')); ?> </th>
                                                    <th> <?php echo e(trans('admin.Coin')); ?> </th>
                                                    <th> <?php echo e(trans('admin.Cost_Center')); ?> </th>
                                                    <th> <?php echo e(trans('admin.Shipping_Compaines')); ?> </th>
                                                    <th> <?php echo e(trans('admin.User')); ?> </th>
                                                    <th> <?php echo e(trans('admin.File')); ?> </th>
                                                   
                                                </tr>
                                            </thead>
                                            <tbody>
                                  
                                                <tr>
                                                   
                                                      <td><?php echo e($item->Draw); ?></td>  
                                                   
                                                        <td>
                                                    
                                                         <?php if($item->Status == 1): ?>
                                                       <?php echo e(trans('admin.Recived')); ?> 
                                                    <?php elseif($item->Status == 0): ?>
                                                       <?php echo e(trans('admin.Pending')); ?> 
                                                    <?php endif; ?>    
                                                    </td>
                                                        <td><?php echo e($item->Vendor_Bill_Date); ?></td>
                                                        <td><?php echo e($item->Refernce_Number); ?></td>
                                                        <td><?php echo e($item->Note); ?></td>
                                                    
                                                        
                                                        <td>
                                                  <?php if(!empty($item->Delegate)): ?>   
                                                   <?php $SD=Employess::find($item->Delegate);  ?>            
                                                            
                                                       <?php echo e(app()->getLocale() == 'ar' ?$SD->Name :$SD->NameEn); ?>

                                                    <?php endif; ?>
                                                    </td>
                                                        <td>
                                                    
                                                    <?php $SS=Stores::find($item->Store);  ?> 
                                                     <?php echo e(app()->getLocale() == 'ar' ?$SS->Name :$SS->NameEn); ?>

                                                    </td>
                                                        <td>
                                                    
                                                      <?php $SC=Coins::find($item->Coin);  ?> 
                                                        <?php echo e(app()->getLocale() == 'ar' ?$SC->Arabic_Name :$SC->English_Name); ?>

                                                    </td>
                                                            <td>
                                                     <?php if(!empty($item->Cost_Center)): ?>        
                                                        <?php $SCO=CostCenter::find($item->Cost_Center);  ?> 
                                                       <?php echo e(app()->getLocale() == 'ar' ?$SCO->Arabic_Name :$SCO->English_Name); ?>

                                                    <?php endif; ?>
                                                    </td>
                                                    
                                                             <td>
                                                         <?php if(!empty($item->Ship)): ?>          
                                                    <?php $Sh=AcccountingManual::find($item->Ship);  ?>
                                       
                                                          <?php echo e(app()->getLocale() == 'ar' ?$Sh->Name :$Sh->NameEn); ?>         
                                                            <?php endif; ?>     
                                                    </td>
                                                    
                                                             <td>
                                            <?php $SU=Admin::find($item->User);  ?> 
                                                          <?php echo e(app()->getLocale() == 'ar' ?$SU->name :$SU->nameEn); ?>

                                                    </td>
                                                                <td>
                                <?php if(!empty($item->File)): ?> 
                            <a href="<?php echo e(URL::to($item->File)); ?>" class="btn btn-primary" dowmload><i class="fal fa-download"></i></a>            
                                        <?php endif; ?>    
                                    </td>
                                                   
                                                </tr>
                            
                                                
                                            </tbody>
                                            <tfoot>
                                                <tr>
                                                  
                                                    <th> <?php echo e(trans('admin.Draw')); ?> </th>  
                                                   
                                                    <th><?php echo e(trans('admin.Status')); ?> </th>
                                                    <th> <?php echo e(trans('admin.Vendor_Bill_Date')); ?> </th>
                                                    <th> <?php echo e(trans('admin.Refernce_Number')); ?> </th>
                                                    <th> <?php echo e(trans('admin.Notes')); ?> </th>
                                                  
                                                    <th> <?php echo e(trans('admin.Delegate')); ?> </th>
                                                    <th> <?php echo e(trans('admin.Store')); ?> </th>
                                                    <th> <?php echo e(trans('admin.Coin')); ?> </th>
                                                    <th> <?php echo e(trans('admin.Cost_Center')); ?> </th>
                                                 <th> <?php echo e(trans('admin.Shipping_Compaines')); ?> </th>    
                                                    <th> <?php echo e(trans('admin.User')); ?> </th>
                                                  
                                                </tr>
                                            </tfoot>
                        
                                        </table>
                                       
                                        <?php echo e($items->Links()); ?>

                                        </div>
                           
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal"><?php echo e(trans('admin.Close')); ?></button>
                            
                        </div>
                    </div>
                </div>
            </div>

            <!-- Modal Check -->
    <div class="modal fade" id="Check<?php echo e($item->id); ?>" tabindex="-1" role="dialog" aria-hidden="true">
                        <div class="modal-dialog modal-dialog-centered" role="document">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h4 class="modal-title">
                         
                                    <div class="row">
                                                <div class="col-md-4">
                                            <label><?php echo e(trans('admin.Check_Type')); ?></label>     
                                                <?php if(!empty($item->Check_Type)): ?>    
                                                <span>
                                                    
                                             <?php echo e(app()->getLocale() == 'ar' ?$item->Check_Type()->first()->Arabic_Name :$item->Check_Type()->first()->English_Name); ?>        
                                                    </span> 
                                                <?php endif; ?>    
                                                    </div>
                                                    
                                                          <div class="col-md-4">
                                            <label><?php echo e(trans('admin.Due_Date')); ?></label>     
                                                <span><?php echo e($item->Due_Date); ?></span>    
                                                    </div>  
                                                    
                                                          <div class="col-md-4">
                                            <label><?php echo e(trans('admin.Check_Number')); ?></label>     
                                                <span><?php echo e($item->Check_Number); ?></span>    
                                                    </div>  
                                     
                                                        
                                                        </div>      
                                        
                                        
                                        
                                    </h4>
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                        <span aria-hidden="true"><i class="fal fa-times"></i></span>
                                    </button>
                                </div>
                                
                                <div class="modal-footer">
                         <button type="button" class="btn btn-secondary" data-dismiss="modal"><?php echo e(trans('admin.No')); ?></button>
                                </div>
                            </div>
                        </div>
                    </div>


          <!-- Modal Delete -->
                    <div class="modal fade" id="Delete<?php echo e($item->id); ?>" tabindex="-1" role="dialog" aria-hidden="true">
                        
                        <div class="modal-dialog modal-dialog-centered" role="document">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h4 class="modal-title">
                                    <?php echo e(trans('admin.RUSWDT')); ?> <strong><?php echo e($item->Code); ?></strong>
                                    </h4>
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                        <span aria-hidden="true"><i class="fal fa-times"></i></span>
                                    </button>
                                </div>
                                <div class="modal-footer">
                 <button type="button" class="btn btn-secondary" data-dismiss="modal"> <?php echo e(trans('admin.No')); ?></button>
                   <a href="<?php echo e(url('DeletePurchaseBill/'.$item->id)); ?>"  class="btn btn-primary"> <?php echo e(trans('admin.Yes')); ?></a>
                                </div>
                            </div>
                        </div>
                    </div>
     

<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>



<?php $__env->stopSection(); ?>


<?php $__env->startPush('js'); ?>
    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/datagrid/datatables/datatables.bundle.css')); ?>">
    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/formplugins/summernote/summernote.css')); ?>">
    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/formplugins/select2/select2.bundle.css')); ?>">

    <style>
        th{
            width:135px!important;
        }
    </style>

 <script src="<?php echo e(asset('Admin/js/datagrid/datatables/datatables.export.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/datagrid/datatables/datatables.bundle.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/formplugins/summernote/summernote.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/formplugins/select2/select2.bundle.js')); ?>"></script>
    <script>
        //_fnFeatureHtmlLength();
        $(document).ready(function () {
            // Setup - add a text input to each footer cell
            $('#dt-basic-example thead tr').clone(true).appendTo('#dt-basic-example thead');
            $('#dt-basic-example thead tr:eq(1) th').each(function (i) {
                var title = $(this).text();
                $(this).html('<input type="text" class="form-control form-control-sm" placeholder="Search '  + '" />');

                $('input', this).on('keyup change', function () {
                    if (table.column(i).search() !== this.value) {
                        table
                            .column(i)
                            .search(this.value)
                            .draw();
                    }
                });
            });
            var table = $('#dt-basic-example').DataTable(
                {
                    // responsive: true,
                    orderCellsTop: true,
                    fixedHeader: true,
                    lengthChange: true,

                    dom: "<'row mb-3'<'col-sm-12 col-md-3 d-flex align-items-center justify-content-start'f><'col-sm-12 col-md-9 d-flex align-items-center justify-content-end'B>>" +
                        "<'row'<'col-sm-12'tr>>" +
                        "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",

                    buttons: [
                        {
                            extend: 'pageLength',
                            className: 'btn-outline-default'
                        },
                        {
                            extend: 'colvis',
                            text: 'Column Visibility',
                            titleAttr: 'Col visibility',
                            className: 'btn-outline-default'
                        },
                        {
                            extend: 'pdfHtml5',
                            text: 'PDF',
                            titleAttr: 'Generate PDF',
                            className: 'btn-outline-danger btn-sm mr-1'
                        },
                        {
                            extend: 'excelHtml5',
                            text: 'Excel',
                            titleAttr: 'Generate Excel',
                            className: 'btn-outline-success btn-sm mr-1'
                        },
                        {
                            extend: 'csvHtml5',
                            text: 'CSV',
                            titleAttr: 'Generate CSV',
                            className: 'btn-outline-primary btn-sm mr-1'
                        },
                        {
                            extend: 'copyHtml5',
                            text: 'Copy',
                            titleAttr: 'Copy to clipboard',
                            className: 'btn-outline-primary btn-sm mr-1'
                        },
                        {
                            extend: 'print',
                            text: 'Print',
                            titleAttr: 'Print Table',
                            className: 'btn-outline-primary btn-sm'
                        }
                    ],
                });
            $('.js-thead-colors a').on('click', function () {
                var theadColor = $(this).attr("data-bg");
                console.log(theadColor);
                $('#dt-basic-example thead').removeClassPrefix('bg-').addClass(theadColor);
            });

            $('.js-tbody-colors a').on('click', function () {
                var theadColor = $(this).attr("data-bg");
                console.log(theadColor);
                $('#dt-basic-example').removeClassPrefix('bg-').addClass(theadColor);
            });

        });

    </script>
  
  <script>
    $(document).ready(function()
    {
        $(function()
        {
            $('.select2').select2();

            $(".select2-placeholder-multiple").select2(
            {
                placeholder: "Select State"
            });
            $(".js-hide-search").select2(
            {
                minimumResultsForSearch: 1 / 0
            });
            $(".js-max-length").select2(
            {
                maximumSelectionLength: 2,
                placeholder: "Select maximum 2 items"
            });
            $(".select2-placeholder").select2(
            {
                placeholder: "Select a state",
                allowClear: true
            });

            $(".js-select2-icons").select2(
            {
                minimumResultsForSearch: 1 / 0,
                templateResult: icon,
                templateSelection: icon,
                escapeMarkup: function(elm)
                {
                    return elm
                }
            });

            function icon(elm)
            {
                elm.element;
                return elm.id ? "<i class='" + $(elm.element).data("icon") + " mr-2'></i>" + elm.text : elm.text
            }

            $(".js-data-example-ajax").select2(
            {
                ajax:
                {
                    url: "https://api.github.com/search/repositories",
                    dataType: 'json',
                    delay: 250,
                    data: function(params)
                    {
                        return {
                            q: params.term, // search term
                            page: params.page
                        };
                    },
                    processResults: function(data, params)
                    {
                        // parse the results into the format expected by Select2
                        // since we are using custom formatting functions we do not need to
                        // alter the remote JSON data, except to indicate that infinite
                        // scrolling can be used
                        params.page = params.page || 1;

                        return {
                            results: data.items,
                            pagination:
                            {
                                more: (params.page * 30) < data.total_count
                            }
                        };
                    },
                    cache: true
                },
                placeholder: 'Search for a repository',
                escapeMarkup: function(markup)
                {
                    return markup;
                }, // let our custom formatter work
                minimumInputLength: 1,
                templateResult: formatRepo,
                templateSelection: formatRepoSelection
            });

            function formatRepo(repo)
            {
                if (repo.loading)
                {
                    return repo.text;
                }

                var markup = "<div class='select2-result-repository clearfix d-flex'>" +
                    "<div class='select2-result-repository__avatar mr-2'><img src='" + repo.owner.avatar_url + "' class='width-2 height-2 mt-1 rounded' /></div>" +
                    "<div class='select2-result-repository__meta'>" +
                    "<div class='select2-result-repository__title fs-lg fw-500'>" + repo.full_name + "</div>";

                if (repo.description)
                {
                    markup += "<div class='select2-result-repository__description fs-xs opacity-80 mb-1'>" + repo.description + "</div>";
                }

                markup += "<div class='select2-result-repository__statistics d-flex fs-sm'>" +
                    "<div class='select2-result-repository__forks mr-2'><i class='fal fa-lightbulb'></i> " + repo.forks_count + " Forks</div>" +
                    "<div class='select2-result-repository__stargazers mr-2'><i class='fal fa-star'></i> " + repo.stargazers_count + " Stars</div>" +
                    "<div class='select2-result-repository__watchers mr-2'><i class='fal fa-eye'></i> " + repo.watchers_count + " Watchers</div>" +
                    "</div>" +
                    "</div></div>";

                return markup;
            }

            function formatRepoSelection(repo)
            {
                return repo.full_name || repo.text;
            }
        });
    });

</script>
<script>
    var autoSave = $('#autoSave');
    var interval;
    var timer = function()
    {
        interval = setInterval(function()
        {
            //start slide...
            if (autoSave.prop('checked'))
                saveToLocal();

            clearInterval(interval);
        }, 3000);
    };

    //save
    var saveToLocal = function()
    {
        localStorage.setItem('summernoteData', $('#saveToLocal').summernote("code"));
        console.log("saved");
    }

    //delete 
    var removeFromLocal = function()
    {
        localStorage.removeItem("summernoteData");
        $('#saveToLocal').summernote('reset');
    }

    $(document).ready(function()
    {
        //init default
        $('.js-summernote').summernote(
        {
            height: 200,
            tabsize: 2,
            placeholder: "Type here...",
            dialogsFade: true,
            toolbar: [
                ['style', ['style']],
                ['font', ['strikethrough', 'superscript', 'subscript']],
                ['font', ['bold', 'italic', 'underline', 'clear']],
                ['fontsize', ['fontsize']],
                ['fontname', ['fontname']],
                ['color', ['color']],
                ['para', ['ul', 'ol', 'paragraph']],
                ['height', ['height']]
                ['table', ['table']],
                ['insert', ['link', 'picture', 'video']],
                ['view', ['fullscreen', 'codeview', 'help']]
            ],
            callbacks:
            {
                //restore from localStorage
                onInit: function(e)
                {
                    $('.js-summernote').summernote("code", localStorage.getItem("summernoteData"));
                },
                onChange: function(contents, $editable)
                {
                    clearInterval(interval);
                    timer();
                }
            }
        });

        //load emojis
        $.ajax(
        {
            url: 'https://api.github.com/emojis',
            async: false
        }).then(function(data)
        {
            window.emojis = Object.keys(data);
            window.emojiUrls = data;
        });

        //init emoji example
        $(".js-hint2emoji").summernote(
        {
            height: 100,
            toolbar: false,
            placeholder: 'type starting with : and any alphabet',
            hint:
            {
                match: /:([\-+\w]+)$/,
                search: function(keyword, callback)
                {
                    callback($.grep(emojis, function(item)
                    {
                        return item.indexOf(keyword) === 0;
                    }));
                },
                template: function(item)
                {
                    var content = emojiUrls[item];
                    return '<img src="' + content + '" width="20" /> :' + item + ':';
                },
                content: function(item)
                {
                    var url = emojiUrls[item];
                    if (url)
                    {
                        return $('<img />').attr('src', url).css('width', 20)[0];
                    }
                    return '';
                }
            }
        });

        //init mentions example
        $(".js-hint2mention").summernote(
        {
            height: 100,
            toolbar: false,
            placeholder: "type starting with @",
            hint:
            {
                mentions: ['jayden', 'sam', 'alvin', 'david'],
                match: /\B@(\w*)$/,
                search: function(keyword, callback)
                {
                    callback($.grep(this.mentions, function(item)
                    {
                        return item.indexOf(keyword) == 0;
                    }));
                },
                content: function(item)
                {
                    return '@' + item;
                }
            }
        });

    });

</script>
<script type="text/javascript">


    $(".show-table").click(function(){
        $(".hide-table").show();
    });
    
</script>

	<style>
	@media  print {
		body * {
			visibility: hidden;
		}
		.modal-content * {
			visibility: visible;
			overflow: visible;
		}
		.main-page * {
			display: none;
		}
		.modal {
			position: absolute;
			left: 0;
			top: -140px;
			margin: 0;
			padding: 0;
			font-size:20px;
			min-height: 550px;
			visibility: visible;
			overflow: visible !important; /* Remove scrollbar for printing. */
		}
		.modal-dialog {
			visibility: visible !important;
			overflow: visible !important; /* Remove scrollbar for printing. */
		}
		
		  .page-content{
            display:none;
        }
        @page  {
		
		size: a4;
       
    }
        

	}
	</style>


<?php $__env->stopPush(); ?>
<?php echo $__env->make('admin.index', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp2\htdocs\erp\resources\views/admin/Purchases/PurchasesSechdule.blade.php ENDPATH**/ ?>