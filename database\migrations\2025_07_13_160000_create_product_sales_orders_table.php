<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateProductSalesOrdersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('product_sales_orders', function (Blueprint $table) {
            $table->id();
            
            // Order relationship
            $table->unsignedBigInteger('SalesOrder')->nullable();
            
            // Product information
            $table->unsignedBigInteger('Product')->nullable();
            $table->string('Product_Code')->nullable();
            $table->string('Product_Name')->nullable();
            $table->string('Product_Name_En')->nullable();
            
            // Product variants
            $table->unsignedBigInteger('V1')->nullable(); // Variant 1 (e.g., Size)
            $table->unsignedBigInteger('V2')->nullable(); // Variant 2 (e.g., Color)
            $table->unsignedBigInteger('V3')->nullable(); // Variant 3
            
            // Quantities and units
            $table->decimal('Qty', 10, 2)->default(0); // Ordered quantity
            $table->decimal('AvQty', 10, 2)->default(0); // Available quantity
            $table->unsignedBigInteger('Unit')->nullable(); // Unit of measurement
            $table->decimal('Unit_Price', 10, 2)->default(0); // Unit price
            
            // Pricing
            $table->decimal('Price', 10, 2)->default(0); // Base price
            $table->decimal('Discount', 10, 2)->default(0); // Discount amount
            $table->decimal('Discount_Percent', 5, 2)->default(0); // Discount percentage
            $table->decimal('Price_After_Discount', 10, 2)->default(0); // Price after discount
            
            // Taxes
            $table->decimal('Tax_Percent', 5, 2)->default(0); // Tax percentage
            $table->decimal('Tax_Amount', 10, 2)->default(0); // Tax amount
            $table->decimal('Total_Tax', 10, 2)->default(0); // Total tax
            
            // Totals
            $table->decimal('Total_Before_Tax', 10, 2)->default(0); // Total before tax
            $table->decimal('Total', 10, 2)->default(0); // Final total
            
            // Store and inventory
            $table->unsignedBigInteger('Store')->nullable(); // Store/warehouse
            $table->string('Batch_Number')->nullable(); // Batch/lot number
            $table->date('Exp_Date')->nullable(); // Expiration date
            $table->date('Production_Date')->nullable(); // Production date
            
            // Additional information
            $table->text('Notes')->nullable(); // Item notes
            $table->string('Barcode')->nullable(); // Product barcode
            $table->decimal('Weight', 8, 3)->default(0); // Product weight
            $table->decimal('Volume', 8, 3)->default(0); // Product volume
            
            // Cost and profit
            $table->decimal('Cost_Price', 10, 2)->default(0); // Cost price
            $table->decimal('Profit_Amount', 10, 2)->default(0); // Profit amount
            $table->decimal('Profit_Percent', 5, 2)->default(0); // Profit percentage
            
            // Status and tracking
            $table->string('Status')->default('Pending'); // Item status
            $table->integer('Delivered_Qty')->default(0); // Delivered quantity
            $table->integer('Returned_Qty')->default(0); // Returned quantity
            
            // Timestamps
            $table->timestamps();
            
            // Indexes for performance
            $table->index('SalesOrder');
            $table->index('Product');
            $table->index('Store');
            $table->index('Status');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('product_sales_orders');
    }
}
