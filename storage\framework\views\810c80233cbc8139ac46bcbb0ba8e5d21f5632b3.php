<?php $__env->startSection('content'); ?>
<?php
use Spatie\Permission\Models\Permission;
use App\Models\Modules;
use App\Models\PackPrem;
$Modules=Modules::orderBy('id','desc')->first();
?>
<style>

  @media  only screen and (max-width: 300px){
   #dt-basic-example{
 
    }}
    
    
@media  only screen and (max-width: 300px){
 .table-responsive{
        display:block;
        width:100%;
    }}
    @media  only screen and (min-width: 992px){
 .table-responsive{
        display:table;
        width:100%;
}}
.col-md-3{
        border: 1px solid #886ab5;
    border-radius: 13px;
    margin:4px;
}
</style>
  <title><?php echo e(trans('admin.AdminsPremations')); ?></title>

 <main id="js-page-content" role="main" class="page-content">
                        <ol class="breadcrumb page-breadcrumb">
                            <li class="breadcrumb-item"><a href="javascript:void(0);"> <?php echo e(trans('admin.Settings')); ?></a></li>
                            <li class="breadcrumb-item active"><?php echo e(trans('admin.AdminsPremations')); ?></li>
                            <li class="position-absolute pos-top pos-right d-none d-sm-block"><span
                                    class="js-get-date"></span></li>
                        </ol>
                        <div class="row">
                            <div class="col-xl-12">
                                <div id="panel-1" class="panel">
                                    <div class="panel-hdr">
                                        <h2>
                                            <span class="fw-300"><i><?php echo e(trans('admin.AdminsPremations')); ?></i></span>
                                        </h2>
                                        <div class="panel-toolbar">
                                              <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اضافه صلاحيه')): ?> 
                                            <button type="button" class="btn btn-default" data-toggle="modal" data-target="#default-example-modal-lg"><?php echo e(trans('admin.AddNew')); ?></button>
                                            <?php endif; ?>
                                            <button class="btn btn-primary btn-sm mx-3" data-toggle="dropdown">Table Style</button>
    
                                            <div
                                                class="dropdown-menu dropdown-menu-animated dropdown-menu-right position-absolute pos-top">
                                                <button class="dropdown-item active" data-action="toggle"
                                                    data-class="table-bordered" data-target="#dt-basic-example"> Bordered
                                                    Table </button>
                                                <button class="dropdown-item" data-action="toggle" data-class="table-sm"
                                                    data-target="#dt-basic-example"> Smaller Table </button>
                                                <button class="dropdown-item" data-action="toggle" data-class="table-dark"
                                                    data-target="#dt-basic-example"> Table Dark </button>
                                                <button class="dropdown-item active" data-action="toggle"
                                                    data-class="table-hover" data-target="#dt-basic-example"> Table Hover
                                                </button>
                                                <button class="dropdown-item active" data-action="toggle"
                                                    data-class="table-stripe" data-target="#dt-basic-example"> Table
                                                    Stripped </button>
                                                <div class="dropdown-divider m-0"></div>
                                                <div class="dropdown-multilevel dropdown-multilevel-left">
                                                    <div class="dropdown-item">
                                                        tbody color
                                                    </div>
                                                    <div class="dropdown-menu no-transition-delay">
                                                        <div class="js-tbody-colors dropdown-multilevel dropdown-multilevel-left d-flex flex-wrap"
                                                            style="width: 15.9rem; padding: 0.5rem">
                                                            <a href="javascript:void(0);" data-bg="bg-primary-100"
                                                                class="btn d-inline-block bg-primary-100 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-primary-200"
                                                                class="btn d-inline-block bg-primary-200 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-primary-300"
                                                                class="btn d-inline-block bg-primary-300 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-primary-400"
                                                                class="btn d-inline-block bg-primary-400 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-primary-500"
                                                                class="btn d-inline-block bg-primary-500 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-primary-600"
                                                                class="btn d-inline-block bg-primary-600 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-primary-700"
                                                                class="btn d-inline-block bg-primary-700 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-primary-800"
                                                                class="btn d-inline-block bg-primary-800 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-primary-900"
                                                                class="btn d-inline-block bg-primary-900 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-success-100"
                                                                class="btn d-inline-block bg-success-100 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-success-200"
                                                                class="btn d-inline-block bg-success-200 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-success-300"
                                                                class="btn d-inline-block bg-success-300 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-success-400"
                                                                class="btn d-inline-block bg-success-400 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-success-500"
                                                                class="btn d-inline-block bg-success-500 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-success-600"
                                                                class="btn d-inline-block bg-success-600 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-success-700"
                                                                class="btn d-inline-block bg-success-700 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-success-800"
                                                                class="btn d-inline-block bg-success-800 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-success-900"
                                                                class="btn d-inline-block bg-success-900 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-info-100"
                                                                class="btn d-inline-block bg-info-100 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-info-200"
                                                                class="btn d-inline-block bg-info-200 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-info-300"
                                                                class="btn d-inline-block bg-info-300 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-info-400"
                                                                class="btn d-inline-block bg-info-400 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-info-500"
                                                                class="btn d-inline-block bg-info-500 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-info-600"
                                                                class="btn d-inline-block bg-info-600 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-info-700"
                                                                class="btn d-inline-block bg-info-700 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-info-800"
                                                                class="btn d-inline-block bg-info-800 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-info-900"
                                                                class="btn d-inline-block bg-info-900 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-danger-100"
                                                                class="btn d-inline-block bg-danger-100 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-danger-200"
                                                                class="btn d-inline-block bg-danger-200 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-danger-300"
                                                                class="btn d-inline-block bg-danger-300 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-danger-400"
                                                                class="btn d-inline-block bg-danger-400 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-danger-500"
                                                                class="btn d-inline-block bg-danger-500 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-danger-600"
                                                                class="btn d-inline-block bg-danger-600 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-danger-700"
                                                                class="btn d-inline-block bg-danger-700 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-danger-800"
                                                                class="btn d-inline-block bg-danger-800 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-danger-900"
                                                                class="btn d-inline-block bg-danger-900 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-warning-100"
                                                                class="btn d-inline-block bg-warning-100 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-warning-200"
                                                                class="btn d-inline-block bg-warning-200 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-warning-300"
                                                                class="btn d-inline-block bg-warning-300 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-warning-400"
                                                                class="btn d-inline-block bg-warning-400 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-warning-500"
                                                                class="btn d-inline-block bg-warning-500 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-warning-600"
                                                                class="btn d-inline-block bg-warning-600 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-warning-700"
                                                                class="btn d-inline-block bg-warning-700 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-warning-800"
                                                                class="btn d-inline-block bg-warning-800 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-warning-900"
                                                                class="btn d-inline-block bg-warning-900 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-fusion-100"
                                                                class="btn d-inline-block bg-fusion-100 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-fusion-200"
                                                                class="btn d-inline-block bg-fusion-200 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-fusion-300"
                                                                class="btn d-inline-block bg-fusion-300 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-fusion-400"
                                                                class="btn d-inline-block bg-fusion-400 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-fusion-500"
                                                                class="btn d-inline-block bg-fusion-500 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-fusion-600"
                                                                class="btn d-inline-block bg-fusion-600 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-fusion-700"
                                                                class="btn d-inline-block bg-fusion-700 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-fusion-800"
                                                                class="btn d-inline-block bg-fusion-800 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-fusion-900"
                                                                class="btn d-inline-block bg-fusion-900 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg=""
                                                                class="btn d-inline-block bg-white border width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="dropdown-multilevel dropdown-multilevel-left">
                                                    <div class="dropdown-item">
                                                        thead color
                                                    </div>
                                                    <div class="dropdown-menu no-transition-delay">
                                                        <div class="js-thead-colors dropdown-multilevel dropdown-multilevel-left d-flex flex-wrap"
                                                            style="width: 15.9rem; padding: 0.5rem">
                                                            <a href="javascript:void(0);" data-bg="bg-primary-100"
                                                                class="btn d-inline-block bg-primary-100 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-primary-200"
                                                                class="btn d-inline-block bg-primary-200 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-primary-300"
                                                                class="btn d-inline-block bg-primary-300 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-primary-400"
                                                                class="btn d-inline-block bg-primary-400 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-primary-500"
                                                                class="btn d-inline-block bg-primary-500 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-primary-600"
                                                                class="btn d-inline-block bg-primary-600 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-primary-700"
                                                                class="btn d-inline-block bg-primary-700 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-primary-800"
                                                                class="btn d-inline-block bg-primary-800 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-primary-900"
                                                                class="btn d-inline-block bg-primary-900 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-success-100"
                                                                class="btn d-inline-block bg-success-100 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-success-200"
                                                                class="btn d-inline-block bg-success-200 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-success-300"
                                                                class="btn d-inline-block bg-success-300 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-success-400"
                                                                class="btn d-inline-block bg-success-400 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-success-500"
                                                                class="btn d-inline-block bg-success-500 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-success-600"
                                                                class="btn d-inline-block bg-success-600 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-success-700"
                                                                class="btn d-inline-block bg-success-700 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-success-800"
                                                                class="btn d-inline-block bg-success-800 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-success-900"
                                                                class="btn d-inline-block bg-success-900 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-info-100"
                                                                class="btn d-inline-block bg-info-100 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-info-200"
                                                                class="btn d-inline-block bg-info-200 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-info-300"
                                                                class="btn d-inline-block bg-info-300 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-info-400"
                                                                class="btn d-inline-block bg-info-400 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-info-500"
                                                                class="btn d-inline-block bg-info-500 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-info-600"
                                                                class="btn d-inline-block bg-info-600 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-info-700"
                                                                class="btn d-inline-block bg-info-700 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-info-800"
                                                                class="btn d-inline-block bg-info-800 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-info-900"
                                                                class="btn d-inline-block bg-info-900 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-danger-100"
                                                                class="btn d-inline-block bg-danger-100 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-danger-200"
                                                                class="btn d-inline-block bg-danger-200 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-danger-300"
                                                                class="btn d-inline-block bg-danger-300 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-danger-400"
                                                                class="btn d-inline-block bg-danger-400 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-danger-500"
                                                                class="btn d-inline-block bg-danger-500 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-danger-600"
                                                                class="btn d-inline-block bg-danger-600 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-danger-700"
                                                                class="btn d-inline-block bg-danger-700 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-danger-800"
                                                                class="btn d-inline-block bg-danger-800 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-danger-900"
                                                                class="btn d-inline-block bg-danger-900 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-warning-100"
                                                                class="btn d-inline-block bg-warning-100 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-warning-200"
                                                                class="btn d-inline-block bg-warning-200 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-warning-300"
                                                                class="btn d-inline-block bg-warning-300 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-warning-400"
                                                                class="btn d-inline-block bg-warning-400 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-warning-500"
                                                                class="btn d-inline-block bg-warning-500 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-warning-600"
                                                                class="btn d-inline-block bg-warning-600 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-warning-700"
                                                                class="btn d-inline-block bg-warning-700 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-warning-800"
                                                                class="btn d-inline-block bg-warning-800 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-warning-900"
                                                                class="btn d-inline-block bg-warning-900 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-fusion-100"
                                                                class="btn d-inline-block bg-fusion-100 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-fusion-200"
                                                                class="btn d-inline-block bg-fusion-200 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-fusion-300"
                                                                class="btn d-inline-block bg-fusion-300 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-fusion-400"
                                                                class="btn d-inline-block bg-fusion-400 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-fusion-500"
                                                                class="btn d-inline-block bg-fusion-500 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-fusion-600"
                                                                class="btn d-inline-block bg-fusion-600 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-fusion-700"
                                                                class="btn d-inline-block bg-fusion-700 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-fusion-800"
                                                                class="btn d-inline-block bg-fusion-800 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg="bg-fusion-900"
                                                                class="btn d-inline-block bg-fusion-900 width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                            <a href="javascript:void(0);" data-bg=""
                                                                class="btn d-inline-block bg-white border width-2 height-2 p-0 rounded-0"
                                                                style="margin:1px"></a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="panel-container show">
                                   <span id="ex"> <?php echo $__env->make('admin.layouts.messages', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>              
                                        <div class="panel-content">
                                            <!-- datatable start -->
                                            <table class="table table-bordered table-responsive table-hover table-striped w-100">
                                                <thead class="bg-highlight">
                                                    <tr>
                                                        <th><?php echo e(trans('admin.Arabic_Name')); ?></th>
                                                        <th><?php echo e(trans('admin.English_Name')); ?></th>
                                                        <th><?php echo e(trans('admin.Details')); ?></th>
                                                        <th><?php echo e(trans('admin.Actions')); ?></th>
                                                        
                                                    </tr>
                                                </thead>
                                                <tbody>
                                        <?php $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $r): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>            
                                                    <tr>
                                                        <td><?php echo e($r->name); ?></td>
                                                        <td><?php echo e($r->nameEn); ?></td>
                                                        <td>
                                                            <button type="button" class="btn btn-default" data-toggle="modal" data-target="#default-example-modal-center-open<?php echo e($r->id); ?>">
                                                                <?php echo e(trans('admin.Premations')); ?> 
                                                            </button>
                                                        </td>
                                                        <td class="text-center">
                                                            
                                                
                                                 <?php if($r->name != 'Owner'): ?>
                                                             <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('حذف صلاحيه')): ?> 
                                                            <button type="button" class="btn btn-default" data-toggle="modal" data-target="#default-example-modal-center<?php echo e($r->id); ?>"><i class="fal fa-trash"></i></button>
                                                        <?php endif; ?>    
                                                              
                                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تعديل صلاحيه')): ?>         
                                                            <button type="button" class="btn btn-default" data-toggle="modal" data-target="#default-example-modal-lg-edit<?php echo e($r->id); ?>"><i class="fal fa-edit"></i></button>
                                                        <?php endif; ?>    
                                   <?php endif; ?>
                                                            
                                                            
                    <?php if($r->name == 'Owner' and auth()->guard('admin')->user()->email == '<EMAIL>'): ?>
                                                            
                                             <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('حذف صلاحيه')): ?> 
                                                            <button type="button" class="btn btn-default" data-toggle="modal" data-target="#default-example-modal-center<?php echo e($r->id); ?>"><i class="fal fa-trash"></i></button>
                                                        <?php endif; ?>    
                                                              
                                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تعديل صلاحيه')): ?>         
                                                            <button type="button" class="btn btn-default" data-toggle="modal" data-target="#default-example-modal-lg-edit<?php echo e($r->id); ?>"><i class="fal fa-edit"></i></button>
                                                        <?php endif; ?>                              
                                                            
                                            <?php endif; ?>                
                                                            
                                                            
                                                            
                                                        </td>
                                                    </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </tbody>
                                                <tfoot>
                                                    <tr>
                                                       <th><?php echo e(trans('admin.Arabic_Name')); ?></th>
                                                       <th><?php echo e(trans('admin.English_Name')); ?></th>
                                                        <th><?php echo e(trans('admin.Details')); ?></th>
                                                        <th><?php echo e(trans('admin.Actions')); ?></th>
                                                    </tr>
                                                </tfoot>
                                            </table>
                                            <?php echo e($roles->Links()); ?>

                                            <!-- datatable end -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
     
                        <!-- Modal Add-->
                        <div class="modal fade" id="default-example-modal-lg" tabindex="-1" role="dialog" aria-hidden="true">
                            <div class="modal-dialog modal-xl" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title"><?php echo e(trans('admin.AddNew')); ?>  </h5>
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                            <span aria-hidden="true"><i class="fal fa-times"></i></span>
                                        </button>
                                    </div>
                                    
                                 
                                    <div class="modal-body">
                             <form action="<?php echo e(url('AddPrem')); ?>" method="post">
                                 <?php echo csrf_field(); ?>

                                            <div class="form-row">
                                                
                                    
                                                  <div class="col-md-6">
                                 <label><?php echo e(trans('admin.Arabic_Name')); ?></label>     
                          <?php echo Form::text('name', null, array('class' => 'form-control')); ?>                    
                                                </div>   
                                                
                                               <div class="col-md-6">        
                                           <label><?php echo e(trans('admin.English_Name')); ?></label>          
                          <?php echo Form::text('nameEn', null, array('class' => 'form-control')); ?>            
                                                </div>
    
                                                
                            <div class="form-group col-lg-12">
                        <label><?php echo e(trans('admin.Premations')); ?></label> <br>
                        <label><?php echo e(trans('admin.All')); ?>

                                
              <input type="checkbox" id="checkbox" onclick="CheckAll()">
       
                        </label> 
                       
                            
                <?php if(auth()->guard('admin')->user()->email == '<EMAIL>'): ?>                
                     <div class="row">
                            <?php $__currentLoopData = $permission; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                         <?php if($value->Parent == 0): ?>
                    <?php if(auth()->guard('admin')->user()->email == '<EMAIL>'): ?>     
             <?php     $CHILDs=DB::table('permissions')->where('Parent',$value->Main)->get(); ?>   
                    <?php else: ?>
               <?php     $CHILDs=DB::table('permissions')->where('Parent',$value->Main)->where('Package',auth()->guard('admin')->user()->package)->get(); ?>                
                         
                    <?php endif; ?>     
                           <div class="col-md-12 mt-2 mb-2" style="color: #463a61;font-size: 35px;">
                                    <label>
                                 <?php echo e(app()->getLocale() == 'ar' ?$value->name :$value->nameEn); ?>   
                               </label>      
                <input type="checkbox" name="permission[]" onclick="CheckModule(<?php echo e($value->Main); ?>)" value="<?php echo e($value->id); ?>" class="name" id="n<?php echo e($value->Main); ?>" >    
              <i class="fal fa-times" style="font-size: 17px;"></i>  <input type="checkbox"  onclick="UnCheckModule(<?php echo e($value->Main); ?>)" class="name" id="UN<?php echo e($value->Main); ?>" >   
                         </div>
                         
                         <div class="container">
                         <div class="row" style="border: 1px solid #886ab5;padding: 24px; border-radius:20px;">
                         <?php $__currentLoopData = $CHILDs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $child): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="col-md-3">
                                    <label style="font-size: 20px;">
                                    
                             <?php echo e(app()->getLocale() == 'ar' ?$child->name :$child->nameEn); ?>        
                                    </label>      
                 <input type="checkbox" name="permission[]" value="<?php echo e($child->id); ?>" class="name nn<?php echo e($child->Parent); ?>">    
                         </div>
                         <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>     
                        </div>     
                         <hr>
                         
                         <?php endif; ?>
                    
                    <!--    <?php echo e(Form::checkbox('permission[]', $value->id, false, array('class' => 'name'))); ?> 
                <?php echo e($value->name); ?>               
            
                              -->

                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div> 
                                
                 <?php else: ?>
                                
                       <div class="row">
                           
                           <?php         
             $permissionS = PackPrem::where('package',auth()->guard('admin')->user()->package)->get();         
            ?>       
                             
                            <?php $__currentLoopData = $permissionS; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                         <?php if($value->premission()->first()->Parent == 0): ?>
              
                           <div class="col-md-12 mt-2 mb-2" style="color: #463a61;font-size: 35px;">
                                    <label>
                                 <?php echo e(app()->getLocale() == 'ar' ?$value->premission()->first()->name :$value->premission()->first()->nameEn); ?>   
                               </label>      
                <input type="checkbox" name="permission[]" onclick="CheckModule(<?php echo e($value->premission()->first()->Main); ?>)" value="<?php echo e($value->premission); ?>" class="name" id="n<?php echo e($value->premission()->first()->Main); ?>" >    
              <i class="fal fa-times" style="font-size: 17px;"></i>  <input type="checkbox"  onclick="UnCheckModule(<?php echo e($value->premission()->first()->Main); ?>)" class="name" id="UN<?php echo e($value->premission()->first()->Main); ?>" >   
                         </div>
                         
                        <?php else: ?>   
                           
            
                                <div class="col-md-4 mt-2 mb-2">
                                    <label style="font-size: 20px;">
                                    
                            <?php echo e(app()->getLocale() == 'ar' ?$value->premission()->first()->name :$value->premission()->first()->nameEn); ?>         
                                    </label>      
                 <input type="checkbox" name="permission[]" value="<?php echo e($value->premission); ?>" class="name nn<?php echo e($value->premission()->first()->Parent); ?>">    
                         </div>
                        
                      
                         <hr>
                         
                         <?php endif; ?>
                    
                    <!--    <?php echo e(Form::checkbox('permission[]', $value->id, false, array('class' => 'name'))); ?> 
                <?php echo e($value->name); ?>               
            
                              -->

                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>           
                    <?php endif; ?>            
                    </div>
    
                                                
                                            </div>
                                 
                                 <div class="form-row">
                         <button type="submit" class="btn btn-primary"><?php echo e(trans('admin.Save')); ?></button>
                                 </div>
                                 
                                          </form> 
                           
                                    </div>
                                    
            
                                </div>
                            </div>
                        </div>
     
     
     
          <?php $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $r): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>   
                        <!-- Modal Edit-->
                        <div class="modal fade" id="default-example-modal-lg-edit<?php echo e($r->id); ?>" tabindex="-1" role="dialog" aria-hidden="true">
                            <div class="modal-dialog modal-xl" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title"><?php echo e(trans('admin.Edit')); ?>  </h5>
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                            <span aria-hidden="true"><i class="fal fa-times"></i></span>
                                        </button>
                                    </div>
                                    <div class="modal-body">
                                       <form action="<?php echo e(url('EditPrem/'.$r->id)); ?>" method="post">
                                 <?php echo csrf_field(); ?>

                                            <div class="form-row">
                                      
                                                        <div class="col-md-6">
                  <label><?php echo e(trans('admin.Arabic_Name')); ?></label> 
                   <?php echo Form::text('name', $r->name, array('class' => 'form-control')); ?>            
                                                </div>
                                
                                    
                                                        <div class="col-md-6">
    <label><?php echo e(trans('admin.English_Name')); ?></label>    
                                                
                              <?php echo Form::text('nameEn', $r->nameEn, array('class' => 'form-control')); ?>                           
                                                </div> 
                            <div class="form-group col-lg-12">
                   <label><?php echo e(trans('admin.Premations')); ?></label> 
                             <label><?php echo e(trans('admin.All')); ?>

                                
              <input type="checkbox" id="checkbox<?php echo e($r->id); ?>" onclick="CheckAllEdit(<?php echo e($r->id); ?>)">
                            
        
                        </label>        
                                
                                
                                
          
                                
                     <?php if(auth()->guard('admin')->user()->email == '<EMAIL>'): ?>              
                                <div class="row">
                                    
                                    
                
                                    
                            <?php $__currentLoopData = $permission; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php if($value->Parent == 0): ?>
                                    
                      <?php if(auth()->guard('admin')->user()->email == '<EMAIL>'): ?>     
             <?php     $CHILDs=DB::table('permissions')->where('Parent',$value->Main)->get(); ?>   
                    <?php else: ?>
               <?php     $CHILDs=DB::table('permissions')->where('Parent',$value->Main)->get(); ?>                
                         
                    <?php endif; ?>  
                                    
                        <div class="col-md-12 mt-2 mb-2" style="color: #463a61;font-size: 35px;">
                                                             <?php
$rolePermissions = DB::table("role_has_permissions")->where("role_has_permissions.role_id",$r->id)
->pluck('role_has_permissions.permission_id','role_has_permissions.permission_id')
->all();        
                                ?>  
                                
                             
                           
<?php echo e(Form::checkbox('permission[]', $value->id, in_array($value->id, $rolePermissions) ? true : false, array('class' => 'nameEdit'))); ?>                    
  
                               <label>     <?php echo e(app()->getLocale() == 'ar' ?$value->name :$value->nameEn); ?>   </label>   
                            
                             <input type="checkbox" name="permission[]" onclick="CheckModuleEdit(<?php echo e($value->Main); ?>)" value="<?php echo e($value->id); ?>" class="name" id="nEdit<?php echo e($value->Main); ?>" >    
              <i class="fal fa-times" style="font-size: 17px;"></i>  <input type="checkbox"  onclick="UnCheckModuleEdit(<?php echo e($value->Main); ?>)" class="name" id="UNEdit<?php echo e($value->Main); ?>" >       
                             </div>
    
                             <div class="container">
                         <div class="row" style="border: 3px solid #453961;padding: 24px;">            
                       <?php $__currentLoopData = $CHILDs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $child): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="col-md-4">     
                                    
    <?php echo e(Form::checkbox('permission[]', $child->id, in_array($child->id, $rolePermissions) ? true : false, array('class' => 'nameEdit nnEdit'.$child->Parent))); ?>               
             <label style="font-size: 20px;">   <?php echo e(app()->getLocale() == 'ar' ?$child->name :$child->nameEn); ?>      </label>                            
                             </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>               
                                 </div>        
                                 </div>        
                                    
                                    <?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                                
                            <?php else: ?>
                            
                 <?php         
             $permissionS = PackPrem::where('package',auth()->guard('admin')->user()->package)->get();         
            ?>       
                                
                                
               <div class="row">
                                    
                                    
                
                                    
                            <?php $__currentLoopData = $permissionS; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php if($value->premission()->first()->Parent == 0): ?>
     
                                    
                        <div class="col-md-12 mt-2 mb-2" style="color: #463a61;font-size: 35px;">
                                                             <?php
$rolePermissions = DB::table("role_has_permissions")->where("role_has_permissions.role_id",$r->id)
->pluck('role_has_permissions.permission_id','role_has_permissions.permission_id')
->all();        
                                ?>  
                            
                            
                            
                                
<?php echo e(Form::checkbox('permission[]', $value->premission, in_array($value->premission, $rolePermissions) ? true : false, array('class' => 'nameEdit'))); ?>                    
  
                               <label>     <?php echo e(app()->getLocale() == 'ar' ?$value->premission()->first()->name :$value->premission()->first()->nameEn); ?>   </label>   
                            
       <input type="checkbox" name="permission[]" onclick="CheckModuleEdit(<?php echo e($value->premission()->first()->Main); ?>)" value="<?php echo e($value->premission); ?>" class="name" id="nEdit<?php echo e($value->premission()->first()->Main); ?>" >    
    <i class="fal fa-times" style="font-size: 17px;"></i>  <input type="checkbox"  onclick="UnCheckModuleEdit(<?php echo e($value->premission()->first()->Main); ?>)" class="name" id="UNEdit<?php echo e($value->premission()->first()->Main); ?>" >   

                             </div>

<?php else: ?>
                             <div class="col-md-4 mt-2 mb-2">
            
                       
                                    
<?php echo e(Form::checkbox('permission[]', $value->premission, in_array($value->premission, $rolePermissions) ? true : false, array('class' => 'nameEdit nnEdit'.$value->premission()->first()->Parent))); ?>            
             <label style="font-size: 20px;">   <?php echo e(app()->getLocale() == 'ar' ?$value->premission()->first()->name :$value->premission()->first()->nameEn); ?>     </label>                            
                           
                                          
                                   
                                 </div>        
                                    
                                    <?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>                    
                                
                                
                        <?php endif; ?>        
                                
                    
                    </div>
    
                                                
                                            </div>
                                 
                                 <div class="form-row">
                         <button type="submit" class="btn btn-primary"><?php echo e(trans('admin.SaveChanges')); ?></button>
                                 </div>
                                 
                                          </form> 
                                        
                                    </div>

                                </div>
                            </div>
                        </div>
     
     
                          <!-- Modal Delete -->
                          <div class="modal fade" id="default-example-modal-center<?php echo e($r->id); ?>" tabindex="-1" role="dialog" aria-hidden="true">
                            <div class="modal-dialog modal-dialog-centered" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h4 class="modal-title">
                                             <?php echo e(trans('admin.RUSWDT')); ?> <strong>
                                            
                                             <?php echo e(app()->getLocale() == 'ar' ?$r->name :$r->nameEn); ?>

                                            </strong>
                                        </h4>
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                            <span aria-hidden="true"><i class="fal fa-times"></i></span>
                                        </button>
                                    </div>
                                    
                                    <div class="modal-footer">
                       <button type="button" class="btn btn-secondary" data-dismiss="modal"> <?php echo e(trans('admin.No')); ?></button>
                   <a href="<?php echo e(url('DeletePrem/'.$r->id)); ?>"  class="btn btn-primary"> <?php echo e(trans('admin.Yes')); ?></a>
                                    </div>
                                </div>
                            </div>
                        </div>
     
     
                        <!--   Model Permission -->
                        <div class="modal fade" id="default-example-modal-center-open<?php echo e($r->id); ?>" tabindex="-1" role="dialog"
                        aria-hidden="true">
                        <div class="modal-dialog modal-dialog-centered modal-xl" role="document">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h4 class="modal-title">
                                       <?php echo e(trans('admin.Premations')); ?>

                                    </h4>
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                        <span aria-hidden="true"><i class="fal fa-times"></i></span>
                                    </button>
                                </div>
                                <div class="modal-body">
                                <?php
                                    
            if($Modules->Capital == 1){
                  $cR=15;
              }else{
                 $cR=null; 
                  
              } 
                                    
                                    
          if($Modules->Petrol == 1){
                  $Pet=17;
              }else{
                 $Pet=null; 
                  
              }         
                                    

            if($Modules->Accounts == 1){
                  $AR=4;
                  $ARR=8;
              }else{
                 $AR=null; 
                 $ARR=null; 
                  
              } 
          
            if($Modules->Stores == 1){
                  $SR=2;
                  $SRR=3;
                  $SRRR=9;
                  $SRRRR=6;
                  $SRRRRR=7;
              }else{
                  $SR=null;
                  $SRR=null;
                  $SRRR=null;
                  $SRRRR=null;
                  $SRRRRR=null;
                  
              } 
          
            if($Modules->CRM == 1){
                  $cmR=5;
              }else{
                 $cmR=null; 
                  
              } 
          
            if($Modules->HR == 1){
                  $hR=10;
              }else{
                 $hR=null; 
                  
              } 
          
            if($Modules->Manufacturing == 1){
                  $mnR=14;
              }else{
                 $mnR=null; 
                  
              } 
          
            if($Modules->Maintenance == 1){
                  $mR=13;
              }else{
                 $mR=null; 
                  
              } 
          
          
            if($Modules->Secretariat == 1){
                  $SCR=16;
              }else{
                 $SCR=null; 
                  
              } 
  
                    if($Modules->Petrol == 1){
                  $PPP=17;
              }else{
                 $PPP=null; 
                  
              } 
          
            if($Modules->ECommerce == 1){
                  $ECOM=18;
              }else{
                 $ECOM=null; 
                  
              } 
                if($Modules->Shipping == 1){
                  $SH=19;
              }else{
                 $SH=null; 
                  
              }  
          
           if($Modules->Bill_Electronic == 1){
                  $EB=20;
              }else{
                 $EB=null; 
                  
              }  
          
          
           if($Modules->Hotels == 1){
                  $H=21;
              }else{
                 $H=null; 
                  
              }       
                                    
         if($Modules->HR == 1){
                  $hR=10;
                  $hRReport=22;
              }else{
                 $hR=null; 
                 $hRReport=null; 
                  
              } 
                                    
            if($Modules->Resturant == 1){
                  $Rst=23;
              }else{
                 $Rst=null; 
                  
              }  
                                    
                                                 if($Modules->Traning_Center == 1){
                 $traning=24;
              }else{
                 $traning=null; 
                  
              }  
          
                                              
             if($Modules->Translate == 1){
                 $trans=25;
              }else{
                 $trans=null; 
                  
              }  
          
                                    
             if(auth()->guard('admin')->user()->email == '<EMAIL>'){      
                                    
 $rolePermissions = Permission::join("role_has_permissions","role_has_permissions.permission_id","=","permissions.id")
->where("role_has_permissions.role_id",$r->id)
    ->whereIn('Main',[1,11,12,$cR,$AR,$ARR,$SR,$SRR,$SRRR,$SRRRR,$SRRRRR,$cmR,$hR,$mnR,$mR,$SCR,$PPP,$ECOM,$SH,$EB,$H,$hRReport,$Rst,$traning,$trans])
->get();   
                                    
                                    }
                                ?>    
                                    
                                    
                        <?php if(auth()->guard('admin')->user()->email == '<EMAIL>'): ?>            
                                     <?php if(!empty($rolePermissions)): ?>
                                    <?php $__currentLoopData = $rolePermissions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $v): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                      
                                                 <?php if($v->Parent == 0): ?>
                                    <div class="row">
                                    <div class="col-md-12 mt-2 mb-2" style="font-size: 30px; color: darkred">
                                     
                                        
                             <?php echo e(app()->getLocale() == 'ar' ?$v->name :$v->nameEn); ?>            
                                        </div>
                                    </div>
                                    <div class="container">
   <div class="row">

       
                   <?php if(auth()->guard('admin')->user()->email == '<EMAIL>'): ?>     
             <?php     $CHILDs=DB::table('permissions')->where('Parent',$v->Main)->get(); ?>   
                    <?php else: ?>
               <?php     $CHILDs=DB::table('permissions')->where('Parent',$v->Main)->where('Package',auth()->guard('admin')->user()->package)->get(); ?>                
                         
                    <?php endif; ?>   
    <?php $__currentLoopData = $CHILDs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $child): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                             
                                    <div class="col-md-4 mt-2 mb-2" style="font-size: 20px; color: darkblue">                    
                                
                                      <?php echo e(app()->getLocale() == 'ar' ?$child->name :$child->nameEn); ?>      
                                   </div>
                                 
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>                            
                     </div>
                     </div>
                                    <?php endif; ?>
     
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <?php endif; ?>
                               

                                    <?php else: ?>
                                    
                                         <?php         
             $permissionS = PackPrem::where('package',auth()->guard('admin')->user()->package)->get();         
            ?>       
                                
                                
               <div class="row">
                                    
                                    
                
                                    
                            <?php $__currentLoopData = $permissionS; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php if($value->premission()->first()->Parent == 0): ?>
     
                                    
                        <div class="col-md-12 mt-2 mb-2" style="color: #463a61;font-size: 35px;">
                                                             <?php
$rolePermissions = DB::table("role_has_permissions")->where("role_has_permissions.role_id",$r->id)
->pluck('role_has_permissions.permission_id','role_has_permissions.permission_id')
->all();        
                                ?>  
                                
<?php echo e(Form::checkbox('permission[]', $value->premission, in_array($value->premission, $rolePermissions) ? true : false, array('class' => 'nameEdit','disabled'))); ?>                    
  
                               <label>     <?php echo e(app()->getLocale() == 'ar' ?$value->premission()->first()->name :$value->premission()->first()->nameEn); ?>   </label>   
                             </div>

<?php else: ?>
                             <div class="col-md-4 mt-2 mb-2">
            
                       
                                    
<?php echo e(Form::checkbox('permission[]', $value->premission, in_array($value->premission, $rolePermissions) ? true : false, array('class' => 'nameEdit','disabled'))); ?>            
             <label style="font-size: 20px;">   <?php echo e(app()->getLocale() == 'ar' ?$value->premission()->first()->name :$value->premission()->first()->nameEn); ?>     </label>                            
                           
                                          
                                   
                                 </div>        
                                    
                                    <?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>                    
                                
                 
                                    
                                <?php endif; ?>    
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-dismiss="modal"><?php echo e(trans('admin.Close')); ?></button>
                                </div>
                            </div>
                        </div>
                    </div>
                            </div>
            


     <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
     
                    </main>
     
<?php $__env->stopSection(); ?>

     <?php $__env->startPush('js'); ?>
         <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/datagrid/datatables/datatables.bundle.css')); ?>">
    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/formplugins/select2/select2.bundle.css')); ?>">
    <script src="<?php echo e(asset('Admin/js/formplugins/select2/select2.bundle.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/datagrid/datatables/datatables.export.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/datagrid/datatables/datatables.bundle.js')); ?>"></script>


<script>
        //_fnFeatureHtmlLength();
        $(document).ready(function () {
            // Setup - add a text input to each footer cell
            $('#dt-basic-example thead tr').clone(true).appendTo('#dt-basic-example thead');
            $('#dt-basic-example thead tr:eq(1) th').each(function (i) {
                var title = $(this).text();
                $(this).html('<input type="text" class="form-control form-control-sm" placeholder="Search ' + title + '" />');

                $('input', this).on('keyup change', function () {
                    if (table.column(i).search() !== this.value) {
                        table
                            .column(i)
                            .search(this.value)
                            .draw();
                    }
                });
            });
            var table = $('#dt-basic-example').DataTable(
                {
                    responsive: true,
                    orderCellsTop: true,
                    fixedHeader: true,
                    lengthChange: true,

                    dom: "<'row mb-3'<'col-sm-12 col-md-3 d-flex align-items-center justify-content-start'f><'col-sm-12 col-md-9 d-flex align-items-center justify-content-end'B>>" +
                        "<'row'<'col-sm-12'tr>>" +
                        "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",

                    buttons: [
                        {
                            extend: 'pageLength',
                            className: 'btn-outline-default'
                        },
                        {
                            extend: 'colvis',
                            text: 'Column Visibility',
                            titleAttr: 'Col visibility',
                            className: 'btn-outline-default'
                        },
                        {
                            extend: 'pdfHtml5',
                            text: 'PDF',
                            titleAttr: 'Generate PDF',
                            className: 'btn-outline-danger btn-sm mr-1'
                        },
                        {
                            extend: 'excelHtml5',
                            text: 'Excel',
                            titleAttr: 'Generate Excel',
                            className: 'btn-outline-success btn-sm mr-1'
                        },
                        {
                            extend: 'csvHtml5',
                            text: 'CSV',
                            titleAttr: 'Generate CSV',
                            className: 'btn-outline-primary btn-sm mr-1'
                        },
                        {
                            extend: 'copyHtml5',
                            text: 'Copy',
                            titleAttr: 'Copy to clipboard',
                            className: 'btn-outline-primary btn-sm mr-1'
                        },
                        {
                            extend: 'print',
                            text: 'Print',
                            titleAttr: 'Print Table',
                            className: 'btn-outline-primary btn-sm'
                        }
                    ],
                });
            $('.js-thead-colors a').on('click', function () {
                var theadColor = $(this).attr("data-bg");
                console.log(theadColor);
                $('#dt-basic-example thead').removeClassPrefix('bg-').addClass(theadColor);
            });

            $('.js-tbody-colors a').on('click', function () {
                var theadColor = $(this).attr("data-bg");
                console.log(theadColor);
                $('#dt-basic-example').removeClassPrefix('bg-').addClass(theadColor);
            });

        });

    </script>
     

<script>
    function CheckAll() {  
                             if($("#checkbox").prop("checked"))
                                {
                                    $(".name").prop("checked",true);
                                   
                                    
                                }else 
                                if($("#checkbox").prop("checked",false))    
                                {
                                 
                                    $(".name").prop("checked",false);
                                  
                                    
                                }       
                                   }
       function CheckModule(r) {  
                             if($("#n"+r).prop("checked"))
                                {
                                    $(".nn"+r).prop("checked",true);
                                   
                                    
                                }else 
                                if($("#n"+r).prop("checked",false))    
                                {
                                 
                                    $(".nn"+r).prop("checked",false);
                                  
                                    
                                }       
                                   }
     function UnCheckModule(r) {  
                             if($("#UN"+r).prop("checked"))
                                {
                                    $(".nn"+r).prop("checked",false);
                                   
                                    
                                }      
                                   }
      function CheckAllEdit(r) {  
                             if($("#checkbox"+r).prop("checked"))
                                {
                                    $(".nameEdit").prop("checked",true);
                                   
                                    
                                }else 
                                if($("#checkbox"+r).prop("checked",false))    
                                {
                                 
                                    $(".nameEdit").prop("checked",false);
                                  
                                    
                                }       
                                   }
    
    
       function CheckModuleEdit(r) {  
                             if($("#nEdit"+r).prop("checked"))
                                {
                                    $(".nnEdit"+r).prop("checked",true);
                                   
                                    
                                }else 
                                if($("#nEdit"+r).prop("checked",false))    
                                {
                                 
                                    $(".nnEdit"+r).prop("checked",true);
                                  
                                    
                                }             
                                   }
     function UnCheckModuleEdit(r) {  
                             if($("#UNEdit"+r).prop("checked"))
                                {
                                    $(".nnEdit"+r).prop("checked",false);
                                   
                                    
                                } else 
                                if($("#nEdit"+r).prop("checked",true))    
                                {
                                 
                                    $(".nnEdit"+r).prop("checked",false);
                                  
                                    
                                }            
                                   }
    
                    </script>     
    
 
     <?php $__env->stopPush(); ?>
     
<?php echo $__env->make('admin.index', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp2\htdocs\erp\resources\views/admin/AdminsPremations.blade.php ENDPATH**/ ?>