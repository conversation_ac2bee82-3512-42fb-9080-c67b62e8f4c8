<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSocialMediaTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('social_media', function (Blueprint $table) {
            $table->id();
            
            // Social media links
            $table->string('Facebook')->nullable();
            $table->string('Twitter')->nullable();
            $table->string('Instagram')->nullable();
            $table->string('LinkedIn')->nullable();
            $table->string('YouTube')->nullable();
            $table->string('WhatsApp')->nullable();
            $table->string('Telegram')->nullable();
            $table->string('Snapchat')->nullable();
            $table->string('TikTok')->nullable();
            $table->string('Pinterest')->nullable();
            $table->string('Discord')->nullable();
            $table->string('Reddit')->nullable();
            
            // Social media settings
            $table->string('Status')->default('1');
            $table->string('Show_In_Header')->default('1');
            $table->string('Show_In_Footer')->default('1');
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('social_media');
    }
}
