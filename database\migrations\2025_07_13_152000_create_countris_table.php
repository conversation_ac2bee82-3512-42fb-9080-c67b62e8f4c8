<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCountrisTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('countris', function (Blueprint $table) {
            $table->id();
            
            // Country information
            $table->string('Arabic_Name')->nullable();
            $table->string('English_Name')->nullable();
            $table->string('Flag')->nullable();
            $table->string('Code')->nullable();
            $table->string('SearchCode')->nullable();
            
            // Default references
            $table->string('Safe')->nullable();  // Default safe for this country
            $table->string('Coin')->nullable();  // Default currency
            $table->string('Store')->nullable(); // Default store
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('countris');
    }
}
