<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateHomeProductEComDesignsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('home_product_e_com_designs', function (Blueprint $table) {
            $table->id();
            $table->string('Special_Offer_Title_BG_Color')->nullable();
            $table->string('Special_Offer_Title_Txt_Color')->nullable();
            $table->string('Special_Offer_Product_BG_Color')->nullable();
            $table->string('Special_Offer_Product_Border_Color')->nullable();
            $table->string('Special_Offer_Product_Txt_Color')->nullable();
            $table->string('Special_Offer_Product_Txt_Hover_Color')->nullable();
            $table->string('Special_Offer_Product_Price_Color')->nullable();
            $table->string('Special_Offer_Product_Rate_Color')->nullable();
            $table->string('Best_Sellers_Title_BG_Color')->nullable();
            $table->string('Best_Sellers_Title_Txt_Color')->nullable();
            $table->string('Best_Sellers_Category_Txt_Color')->nullable();
            $table->string('Best_Sellers_Category_Active_Txt_Color')->nullable();
            $table->string('Best_Sellers_Product_BG_Color')->nullable();
            $table->string('Best_Sellers_Product_Group_BG_Color')->nullable();
            $table->string('Best_Sellers_Product_Group_Txt_Color')->nullable();
            $table->string('Best_Sellers_Product_Group_Hover_BG_Color')->nullable();
            $table->string('Best_Sellers_Product_Group_Hover_Txt_Color')->nullable();
            $table->string('Best_Sellers_Product_Icon_BG_Color')->nullable();
            $table->string('Best_Sellers_Product_Icon_Txt_Color')->nullable();
            $table->string('Best_Sellers_Product_Icon_Hover_BG_Color')->nullable();
            $table->string('Best_Sellers_Product_Icon_Hover_Txt_Color')->nullable();
            $table->string('Best_Sellers_Product_Txt_Color')->nullable();
            $table->string('Best_Sellers_Product_Price_Color')->nullable();
            $table->string('Best_Sellers_Product_Rate_Color')->nullable();
            $table->string('New_Arrivals_Title_BG_Color')->nullable();
            $table->string('New_Arrivals_Title_Txt_Color')->nullable();
            $table->string('New_Arrivals_Product_BG_Color')->nullable();
            $table->string('New_Arrivals_Product_Group_BG_Color')->nullable();
            $table->string('New_Arrivals_Product_Group_Txt_Color')->nullable();
            $table->string('New_Arrivals_Product_Group_Hover_BG_Color')->nullable();
            $table->string('New_Arrivals_Product_Group_Hover_Txt_Color')->nullable();
            $table->string('New_Arrivals_Product_Icon_BG_Color')->nullable();
            $table->string('New_Arrivals_Product_Icon_Txt_Color')->nullable();
            $table->string('New_Arrivals_Product_Icon_Hover_BG_Color')->nullable();
            $table->string('New_Arrivals_Product_Icon_Hover_Txt_Color')->nullable();
            $table->string('New_Arrivals_Product_Txt_Color')->nullable();
            $table->string('New_Arrivals_Product_Price_Color')->nullable();
            $table->string('New_Arrivals_Product_Hover_Price_Color')->nullable();
            $table->string('New_Arrivals_Product_Rate_Color')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('home_product_e_com_designs');
    }
}
