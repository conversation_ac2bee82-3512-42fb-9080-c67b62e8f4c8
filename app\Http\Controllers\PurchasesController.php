<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\PurchasesDefaultData;
use App\Models\UsersMoves;
use App\Models\Countris;
use App\Models\AcccountingManual;
use App\Models\Admin;
use App\Models\Governrate;
use App\Models\City;
use App\Models\Places;
use App\Models\Taxes;
use App\Models\Vendors;
use App\Models\Employess;
use App\Models\CostCenter;
use App\Models\Coins;
use App\Models\Stores;
use App\Models\ProductsQty;
use App\Models\ProductUnits;
use App\Models\Measuerments;
use App\Models\PurchasesOrder;
use App\Models\ProductsPurchasesOrder;
use App\Models\Purchases;
use App\Models\ProductsPurchases;
use App\Models\ProductMoves;
use App\Models\Journalizing;
use App\Models\JournalizingDetails;
use App\Models\GeneralDaily;
use App\Models\ReturnPurches;
use App\Models\ReturnPurchesProducts;
use App\Models\Products;
use App\Models\AssemblyProducts;
use App\Models\ChecksTypes;
use App\Models\Brands;
use App\Models\ItemsGroups;
use App\Models\DefaultDataShowHide;
use App\Models\Branches;
use App\Models\ModuleSettingsNum;
use App\Models\CompanyData;
use Carbon\Carbon;
use DB;
use Auth;

class PurchasesController extends Controller
{
    //Vendors
    public function VendorsPage(){
        $Vendors = Vendors::all();
        $Governrates = Governrate::all();
        $Countris = Countris::all();
        $Employess = Employess::all();
        $items = ItemsGroups::all();
        $Brands = Brands::all();
        $AcccountingManual = AcccountingManual::all();
        $Admin = Admin::all();

        // Generate next vendor code
        $vendorCount = Vendors::count();
        $Code = $vendorCount + 1;

        return view('admin.Purchases.Vendors', compact('Vendors', 'Governrates', 'Countris', 'Employess', 'items', 'Brands', 'AcccountingManual', 'Admin', 'Code'));
    }

    //Purchase Orders
    public function PurchasesOrderPage(){
        $Def = PurchasesDefaultData::first();
        $Stores = Stores::all();
        $Vendors = Vendors::all();
        $Employess = Employess::all();
        $Coins = Coins::all();
        $CostCenter = CostCenter::all();
        $AcccountingManual = AcccountingManual::all();
        $Brands = Brands::all();
        $ItemsGroups = ItemsGroups::all();
        $show = DefaultDataShowHide::first();
        $PurchasesOrder = PurchasesOrder::orderBy('id', 'desc')->limit(1)->first();
        $Branches = Branches::all();
        $ModuleSettingsNum = ModuleSettingsNum::first();
        $CompanyData = CompanyData::first();

        return view('admin.Purchases.PurchasesOrder', compact('Def', 'Stores', 'Vendors', 'Employess', 'Coins', 'CostCenter', 'AcccountingManual', 'Brands', 'ItemsGroups', 'show', 'PurchasesOrder', 'Branches', 'ModuleSettingsNum', 'CompanyData'));
    }

    //Purchase Order Schedule
    public function PurchasesOrderSechdule(){
        $PurchasesOrder = PurchasesOrder::all();
        $Vendors = Vendors::all();
        $Stores = Stores::all();
        $Coins = Coins::all();
        $Employess = Employess::all();

        return view('admin.Purchases.PurchasesOrderSechdule', compact('PurchasesOrder', 'Vendors', 'Stores', 'Coins', 'Employess'));
    }
}
