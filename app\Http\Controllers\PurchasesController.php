<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\PurchasesDefaultData;
use App\Models\UsersMoves;
use App\Models\Countris;
use App\Models\AcccountingManual;
use App\Models\Admin;
use App\Models\Governrate;
use App\Models\City;
use App\Models\Places;
use App\Models\Taxes;
use App\Models\Vendors;
use App\Models\Employess;
use App\Models\CostCenter;
use App\Models\Coins;
use App\Models\Stores;
use App\Models\ProductsQty;
use App\Models\ProductUnits;
use App\Models\Measuerments;
use App\Models\PurchasesOrder;
use App\Models\ProductsPurchasesOrder;
use App\Models\Purchases;
use App\Models\ProductsPurchases;
use App\Models\ProductMoves;
use App\Models\Journalizing;
use App\Models\JournalizingDetails;
use App\Models\GeneralDaily;
use App\Models\ReturnPurches;
use App\Models\ReturnPurchesProducts;
use App\Models\Products;
use App\Models\AssemblyProducts;
use App\Models\ChecksTypes;
use App\Models\Brands;
use App\Models\ItemsGroups;
use App\Models\DefaultDataShowHide;
use App\Models\Branches;
use App\Models\ModuleSettingsNum;
use App\Models\CompanyData;
use App\Models\SafesBanks;
use Carbon\Carbon;
use DB;
use Auth;

class PurchasesController extends Controller
{
    //Vendors
    public function VendorsPage(){
        $Vendors = Vendors::all();
        $Governrates = Governrate::all();
        $Countris = Countris::all();
        $Nationality = Countris::all();  // For nationality dropdowns
        $Employess = Employess::all();
        $items = ItemsGroups::all();
        $Groups = ItemsGroups::all();  // For product group dropdowns
        $Brands = Brands::all();
        $AcccountingManual = AcccountingManual::all();
        $Admin = Admin::all();

        // Generate next vendor code
        $vendorCount = Vendors::count();
        $Code = $vendorCount + 1;

        return view('admin.Purchases.Vendors', compact('Vendors', 'Governrates', 'Countris', 'Nationality', 'Employess', 'items', 'Groups', 'Brands', 'AcccountingManual', 'Admin', 'Code'));
    }

    //Purchase Orders
    public function PurchasesOrderPage(){
        $Def = PurchasesDefaultData::first();
        $Stores = Stores::all();
        $Vendors = Vendors::all();
        $Employess = Employess::all();
        $Coins = Coins::all();
        $CostCenter = CostCenter::all();
        $CostCenters = CostCenter::all();  // For cost center dropdowns
        $AcccountingManual = AcccountingManual::all();
        $Brands = Brands::all();
        $ItemsGroups = ItemsGroups::all();
        $Units = Measuerments::all();  // For unit selection dropdowns
        $ChecksTypes = ChecksTypes::all();  // For check type selection
        $Safes = SafesBanks::all();  // For safe/treasury selection
        $show = DefaultDataShowHide::first();
        $PurchasesOrder = PurchasesOrder::orderBy('id', 'desc')->limit(1)->first();
        $Branches = Branches::all();
        $ModuleSettingsNum = ModuleSettingsNum::first();
        $CompanyData = CompanyData::first();

        // Generate next purchase order code
        $purchaseOrderCount = PurchasesOrder::count();
        $Code = $purchaseOrderCount + 1;

        // Generate next vendor code for new vendor modal
        $vendorCount = Vendors::count();
        $CodeUser = $vendorCount + 1;

        return view('admin.Purchases.PurchasesOrder', compact('Def', 'Stores', 'Vendors', 'Employess', 'Coins', 'CostCenter', 'CostCenters', 'AcccountingManual', 'Brands', 'ItemsGroups', 'Units', 'ChecksTypes', 'Safes', 'show', 'PurchasesOrder', 'Branches', 'ModuleSettingsNum', 'CompanyData', 'Code', 'CodeUser'));
    }

    //Purchase Order Schedule
    public function PurchasesOrderSechdule(){
        $PurchasesOrder = PurchasesOrder::all();
        $items = PurchasesOrder::paginate(15);  // For paginated purchase order listing
        $Vendors = Vendors::all();
        $Stores = Stores::all();
        $Coins = Coins::all();
        $Employess = Employess::all();
        $Safes = SafesBanks::all();  // For safe selection dropdown
        $CostCenters = CostCenter::all();  // For cost center selection dropdown
        $Users = Admin::all();  // For user selection dropdown

        return view('admin.Purchases.PurchasesOrderSechdule', compact('PurchasesOrder', 'items', 'Vendors', 'Stores', 'Coins', 'Employess', 'Safes', 'CostCenters', 'Users'));
    }

    //Filter Purchase Orders
    public function FilterBillPurchasesOrder(Request $request){
        $query = PurchasesOrder::query();

        // Apply filters based on request parameters
        if ($request->filled('Code')) {
            $query->where('Code', 'like', '%' . $request->Code . '%');
        }

        if ($request->filled('Vendor')) {
            $query->where('Vendor', $request->Vendor);
        }

        if ($request->filled('Store')) {
            $query->where('Store', $request->Store);
        }

        if ($request->filled('Coin')) {
            $query->where('Coin', $request->Coin);
        }

        if ($request->filled('Delegate')) {
            $query->where('Delegate', $request->Delegate);
        }

        if ($request->filled('Safe')) {
            $query->where('Safe', $request->Safe);
        }

        if ($request->filled('Cost_Center')) {
            $query->where('Cost_Center', $request->Cost_Center);
        }

        if ($request->filled('User')) {
            $query->where('User', $request->User);
        }

        if ($request->filled('Payment_Method')) {
            $query->where('Payment_Method', $request->Payment_Method);
        }

        if ($request->filled('Refernce_Number')) {
            $query->where('Refernce_Number', 'like', '%' . $request->Refernce_Number . '%');
        }

        // Date range filtering
        if ($request->filled('From')) {
            $query->whereDate('Date', '>=', $request->From);
        }

        if ($request->filled('To')) {
            $query->whereDate('Date', '<=', $request->To);
        }

        // Get filtered results with pagination
        $items = $query->paginate(15);

        // Get all data for dropdowns
        $PurchasesOrder = PurchasesOrder::all();
        $Vendors = Vendors::all();
        $Stores = Stores::all();
        $Coins = Coins::all();
        $Employess = Employess::all();
        $Safes = SafesBanks::all();
        $CostCenters = CostCenter::all();
        $Users = Admin::all();

        return view('admin.Purchases.PurchasesOrderSechdule', compact('PurchasesOrder', 'items', 'Vendors', 'Stores', 'Coins', 'Employess', 'Safes', 'CostCenters', 'Users'));
    }

    //Purchases
    public function PurchasesPage(){
        $Def = PurchasesDefaultData::first();
        $Stores = Stores::all();
        $Vendors = Vendors::all();
        $Employess = Employess::all();
        $Coins = Coins::all();
        $CostCenter = CostCenter::all();
        $CostCenters = CostCenter::all();  // For cost center dropdowns
        $AcccountingManual = AcccountingManual::all();
        $Brands = Brands::all();
        $ItemsGroups = ItemsGroups::all();
        $Units = Measuerments::all();  // For unit selection dropdowns
        $ChecksTypes = ChecksTypes::all();  // For check type selection
        $Safes = SafesBanks::all();  // For safe/treasury selection
        $show = DefaultDataShowHide::first();
        $Purchases = Purchases::orderBy('id', 'desc')->limit(1)->first();
        $Branches = Branches::all();
        $ModuleSettingsNum = ModuleSettingsNum::first();
        $CompanyData = CompanyData::first();

        // Generate next purchase code
        $purchaseCount = Purchases::count();
        $Code = $purchaseCount + 1;

        // Generate next vendor code for new vendor modal
        $vendorCount = Vendors::count();
        $CodeUser = $vendorCount + 1;

        return view('admin.Purchases.Purchases', compact('Def', 'Stores', 'Vendors', 'Employess', 'Coins', 'CostCenter', 'CostCenters', 'AcccountingManual', 'Brands', 'ItemsGroups', 'Units', 'ChecksTypes', 'Safes', 'show', 'Purchases', 'Branches', 'ModuleSettingsNum', 'CompanyData', 'Code', 'CodeUser'));
    }
}
