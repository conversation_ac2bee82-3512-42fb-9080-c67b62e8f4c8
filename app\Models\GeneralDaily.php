<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Stancl\Tenancy\Database\Concerns\CentralConnection;

class GeneralDaily extends Model
{
    use HasFactory, CentralConnection;

        protected $table = 'general_dailies';
      protected $fillable = [
        'Code',
        'Date',
        'Type',
        'TypeEn',
        'Code_Type',
        'Debitor',
        'Creditor',
        'Statement',
        'Draw',
        'Debitor_Coin',
        'Creditor_Coin',
        'Account',
        'Coin',
        'Cost_Center',
        'userr',
        'Branch',

    ];

        public function Account()
    {
        return $this->belongsTo(AcccountingManual::class,'Account');
    }

           public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }

        public function Cost_Center()
    {
        return $this->belongsTo(CostCenter::class,'Cost_Center');
    }

        public function userr()
    {
        return $this->belongsTo(Admin::class,'userr');
    }

        public function Branch()
    {
        return $this->belongsTo(Branches::class,'Branch');
    }



}
