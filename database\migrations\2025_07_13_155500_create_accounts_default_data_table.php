<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAccountsDefaultDataTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('accounts_default_data', function (Blueprint $table) {
            $table->id();
            
            // Accounting default settings
            $table->string('Draw')->nullable();           // Default drawing/document settings
            $table->string('Coin')->nullable();           // Default currency
            $table->string('Sure_Recipts')->nullable();   // Receipt confirmation settings
            $table->string('Show_Group')->nullable();     // Group display settings
            $table->string('Account_Balance')->nullable(); // Account balance settings
            $table->string('Salary')->nullable();         // Default salary account
            $table->string('Commission')->nullable();     // Default commission account
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('accounts_default_data');
    }
}
