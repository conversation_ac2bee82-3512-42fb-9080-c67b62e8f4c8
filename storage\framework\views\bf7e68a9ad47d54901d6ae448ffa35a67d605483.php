<?php
use App\Models\CompanyData;
use App\Models\LoginSlider;
$Def=CompanyData::orderBy('id','desc')->first();
$Sliders=LoginSlider::all();
?>
<style>
.page-logo {
    width: 100% !important;
}    
</style>    
<!DOCTYPE html>
<html lang="en">
   <head>
      <meta charset="utf-8">
      <title>
         <?php echo e(trans('admin.Login')); ?>

      </title>
      
      <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-GLhlTQ8iRABdZLl6O3oVMWSktQOp6b7In1Zl3/Jr59b6EGGoI1aFkw7cmDA6j6gD" crossorigin="anonymous">
      <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js" integrity="sha384-w76AqPfDkMBDXo30jS1Sgez6pr3x5MlQ1ZAGC+nuZB+EYdgRZgiwxhTBTkF7CXvN" crossorigin="anonymous"></script>
      
      <meta name="description" content="Login">
      <meta http-equiv="X-UA-Compatible" content="IE=edge">
      <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no, user-scalable=no, minimal-ui">
       <!--Call App Mode on ios devices -->
      <meta name="apple-mobile-web-app-capable" content="yes" />
       <!--Remove Tap Highlight on Windows Phone IE -->
      <meta name="msapplication-tap-highlight" content="no">
           <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests" />
       <!--Place favicon.ico in the root directory -->
      <link rel="apple-touch-icon" sizes="180x180" href="<?php echo e(URL::to($Def->Icon)); ?>">
      <link rel="icon" type="image/png" sizes="32x32" href="<?php echo e(URL::to($Def->Icon)); ?>">
      <link rel="mask-icon" href="<?php echo e(URL::to($Def->Icon)); ?>" color="#5bbad5">
      <?php 
         if(!function_exists('direction')){
         
                 function direction(){
         
                     if(session()->has('lang')){
                         if(session('lang') == 'ar'){
         
                          return 'rtl';
                         }else{
                             return 'ltr' ;
                         }
                     }else{
                         return 'rtl' ;
                     }
                 }
         
         
                 }
             
         ?>  
      <?php if(direction()=='ltr'): ?> 
      <link id="vendorsbundle" rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/vendors.bundle.css')); ?>">
      <link id="appbundle" rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/app.bundle.css')); ?>">
      <link id="mytheme" rel="stylesheet" media="screen, print" href="#">
      <link id="myskin" rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/skins/skin-master.css')); ?>">
      <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/page-login-alt.css')); ?>">
      <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/style.css')); ?>">
      <?php else: ?>
       <!--base css -->
      <link id="vendorsbundle" rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/vendors.bundle.css')); ?>">
      <link id="appbundle" rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/app.bundle.css')); ?>">
      <link id="mytheme" rel="stylesheet" media="screen, print" href="#">
      <link id="myskin" rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/skins/skin-master.css')); ?>">
      <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/page-login-alt.css')); ?>">
      <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/style.css')); ?>">
      <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/style-ar.css')); ?>">
      <?php endif; ?>   
  </head>
    <style>
    /*  @import  url(http://fonts.googleapis.com/css?family=Open+Sans:800);*/
    /*  .text {*/
    /*    fill: none;*/
    /*    stroke-width: 6;*/
    /*    stroke-linejoin: round;*/
    /*    stroke-dasharray: 70 330;*/
    /*    stroke-dashoffset: 0;*/
    /*    -webkit-animation: stroke 6s infinite linear;*/
    /*    animation: stroke 6s infinite linear;*/
    /*  }*/
    /*  .text:nth-child(5n + 1) {*/
    /*    stroke: #F2385A;*/
    /*    -webkit-animation-delay: -1.2s;*/
    /*    animation-delay: -1.2s;*/
    /*  }*/
    /*  .text:nth-child(5n + 2) {*/
    /*    stroke: #F5A503;*/
    /*    -webkit-animation-delay: -2.4s;*/
    /*    animation-delay: -2.4s;*/
    /*  }*/
    /*  .text:nth-child(5n + 3) {*/
    /*    stroke: #E9F1DF;*/
    /*    -webkit-animation-delay: -3.6s;*/
    /*    animation-delay: -3.6s;*/
    /*  }*/
    /*  .text:nth-child(5n + 4) {*/
    /*    stroke: #56D9CD;*/
    /*    -webkit-animation-delay: -4.8s;*/
    /*    animation-delay: -4.8s;*/
    /*  }*/
    /*  .text:nth-child(5n + 5) {*/
    /*    stroke: #3AA1BF;*/
    /*    -webkit-animation-delay: -6s;*/
    /*    animation-delay: -6s;*/
    /*  }*/
    /*  @-webkit-keyframes stroke {*/
    /*    100% {*/
    /*      stroke-dashoffset: -400;*/
    /*    }*/
    /*  }*/
    /*  @keyframes  stroke {*/
    /*    100% {*/
    /*      stroke-dashoffset: -400;*/
    /*    }*/
    /*  }*/
       /*Other styles */
      
     
    /*  .logoo {*/
        /*background: #111;*/
    /*    background-size: .2em 100%;*/
    /*    font: 12.5em/1 Open Sans, Impact;*/
    /*    text-transform: uppercase;*/
    /*    margin: 0;*/
    /*  }*/
    
     </style>
  <body>
      <!-- DOC: script to save and load page settings -->
      <script>
         /**
          *	This script should be placed right after the body tag for fast execution 
          *	Note: the script is written in pure javascript and does not depend on thirdparty library
          **/
        //  'use strict';
         
         var classHolder = document.getElementsByTagName("BODY")[0],
        //      /** 
        //       * Load from localstorage
        //       **/
             themeSettings = (localStorage.getItem('themeSettings')) ? JSON.parse(localStorage.getItem('themeSettings')) :
             {},
             themeURL = themeSettings.themeURL || '',
             themeOptions = themeSettings.themeOptions || '';
         /** 
          * Load theme options
          **/
         if (themeSettings.themeOptions)
         {
             classHolder.className = themeSettings.themeOptions;
             console.log("%c✔ Theme settings loaded", "color: #148f32");
         }
         else
         {
             console.log("%c✔ Heads up! Theme settings is empty or does not exist, loading default settings...", "color: #ed1c24");
         }
         if (themeSettings.themeURL && !document.getElementById('mytheme'))
         {
             var cssfile = document.createElement('link');
             cssfile.id = 'mytheme';
             cssfile.rel = 'stylesheet';
             cssfile.href = themeURL;
             document.getElementsByTagName('head')[0].appendChild(cssfile);
         
         }
         else if (themeSettings.themeURL && document.getElementById('mytheme'))
         {
             document.getElementById('mytheme').href = themeSettings.themeURL;
         }
         /** 
          * Save to localstorage 
          **/
         var saveSettings = function()
         {
             themeSettings.themeOptions = String(classHolder.className).split(/[^\w-]+/).filter(function(item)
             {
                 return /^(nav|header|footer|mod|display)-/i.test(item);
             }).join(' ');
             if (document.getElementById('mytheme'))
             {
                 themeSettings.themeURL = document.getElementById('mytheme').getAttribute("href");
             };
             localStorage.setItem('themeSettings', JSON.stringify(themeSettings));
         }
         /** 
          * Reset settings
          **/
         var resetSettings = function()
         {
             localStorage.setItem("themeSettings", "");
         }
         
      </script>
    <!--  <div class="blankpage-form-field">-->
    <!--      <div>-->
    <!--                     <div class="left-section text-center" >-->
    <!--              <div class="logoo">-->
    <!--                  <svg viewBox="0 0 600 300">-->
    <!--  <symbol id="s-text">-->
    <!--    <text text-anchor="middle" x="50%" y="50%" dy=".35em">-->
    <!--     RENO-->
    <!--    </text>-->
    <!--  </symbol>-->
       <!--Duplicate symbols -->
    <!--  <use xlink:href="#s-text" class="text"></use>-->
    <!--  <use xlink:href="#s-text" class="text"></use>-->
    <!--  <use xlink:href="#s-text" class="text"></use>-->
    <!--  <use xlink:href="#s-text" class="text"></use>-->
    <!--  <use xlink:href="#s-text" class="text"></use>-->
    <!--</svg>-->
    <!--     </div>-->
    
    
    
    <div class="background-image">
<div class="row">
<div class="box-slider col-lg-5 col-md-12 col-sm-12">
<div id="carouselExampleInterval" class="carousel slide" data-bs-ride="carousel">
<div class="carousel-inner">
         <?php $i = 1; ?>
    <?php $__currentLoopData = $Sliders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $slide): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
<div class="carousel-item <?php if($i === 1): ?> active <?php endif; ?>" data-bs-interval="10000">
<img src="<?php echo e(URL::to($slide->Image)); ?>" class="d-block w-100" alt="...">
      <?php $i ++; ?>
</div>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</div>
<button class="carousel-control-prev" type="button" data-bs-target="#carouselExampleInterval" data-bs-slide="prev">
<span class="carousel-control-prev-icon" aria-hidden="true"></span>
<span class="visually-hidden">Previous</span>
</button>
<button class="carousel-control-next" type="button" data-bs-target="#carouselExampleInterval" data-bs-slide="next">
<span class="carousel-control-next-icon" aria-hidden="true"></span>
<span class="visually-hidden">Next</span>
</button>
</div>
</div>
    
    
    <div class="col-lg-7 col-md-12 col-sm-12 box-form pe-5">
    
         <div class="page-logo">
            <!--<a href="javascript:void(0)" class="page-logo-link press-scale-down d-flex align-items-center">-->
              <!--Logo-->
              <?php if(!empty($Def->Logo)): ?>
              <img src="<?php echo e(URL::to($Def->Logo)); ?>" alt="SmartAdmin WebApp" aria-roledescription="logo">
              <?php else: ?>  
              <img src="<?php echo e(asset('Admin/img/logo.png')); ?>" alt="SmartAdmin WebApp" aria-roledescription="logo">
              <?php endif; ?>
              <span class="page-logo-text mr-1">
              <?php if(!empty($Def->Name)): ?>
                 <?php echo e(app()->getLocale() == 'ar' ?$Def->Name :$Def->NameEn); ?>

              <?php else: ?>
              <?php echo e(trans('admin.Ost')); ?>

              <?php endif; ?>
              </span>
              <!--<i class="fal fa-angle-down d-inline-block ml-1 fs-lg color-primary-300"></i>-->
            <!--</a>-->
         </div>
         
         
         <div class="card p-4 border-top-left-radius-0 border-top-right-radius-0">
            <span id="ex"> <?php echo $__env->make('admin.layouts.messages', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>  
            <form action="<?php echo e(url('Login')); ?>" method="post" enctype="multipart/form-data">
              <?php echo csrf_field(); ?>

              <?php echo view('honeypot::honeypotFormFields'); ?>
              <div class="form-group">
                  <input type="email" name="email" id="username" class="form-control" placeholder="<?php echo e(trans('admin.Email')); ?>" value="<?php echo e(old('email')); ?>" required>
              </div>
              <div class="form-group">
                  <input type="password" name="password" id="password" class="form-control" placeholder="<?php echo e(trans('admin.Password')); ?>" value="<?php echo e(old('password')); ?>" required>
              </div>
              <div class="form-group text-left">
                  <div class="custom-control custom-checkbox">
                     <input type="checkbox" class="custom-control-input" name="rememberme" id="rememberme">
                     <label class="custom-control-label" for="rememberme"> <?php echo e(trans('admin.RmemberMe')); ?></label>
                  </div>
              </div>
              <button style="color:black;" type="submit" class="btn btn-default"> <?php echo e(trans('admin.Login')); ?></button>
            </form>
         <!--</div>-->
         <div class="blankpage-footer text-center">
            <a href="<?php echo e(url('forgotpassword')); ?>"><strong style="color:black;"><?php echo e(trans('admin.ForgotPassword')); ?></strong></a> 
         </div>
      </div>
      
      
</div>
</div>
</div>

      
      
      
    
      
      
      <!--<video poster="<?php echo e(asset('Admin/img/backgrounds/clouds.png')); ?>" id="bgvid" playsinline autoplay muted loop>-->
      <!--   <source src="<?php echo e(asset('Admin/media/video/cc.webm')); ?>" type="video/webm">-->
      <!--   <source src="<?php echo e(asset('Admin/media/video/cc.mp4')); ?>" type="video/mp4">-->
      <!--</video>-->
       <!--BEGIN Color profile -->
       <!--this area is hidden and will not be seen on screens or screen reade-->
       
       
       
       
       
       
       
    <?php if(app()->getLocale() == 'ar' ): ?>   

       
<div class="row">
<div class="col-md-12 col-lg-12 text-center">
<div class="text-effect">
<span>P</span><span>R</span><span>E</span>    
 <span>T</span><span>S</span><span>O</span>


</div>
</div>
</div>
     
<?php else: ?>
      <div class="row">
<div class="col-md-12 col-lg-12 text-center">
<div class="text-effect">
    
<span>O</span><span>S</span><span>T</span>  
<span>E</span><span>R</span><span>P</span>    



</div>
</div>
</div>
      
<?php endif; ?>      









<style>
      .card {
          padding: 20px 134px !important;
      }
      .card .form-group input {
          margin: 20px 91px !important;
          text-align: center !important;
          margin: auto !important;
          border: 1px solid #574677;
      }
      .page-logo {
          width: 29.875rem;
      }
      .page-logo span {
          padding-right: 22rem ;
      }
      .btn-login {
          background-color: #695191 !important;
          color: white !important;
      }
      .box-form {
          margin: auto !important;
          text-align: center !important;
            width: 100% !important;
      }
      .box-slider {
          margin: auto !important;
          text-align: center !important;
          width: 100% !important;
          padding-left: 3rem!important;
      }
      .box-slider img,
      .box-slider .carousel-inner {
          width: 325px !important;
          height: 350px !important;
      }
      
      .demo{ background: linear-gradient(to right,#7BC6CC,#BE93C5); }
.text-effect{
    color: #fff;
    font-family: 'Oswald', sans-serif;
    font-size: 100px;
    font-weight: 700;
    text-align: center;
}
.text-effect span{
    display: inline-block;
    text-shadow: 2px 2px 3px #3d6366;
}
.text-effect span:nth-child(1) { animation: animate 3s infinite 0.2s ease-in-out; }
.text-effect span:nth-child(2) { animation: animate 3s infinite 0.4s ease-in-out; }
.text-effect span:nth-child(3) { animation: animate 3s infinite 0.6s ease-in-out; }
.text-effect span:nth-child(4) { animation: animate 3s infinite 0.8s ease-in-out; }
.text-effect span:nth-child(5) { animation: animate 3s infinite 1s ease-in-out; }
.text-effect span:nth-child(6) { animation: animate 3s infinite 1.2s ease-in-out; }
.text-effect span:nth-child(7) { animation: animate 3s infinite 1.4s ease-in-out; }
.text-effect span:nth-child(8) { animation: animate 3s infinite 1.6s ease-in-out; }
.text-effect span:nth-child(9) { animation: animate 3s infinite 1.8s ease-in-out; }
.text-effect span:nth-child(10){ animation: animate 3s infinite 2s ease-in-out; }
@keyframes    animate{
    0%{ transform: translateY(0); }
50%{
        transform: translateY(12px);
        opacity: 0.09;
    }
}
      
      
      @media (max-width: 600px) {
         .box-slider img,
         .box-slider .carousel-inner {
          width: 100% !important;
          height: 350px !important;
          /*padding: 0 10px !important;*/
          padding-left: 0 !important;
          margin: 0 !important;
          margin-bottom: 70px !important;
      }
      body {
          overflow-y: auto !important;
      }
      .box-form {
          /*width: 100% !important;*/
          padding: 0 30px 400px 30px !important;
          margin: auto !important;
      }
      .page-logo span {
          padding-right: 17rem ;
      }
      .box-slider {
          padding: 0 !important;
      }
      .page-logo img {
          padding-right: 0px;
      }
      }
      
      body {
         background: none !important; 
      }
      body {
          background-color: #9491b7 !important;
      }
  </style>
       
       
       
       
       
       
       
       
       
       
       
       
       
       
       
       
       
       
       
       
       
       
       
       
       
       <!--we use this only for CSS color refernce for JS stuff -->
      <p id="js-color-profile" class="d-none">
         <span class="color-primary-50"></span>
         <span class="color-primary-100"></span>
         <span class="color-primary-200"></span>
         <span class="color-primary-300"></span>
         <span class="color-primary-400"></span>
         <span class="color-primary-500"></span>
         <span class="color-primary-600"></span>
         <span class="color-primary-700"></span>
         <span class="color-primary-800"></span>
         <span class="color-primary-900"></span>
         <span class="color-info-50"></span>
         <span class="color-info-100"></span>
         <span class="color-info-200"></span>
         <span class="color-info-300"></span>
         <span class="color-info-400"></span>
         <span class="color-info-500"></span>
         <span class="color-info-600"></span>
         <span class="color-info-700"></span>
         <span class="color-info-800"></span>
         <span class="color-info-900"></span>
         <span class="color-danger-50"></span>
         <span class="color-danger-100"></span>
         <span class="color-danger-200"></span>
         <span class="color-danger-300"></span>
         <span class="color-danger-400"></span>
         <span class="color-danger-500"></span>
         <span class="color-danger-600"></span>
         <span class="color-danger-700"></span>
         <span class="color-danger-800"></span>
         <span class="color-danger-900"></span>
         <span class="color-warning-50"></span>
         <span class="color-warning-100"></span>
         <span class="color-warning-200"></span>
         <span class="color-warning-300"></span>
         <span class="color-warning-400"></span>
         <span class="color-warning-500"></span>
         <span class="color-warning-600"></span>
         <span class="color-warning-700"></span>
         <span class="color-warning-800"></span>
         <span class="color-warning-900"></span>
         <span class="color-success-50"></span>
         <span class="color-success-100"></span>
         <span class="color-success-200"></span>
         <span class="color-success-300"></span>
         <span class="color-success-400"></span>
         <span class="color-success-500"></span>
         <span class="color-success-600"></span>
         <span class="color-success-700"></span>
         <span class="color-success-800"></span>
         <span class="color-success-900"></span>
         <span class="color-fusion-50"></span>
         <span class="color-fusion-100"></span>
         <span class="color-fusion-200"></span>
         <span class="color-fusion-300"></span>
         <span class="color-fusion-400"></span>
         <span class="color-fusion-500"></span>
         <span class="color-fusion-600"></span>
         <span class="color-fusion-700"></span>
         <span class="color-fusion-800"></span>
         <span class="color-fusion-900"></span>
      </p>
      <script src="<?php echo e(asset('Admin/js/vendors.bundle.js')); ?>"></script>
      <script src="<?php echo e(asset('Admin/js/app.bundle.js')); ?>"></script>
      <script>
         $(document).ready(function(){
           setTimeout(function(){ $("#ex").hide(); }, 6000);
         });
      </script>
       <!--Page related scripts -->
   </body>
    <!--END Body -->
</html>














<?php /**PATH C:\xampp2\htdocs\erp\resources\views/admin/Login.blade.php ENDPATH**/ ?>