<?php
use App\Models\AcccountingManual;
use App\Models\AccountsDefaultData;
$Def=AccountsDefaultData::orderBy('id','desc')->first();
?>
<ul>
   <?php $__currentLoopData = $Childs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $child): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?> 
   <li>
      <?php if($child->Type == 0): ?>
      <span class="label label-success"><i class="fal fa-plus-circle"></i>  
      <?php echo e($child->Code); ?> - <?php echo e(app()->getLocale() == 'ar' ?$child->Name :$child->NameEn); ?>

      </span>
     
         <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('حذف حساب في الدليل المحاسبي')): ?>  
      <button type="button" class="btn btn-default" data-toggle="modal" data-target="#default-example-modal-center<?php echo e($child->id); ?>"><i class="fal fa-trash-alt"></i></button>
       <?php endif; ?>
        
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تعديل حساب في الدليل المحاسبي')): ?>  
      <button type="button" class="btn btn-default" data-toggle="modal" data-target="#default-example-modal-lg-edit<?php echo e($child->id); ?>"><i class="fal fa-edit"></i></button>
       <?php endif; ?>
      
       
       
      <?php    $Childs=AcccountingManual::orderBy('Code','asc')->where('Parent',$child->id)->limit(100)->get();   ?>
      <?php echo $__env->make('admin.Accounts.Childs',['Childs' => $Childs,'Groups'=>$Groups], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
      <?php else: ?>
      <span class="label label-success"> 
      <?php echo e($child->Code); ?> -  <?php echo e(app()->getLocale() == 'ar' ?$child->Name :$child->NameEn); ?>

      </span>
       
      <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('حذف حساب في الدليل المحاسبي')): ?> 
      <button type="button" class="btn btn-default" data-toggle="modal" data-target="#default-example-modal-center<?php echo e($child->id); ?>"><i class="fal fa-trash-alt"></i></button>
       <?php endif; ?>
       
            
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تعديل حساب في الدليل المحاسبي')): ?>  
      <button type="button" class="btn btn-default" data-toggle="modal" data-target="#default-example-modal-lg-edit<?php echo e($child->id); ?>"><i class="fal fa-edit"></i></button>
       <?php endif; ?>
     
      <?php endif; ?>
   </li>
   <!-- Modal Edit-->
   <div class="modal fade" id="default-example-modal-lg-edit<?php echo e($child->id); ?>" tabindex="-1" role="dialog" aria-hidden="true">
      <div class="modal-dialog modal-lg" role="document">
         <div class="modal-content">
            <div class="modal-header">
               <h5 class="modal-title"><?php echo e(trans('admin.Edit')); ?>  </h5>
               <button type="button" class="close" data-dismiss="modal" aria-label="Close">
               <span aria-hidden="true"><i class="fal fa-times"></i></span>
               </button>
            </div>
            <div class="modal-body">
               <form action="<?php echo e(url('EditAccount')); ?>" method="post">
                  <?php echo csrf_field(); ?>

                   <?php echo view('honeypot::honeypotFormFields'); ?>
                  <input type="hidden" name="ID" value="<?php echo e($child->id); ?>">        
                  <div class="form-row">
                     <div class="form-group col-lg-12">
                        <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Arabic_Name')); ?> </label>
                        <input type="text" name="Name" value="<?php echo e($child->Name); ?>" id="simpleinput" class="form-control" required>
                     </div>
                      
                                                                    <div class="form-group col-md-12 col-12-print">
                                    <label class="form-label" for="simpleinput"><?php echo e(trans('admin.English_Name')); ?></label>
                    <input type="text" name="NameEn" value="<?php echo e($child->NameEn); ?>" id="simpleinput" class="form-control" required>
                                                </div>
                      
                          <div class="form-group col-lg-12">
                        <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Code')); ?> </label>
                        <input type="text" name="Account_Code" value="<?php echo e($child->Account_Code); ?>" id="simpleinput" class="form-control" >
                     </div>
                     <div class="form-group col-md-12">
                        <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Notes')); ?></label>
                        <input type="text" name="Note" id="simpleinput" class="form-control" value="<?php echo e($child->Note); ?>">
                     </div>
                                           <?php if($Def->Show_Group == 1): ?>
                                            <div class="form-group col-md-12">
                                     <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Group')); ?></label>
                          <select class="select2 form-control" name="Pro_Group">
                                        <option value=""><?php echo e(trans('admin.Group')); ?></option>     
                              <?php $__currentLoopData = $Groups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $grop): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                              
                                     <option value="<?php echo e($grop->id); ?>" <?php if($grop->id == $child->Pro_Group): ?> selected <?php endif; ?>><?php echo e($grop->Name); ?></option>   
                              <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </select>
                                                </div>
                                                <?php endif; ?>
                      
                  </div>
                  <div class="modal-footer">
                     <button type="button" class="btn btn-secondary" data-dismiss="modal"><?php echo e(trans('admin.Name')); ?> </button>
                     <button type="submit" class="btn btn-primary"><?php echo e(trans('admin.SaveChanges')); ?> </button>
                  </div>
               </form>
            </div>
         </div>
      </div>
   </div>
   <!-- Modal Delete -->
   <div class="modal fade" id="default-example-modal-center<?php echo e($child->id); ?>" tabindex="-1" role="dialog" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered" role="document">
         <div class="modal-content">
            <div class="modal-header">
               <h4 class="modal-title">
                  <?php echo e(trans('admin.RUSWDT')); ?> <strong><?php echo e(app()->getLocale() == 'ar' ?$child->Name :$child->NameEn); ?></strong>   
               </h4>
               <button type="button" class="close" data-dismiss="modal" aria-label="Close">
               <span aria-hidden="true"><i class="fal fa-times"></i></span>
               </button>
            </div>
            <div class="modal-footer">
               <button type="button" class="btn btn-secondary" data-dismiss="modal"><?php echo e(trans('admin.No')); ?></button>
               <a href="<?php echo e(url('DeleteAccount/'.$child->id)); ?>" class="btn btn-primary"><?php echo e(trans('admin.Yes')); ?></a>
            </div>
         </div>
      </div>
   </div>
   <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</ul><?php /**PATH C:\xampp2\htdocs\erp\resources\views/admin/Accounts/Childs.blade.php ENDPATH**/ ?>