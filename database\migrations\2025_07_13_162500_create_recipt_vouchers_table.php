<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateReciptVouchersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('recipt_vouchers', function (Blueprint $table) {
            $table->id();
            
            // Receipt voucher basic information
            $table->string('Code')->nullable();         // Voucher code
            $table->date('Date')->nullable();           // Voucher date
            $table->string('Draw')->nullable();         // Drawing/document number
            
            // Financial information
            $table->string('Coin')->nullable();         // Currency reference
            $table->string('Cost_Center')->nullable();  // Cost center reference
            $table->decimal('Total_Creditor', 15, 2)->nullable();  // Total credit amount
            $table->string('Safe')->nullable();         // Safe/bank account reference
            
            // Additional information
            $table->text('Note')->nullable();           // Voucher notes
            $table->string('Shift')->nullable();        // Work shift reference
            $table->string('Store')->nullable();        // Store reference
            $table->string('User')->nullable();         // User who created the voucher
            $table->string('Status')->nullable();       // Voucher status
            $table->text('File')->nullable();           // Attached file
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('recipt_vouchers');
    }
}
