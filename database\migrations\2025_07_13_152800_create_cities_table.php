<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCitiesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('cities', function (Blueprint $table) {
            $table->id();

            // City information
            $table->string('Arabic_Name')->nullable();
            $table->string('English_Name')->nullable();
            $table->string('Ship_Price')->nullable();
            $table->string('SearchCode')->nullable();

            // References
            $table->string('Gov')->nullable();
            $table->string('Shipping_Company')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('cities');
    }
}
