<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateWebslidersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('websliders', function (Blueprint $table) {
            $table->id();
            
            // Slider information
            $table->string('Status')->default('1');
            $table->string('Arabic_Title')->nullable();
            $table->string('English_Title')->nullable();
            $table->text('Arabic_Desc')->nullable();
            $table->text('English_Desc')->nullable();
            $table->string('Image')->nullable();
            $table->string('Type')->nullable();
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('websliders');
    }
}
