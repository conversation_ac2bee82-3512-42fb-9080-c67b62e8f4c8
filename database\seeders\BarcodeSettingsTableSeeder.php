<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class BarcodeSettingsTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('barcode_settings')->delete();
        
        \DB::table('barcode_settings')->insert(array (
            0 => 
            array (
                'id' => 6,
                'Code' => '2',
                'Name' => 'Default',
                'Type' => '2',
                'Direction' => '1',
                'Width' => '3.8',
                'Height' => '2.5',
                'Padding_L' => '.5',
                'Padding_R' => '.5',
                'Padding_T' => '.5',
                'Padding_B' => '.5',
                'Margin_L' => '.5',
                'Margin_R' => '.5',
                'Margin_T' => '.5',
                'Margin_B' => '.5',
                'Barcode_Width' => '3',
                'Barcode_Height' => '0.5',
                'Font_Size' => '6',
                'Line_Height' => '4',
                'created_at' => '2022-03-09 09:57:35',
                'updated_at' => '2022-04-13 02:49:16',
                'Height_Logo' => '0',
                'Width_Logo' => '0',
            ),
        ));
        
        
    }
}