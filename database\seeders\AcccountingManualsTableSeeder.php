<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class AcccountingManualsTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('acccounting_manuals')->delete();
        
        \DB::table('acccounting_manuals')->insert(array (
            0 => 
            array (
                'id' => 30,
                'Code' => '*********',
                'Name' => 'الخزينه الرئيسيه',
                'Type' => '1',
                'Parent' => '28',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-06-16 00:41:19',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '7',
            ),
            1 => 
            array (
                'id' => 32,
                'Code' => '*********',
                'Name' => 'بنك مصر',
                'Type' => '1',
                'Parent' => '29',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-06-16 00:42:58',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '9',
            ),
            2 => 
            array (
                'id' => 33,
                'Code' => '*********',
                'Name' => 'البنك الأهلي',
                'Type' => '1',
                'Parent' => '29',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-06-16 00:43:54',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '10',
            ),
            3 => 
            array (
                'id' => 24,
                'Code' => '10202',
                'Name' => 'العملاء',
                'Type' => '0',
                'Parent' => '22',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-06-16 00:34:53',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '11',
            ),
            4 => 
            array (
                'id' => 25,
                'Code' => '10203',
                'Name' => 'أوراق قبض',
                'Type' => '0',
                'Parent' => '22',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-06-16 00:36:12',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '15',
            ),
            5 => 
            array (
                'id' => 26,
                'Code' => '10204',
                'Name' => 'مدينين',
                'Type' => '0',
                'Parent' => '22',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-06-16 00:37:03',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '17',
            ),
            6 => 
            array (
                'id' => 27,
                'Code' => '10205',
                'Name' => 'المخزون',
                'Type' => '0',
                'Parent' => '22',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-06-16 00:37:44',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '26',
            ),
            7 => 
            array (
                'id' => 16,
                'Code' => '2',
                'Name' => 'الخصوم',
                'Type' => '0',
                'Parent' => '0',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-06-16 00:29:12',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '32',
            ),
            8 => 
            array (
                'id' => 37,
                'Code' => '201',
                'Name' => 'الموردين',
                'Type' => '0',
                'Parent' => '16',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-06-16 01:12:06',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '33',
            ),
            9 => 
            array (
                'id' => 38,
                'Code' => '20101',
                'Name' => 'مورد نقدي',
                'Type' => '1',
                'Parent' => '37',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-06-16 01:12:46',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '34',
            ),
            10 => 
            array (
                'id' => 39,
                'Code' => '202',
                'Name' => 'الضرائب',
                'Type' => '0',
                'Parent' => '16',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-06-16 01:13:20',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '35',
            ),
            11 => 
            array (
                'id' => 41,
                'Code' => '203',
                'Name' => 'أوراق دفع',
                'Type' => '0',
                'Parent' => '16',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-06-16 01:14:38',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '38',
            ),
            12 => 
            array (
                'id' => 42,
                'Code' => '20301',
                'Name' => 'أوراق دفع',
                'Type' => '1',
                'Parent' => '41',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-06-16 01:15:16',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '39',
            ),
            13 => 
            array (
                'id' => 43,
                'Code' => '204',
                'Name' => 'دائنين',
                'Type' => '0',
                'Parent' => '16',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-06-16 01:15:57',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '40',
            ),
            14 => 
            array (
                'id' => 44,
                'Code' => '301',
                'Name' => 'رأس المال',
                'Type' => '0',
                'Parent' => '17',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-06-16 01:16:55',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '44',
            ),
            15 => 
            array (
                'id' => 45,
                'Code' => '30101',
                'Name' => 'رأس المال',
                'Type' => '1',
                'Parent' => '44',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-06-16 01:17:44',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '45',
            ),
            16 => 
            array (
                'id' => 18,
                'Code' => '4',
                'Name' => 'الايرادات',
                'Type' => '0',
                'Parent' => '0',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-06-16 00:29:56',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '51',
            ),
            17 => 
            array (
                'id' => 47,
                'Code' => '401',
                'Name' => 'إيرادات المبيعات',
                'Type' => '0',
                'Parent' => '18',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-06-16 01:19:32',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '52',
            ),
            18 => 
            array (
                'id' => 20,
                'Code' => '6',
                'Name' => 'المصروفات',
                'Type' => '0',
                'Parent' => '0',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-06-16 00:30:50',
                'updated_at' => '2022-10-18 22:42:48',
                'Account_Code' => '60',
            ),
            19 => 
            array (
                'id' => 21,
                'Code' => '101',
                'Name' => 'أصول ثابتة',
                'Type' => '0',
                'Parent' => '15',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-06-16 00:31:46',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '2',
            ),
            20 => 
            array (
                'id' => 122,
                'Code' => '10102',
                'Name' => 'الأثاث المكتبي',
                'Type' => '0',
                'Parent' => '21',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-10-02 08:25:42',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '3',
            ),
            21 => 
            array (
                'id' => 23,
                'Code' => '10201',
                'Name' => 'النقدية وما في حكمها',
                'Type' => '0',
                'Parent' => '22',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-06-16 00:34:14',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '5',
            ),
            22 => 
            array (
                'id' => 28,
                'Code' => '1020101',
                'Name' => 'الخزينة',
                'Type' => '0',
                'Parent' => '23',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-06-16 00:38:47',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '6',
            ),
            23 => 
            array (
                'id' => 29,
                'Code' => '1020102',
                'Name' => 'نقدية بالبنك',
                'Type' => '0',
                'Parent' => '23',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-06-16 00:40:04',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '8',
            ),
            24 => 
            array (
                'id' => 34,
                'Code' => '1020201',
                'Name' => 'عميل نقدي',
                'Type' => '1',
                'Parent' => '24',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-06-16 00:44:42',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '12',
            ),
            25 => 
            array (
                'id' => 832,
                'Code' => '1020203',
                'Name' => 'Default',
                'Type' => '1',
                'Parent' => '24',
                'Note' => NULL,
                'User' => 11,
                'created_at' => '2022-05-22 10:37:57',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '14',
            ),
            26 => 
            array (
                'id' => 35,
                'Code' => '1020301',
                'Name' => 'أوراق قبض',
                'Type' => '1',
                'Parent' => '25',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-06-16 00:46:08',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '16',
            ),
            27 => 
            array (
                'id' => 121,
                'Code' => '1020401',
                'Name' => 'العهد',
                'Type' => '0',
                'Parent' => '26',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-10-02 08:24:25',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '18',
            ),
            28 => 
            array (
                'id' => 212,
                'Code' => '*********',
                'Name' => 'Default عهده',
                'Type' => '1',
                'Parent' => '121',
                'Note' => '1',
                'User' => 1,
                'created_at' => '2022-02-18 01:17:44',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '19',
            ),
            29 => 
            array (
                'id' => 215,
                'Code' => '*********',
                'Name' => 'Default عهده',
                'Type' => '1',
                'Parent' => '121',
                'Note' => '1',
                'User' => 1,
                'created_at' => '2022-02-18 01:21:42',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '20',
            ),
            30 => 
            array (
                'id' => 53,
                'Code' => '601',
                'Name' => 'المرتبات والأجور',
                'Type' => '0',
                'Parent' => '20',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-06-16 01:24:33',
                'updated_at' => '2022-10-18 22:42:48',
                'Account_Code' => '61',
            ),
            31 => 
            array (
                'id' => 141,
                'Code' => '30303',
                'Name' => 'الارباح المرحلة',
                'Type' => '0',
                'Parent' => '136',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-10-13 17:37:01',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '50',
            ),
            32 => 
            array (
                'id' => 824,
                'Code' => '606',
                'Name' => 'مصروفات البيع و التوزيع',
                'Type' => '0',
                'Parent' => '20',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2022-04-13 19:11:41',
                'updated_at' => '2022-10-18 22:42:48',
                'Account_Code' => '75',
            ),
            33 => 
            array (
                'id' => 164,
                'Code' => '60601',
                'Name' => 'عمولات الموظفين',
                'Type' => '0',
                'Parent' => '824',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-10-28 09:42:04',
                'updated_at' => '2022-10-18 22:42:48',
                'Account_Code' => '76',
            ),
            34 => 
            array (
                'id' => 55,
                'Code' => '60602',
                'Name' => 'مصروفات التشغيل',
                'Type' => '0',
                'Parent' => '824',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-06-16 01:25:48',
                'updated_at' => '2022-10-18 22:42:48',
                'Account_Code' => '83',
            ),
            35 => 
            array (
                'id' => 211,
                'Code' => '6060101',
                'Name' => 'Default عموله',
                'Type' => '1',
                'Parent' => '164',
                'Note' => '1',
                'User' => 1,
                'created_at' => '2022-02-18 01:17:44',
                'updated_at' => '2022-10-18 22:42:48',
                'Account_Code' => '77',
            ),
            36 => 
            array (
                'id' => 214,
                'Code' => '6060102',
                'Name' => 'Default عموله',
                'Type' => '1',
                'Parent' => '164',
                'Note' => '1',
                'User' => 1,
                'created_at' => '2022-02-18 01:21:42',
                'updated_at' => '2022-10-18 22:42:48',
                'Account_Code' => '78',
            ),
            37 => 
            array (
                'id' => 816,
                'Code' => '6060103',
                'Name' => 'Default عموله',
                'Type' => '1',
                'Parent' => '164',
                'Note' => '1',
                'User' => 11,
                'created_at' => '2022-04-13 02:51:17',
                'updated_at' => '2022-10-18 22:42:48',
                'Account_Code' => '79',
            ),
            38 => 
            array (
                'id' => 147,
                'Code' => '103',
                'Name' => 'اصول اخري',
                'Type' => '0',
                'Parent' => '15',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-10-14 17:02:03',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '31',
            ),
            39 => 
            array (
                'id' => 112,
                'Code' => '20202',
                'Name' => 'بدون ضريبة',
                'Type' => '1',
                'Parent' => '39',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-08-23 00:16:53',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '37',
            ),
            40 => 
            array (
                'id' => 54,
                'Code' => '60101',
                'Name' => 'الموظفين',
                'Type' => '0',
                'Parent' => '53',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-06-16 01:25:07',
                'updated_at' => '2022-10-18 22:42:48',
                'Account_Code' => '62',
            ),
            41 => 
            array (
                'id' => 48,
                'Code' => '40101',
                'Name' => 'المبيعات',
                'Type' => '1',
                'Parent' => '47',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-06-16 01:20:15',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '53',
            ),
            42 => 
            array (
                'id' => 56,
                'Code' => '6060201',
                'Name' => 'الإيجار',
                'Type' => '1',
                'Parent' => '55',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-06-16 01:26:20',
                'updated_at' => '2022-10-18 22:42:48',
                'Account_Code' => '84',
            ),
            43 => 
            array (
                'id' => 819,
                'Code' => '6060104',
                'Name' => 'Default عموله',
                'Type' => '1',
                'Parent' => '164',
                'Note' => NULL,
                'User' => 11,
                'created_at' => '2022-04-13 02:51:34',
                'updated_at' => '2022-10-18 22:42:48',
                'Account_Code' => '80',
            ),
            44 => 
            array (
                'id' => 210,
                'Code' => '60103',
                'Name' => 'Default',
                'Type' => '1',
                'Parent' => '53',
                'Note' => '1',
                'User' => 1,
                'created_at' => '2022-02-18 01:17:44',
                'updated_at' => '2022-10-18 22:42:48',
                'Account_Code' => '63',
            ),
            45 => 
            array (
                'id' => 57,
                'Code' => '6060202',
                'Name' => 'كهرباء',
                'Type' => '1',
                'Parent' => '55',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-06-16 01:26:56',
                'updated_at' => '2022-10-18 22:42:48',
                'Account_Code' => '85',
            ),
            46 => 
            array (
                'id' => 58,
                'Code' => '6060203',
                'Name' => 'مصروفات ضيافه',
                'Type' => '1',
                'Parent' => '55',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-06-16 01:41:37',
                'updated_at' => '2022-10-18 22:42:48',
                'Account_Code' => '86',
            ),
            47 => 
            array (
                'id' => 822,
                'Code' => '6060105',
                'Name' => 'Default عموله',
                'Type' => '1',
                'Parent' => '164',
                'Note' => NULL,
                'User' => 11,
                'created_at' => '2022-04-13 02:51:51',
                'updated_at' => '2022-10-18 22:42:48',
                'Account_Code' => '81',
            ),
            48 => 
            array (
                'id' => 817,
                'Code' => '*********',
                'Name' => 'Default عهده',
                'Type' => '1',
                'Parent' => '121',
                'Note' => '1',
                'User' => 11,
                'created_at' => '2022-04-13 02:51:17',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '21',
            ),
            49 => 
            array (
                'id' => 119,
                'Code' => '205',
                'Name' => 'مجمع اهلاك',
                'Type' => '0',
                'Parent' => '16',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-10-02 08:23:41',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '41',
            ),
            50 => 
            array (
                'id' => 213,
                'Code' => '60104',
                'Name' => 'Default',
                'Type' => '1',
                'Parent' => '53',
                'Note' => '1',
                'User' => 1,
                'created_at' => '2022-02-18 01:21:42',
                'updated_at' => '2022-10-18 22:42:48',
                'Account_Code' => '64',
            ),
            51 => 
            array (
                'id' => 49,
                'Code' => '40102',
                'Name' => 'مردودات المبيعات',
                'Type' => '1',
                'Parent' => '47',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-06-16 01:20:45',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '54',
            ),
            52 => 
            array (
                'id' => 51,
                'Code' => '40104',
                'Name' => 'خدمة صالة',
                'Type' => '1',
                'Parent' => '47',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-06-16 01:21:57',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '55',
            ),
            53 => 
            array (
                'id' => 820,
                'Code' => '*********',
                'Name' => 'Default عهده',
                'Type' => '1',
                'Parent' => '121',
                'Note' => NULL,
                'User' => 11,
                'created_at' => '2022-04-13 02:51:34',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '22',
            ),
            54 => 
            array (
                'id' => 823,
                'Code' => '*********',
                'Name' => 'Default عهده',
                'Type' => '1',
                'Parent' => '121',
                'Note' => NULL,
                'User' => 11,
                'created_at' => '2022-04-13 02:51:51',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '23',
            ),
            55 => 
            array (
                'id' => 829,
                'Code' => '*********',
                'Name' => 'Delivery عهده',
                'Type' => '1',
                'Parent' => '121',
                'Note' => NULL,
                'User' => 11,
                'created_at' => '2022-05-15 16:53:39',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '24',
            ),
            56 => 
            array (
                'id' => 36,
                'Code' => '1020501',
                'Name' => 'المخزون',
                'Type' => '1',
                'Parent' => '27',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-06-16 00:48:00',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '27',
            ),
            57 => 
            array (
                'id' => 206,
                'Code' => '402',
                'Name' => 'خصم مكتسب',
                'Type' => '1',
                'Parent' => '18',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2022-02-16 22:01:22',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '56',
            ),
            58 => 
            array (
                'id' => 70,
                'Code' => '10206',
                'Name' => 'وسيط المخازن',
                'Type' => '0',
                'Parent' => '22',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-07-16 12:00:41',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '29',
            ),
            59 => 
            array (
                'id' => 71,
                'Code' => '1020601',
                'Name' => 'وسيط المخازن',
                'Type' => '1',
                'Parent' => '70',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-07-16 12:01:10',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '30',
            ),
            60 => 
            array (
                'id' => 148,
                'Code' => '207',
                'Name' => 'خصوم طويلة الاجل',
                'Type' => '0',
                'Parent' => '16',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-10-14 17:02:50',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '42',
            ),
            61 => 
            array (
                'id' => 46,
                'Code' => '302',
                'Name' => 'جاري الشركاء',
                'Type' => '0',
                'Parent' => '17',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-06-16 01:18:34',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '46',
            ),
            62 => 
            array (
                'id' => 826,
                'Code' => '403',
                'Name' => 'تحصيل دليفري',
                'Type' => '1',
                'Parent' => '18',
                'Note' => NULL,
                'User' => 11,
                'created_at' => '2022-05-15 16:52:32',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '57',
            ),
            63 => 
            array (
                'id' => 136,
                'Code' => '303',
                'Name' => 'الارباح و الخسائر',
                'Type' => '0',
                'Parent' => '17',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-10-09 12:10:07',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '47',
            ),
            64 => 
            array (
                'id' => 815,
                'Code' => '60105',
                'Name' => 'Default',
                'Type' => '1',
                'Parent' => '53',
                'Note' => '1',
                'User' => 11,
                'created_at' => '2022-04-13 02:51:17',
                'updated_at' => '2022-10-18 22:42:48',
                'Account_Code' => '65',
            ),
            65 => 
            array (
                'id' => 821,
                'Code' => '60107',
                'Name' => 'Default',
                'Type' => '1',
                'Parent' => '53',
                'Note' => NULL,
                'User' => 11,
                'created_at' => '2022-04-13 02:51:51',
                'updated_at' => '2022-10-18 22:42:48',
                'Account_Code' => '67',
            ),
            66 => 
            array (
                'id' => 139,
                'Code' => '30301',
                'Name' => 'الارباح الحالية',
                'Type' => '0',
                'Parent' => '136',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-10-13 17:36:17',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '48',
            ),
            67 => 
            array (
                'id' => 140,
                'Code' => '30302',
                'Name' => 'الارباح الرأس مالية',
                'Type' => '0',
                'Parent' => '136',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-10-13 17:36:41',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '49',
            ),
            68 => 
            array (
                'id' => 828,
                'Code' => '6060106',
                'Name' => 'Delivery عموله',
                'Type' => '1',
                'Parent' => '164',
                'Note' => NULL,
                'User' => 11,
                'created_at' => '2022-05-15 16:53:39',
                'updated_at' => '2022-10-18 22:42:48',
                'Account_Code' => '82',
            ),
            69 => 
            array (
                'id' => 19,
                'Code' => '5',
                'Name' => 'تكلفة المبيعات',
                'Type' => '0',
                'Parent' => '0',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-06-16 00:30:22',
                'updated_at' => '2022-10-18 22:42:48',
                'Account_Code' => '58',
            ),
            70 => 
            array (
                'id' => 52,
                'Code' => '501',
                'Name' => 'تكلفه بضاعه مباعه',
                'Type' => '1',
                'Parent' => '19',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-06-16 01:23:13',
                'updated_at' => '2022-10-18 22:42:48',
                'Account_Code' => '59',
            ),
            71 => 
            array (
                'id' => 97,
                'Code' => '603',
                'Name' => 'شركات الشحن',
                'Type' => '0',
                'Parent' => '20',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-08-08 10:48:13',
                'updated_at' => '2022-10-18 22:42:48',
                'Account_Code' => '70',
            ),
            72 => 
            array (
                'id' => 120,
                'Code' => '605',
                'Name' => 'اهلاك',
                'Type' => '0',
                'Parent' => '20',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-10-02 08:23:55',
                'updated_at' => '2022-10-18 22:42:48',
                'Account_Code' => '73',
            ),
            73 => 
            array (
                'id' => 188,
                'Code' => '60501',
                'Name' => 'اهلاك جديد',
                'Type' => '1',
                'Parent' => '120',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-12-29 11:16:16',
                'updated_at' => '2022-10-18 22:42:48',
                'Account_Code' => '74',
            ),
            74 => 
            array (
                'id' => 22,
                'Code' => '102',
                'Name' => 'أصول متداولة',
                'Type' => '0',
                'Parent' => '15',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-06-16 00:32:28',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '4',
            ),
            75 => 
            array (
                'id' => 218,
                'Code' => '1020202',
                'Name' => 'المخزونعميل مخزن',
                'Type' => '1',
                'Parent' => '24',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2022-03-08 02:53:23',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '13',
            ),
            76 => 
            array (
                'id' => 830,
                'Code' => '1020402',
                'Name' => 'حساب معايره',
                'Type' => '1',
                'Parent' => '26',
                'Note' => NULL,
                'User' => 11,
                'created_at' => '2022-05-21 01:12:14',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '25',
            ),
            77 => 
            array (
                'id' => 831,
                'Code' => '1020502',
                'Name' => 'Store',
                'Type' => '1',
                'Parent' => '27',
                'Note' => NULL,
                'User' => 11,
                'created_at' => '2022-05-22 10:14:10',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '28',
            ),
            78 => 
            array (
                'id' => 40,
                'Code' => '20201',
                'Name' => 'ضريبة القيمة المضافة',
                'Type' => '1',
                'Parent' => '39',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-06-16 01:13:49',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '36',
            ),
            79 => 
            array (
                'id' => 17,
                'Code' => '3',
                'Name' => 'حقوق الملكية',
                'Type' => '0',
                'Parent' => '0',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-06-16 00:29:32',
                'updated_at' => '2022-10-18 22:42:47',
                'Account_Code' => '43',
            ),
            80 => 
            array (
                'id' => 818,
                'Code' => '60106',
                'Name' => 'Default',
                'Type' => '1',
                'Parent' => '53',
                'Note' => NULL,
                'User' => 11,
                'created_at' => '2022-04-13 02:51:34',
                'updated_at' => '2022-10-18 22:42:48',
                'Account_Code' => '66',
            ),
            81 => 
            array (
                'id' => 827,
                'Code' => '60108',
                'Name' => 'Delivery',
                'Type' => '1',
                'Parent' => '53',
                'Note' => NULL,
                'User' => 11,
                'created_at' => '2022-05-15 16:53:39',
                'updated_at' => '2022-10-18 22:42:48',
                'Account_Code' => '68',
            ),
            82 => 
            array (
                'id' => 825,
                'Code' => '602',
                'Name' => 'مصروفات عمومية وادارية',
                'Type' => '0',
                'Parent' => '20',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2022-04-13 19:12:04',
                'updated_at' => '2022-10-18 22:42:48',
                'Account_Code' => '69',
            ),
            83 => 
            array (
                'id' => 851,
                'Code' => '604',
                'Name' => 'خصم مسموح به',
                'Type' => '0',
                'Parent' => '20',
                'Note' => NULL,
                'User' => 1,
                'created_at' => NULL,
                'updated_at' => '2022-10-18 22:42:48',
                'Account_Code' => '71',
            ),
            84 => 
            array (
                'id' => 50,
                'Code' => '60401',
                'Name' => 'خصم مسموح به',
                'Type' => '1',
                'Parent' => '851',
                'Note' => NULL,
                'User' => 1,
                'created_at' => '2021-06-16 01:21:13',
                'updated_at' => '2022-10-18 22:42:48',
                'Account_Code' => '72',
            ),
            85 => 
            array (
                'id' => 852,
                'Code' => '607',
                'Name' => 'رسوم بنوك',
                'Type' => '0',
                'Parent' => '20',
                'Note' => NULL,
                'User' => 11,
                'created_at' => '2022-11-03 07:48:29',
                'updated_at' => '2022-11-03 07:48:29',
                'Account_Code' => '٠',
            ),
            86 => 
            array (
                'id' => 853,
                'Code' => '608',
                'Name' => 'مصاريف شركات التقسيط',
                'Type' => '0',
                'Parent' => '20',
                'Note' => NULL,
                'User' => 11,
                'created_at' => '2022-11-03 07:49:34',
                'updated_at' => '2022-11-03 07:49:34',
                'Account_Code' => '0',
            ),
            87 => 
            array (
                'id' => 854,
                'Code' => '60701',
                'Name' => 'رسوم خدمه',
                'Type' => '1',
                'Parent' => '852',
                'Note' => NULL,
                'User' => 11,
                'created_at' => '2022-11-03 07:49:52',
                'updated_at' => '2022-11-03 07:49:52',
                'Account_Code' => '0',
            ),
            88 => 
            array (
                'id' => 856,
                'Code' => '404',
                'Name' => 'إيردات اخري',
                'Type' => '0',
                'Parent' => '18',
                'Note' => NULL,
                'User' => 11,
                'created_at' => '2022-11-03 07:55:13',
                'updated_at' => '2022-11-03 07:55:13',
                'Account_Code' => '0',
            ),
            89 => 
            array (
                'id' => 888,
                'Code' => '1020103',
                'Name' => 'شركات التقسيط',
                'Type' => '0',
                'Parent' => '23',
                'Note' => NULL,
                'User' => 11,
                'created_at' => '2022-11-03 07:54:02',
                'updated_at' => '2022-11-03 07:54:02',
                'Account_Code' => '0',
            ),
            90 => 
            array (
                'id' => 895,
                'Code' => '40401',
                'Name' => 'ايردات رسوم الخدمه',
                'Type' => '1',
                'Parent' => '856',
                'Note' => NULL,
                'User' => 11,
                'created_at' => '2022-11-03 07:55:46',
                'updated_at' => '2022-11-03 07:55:46',
                'Account_Code' => '0',
            ),
            91 => 
            array (
                'id' => 2496,
                'Code' => '405',
                'Name' => 'يراد مصنعيه',
                'Type' => '1',
                'Parent' => '18',
                'Note' => NULL,
                'User' => 11,
                'created_at' => '2022-12-21 02:46:07',
                'updated_at' => '2022-12-21 02:46:07',
                'Account_Code' => '0',
            ),
            92 => 
            array (
                'id' => 2497,
                'Code' => '6060204',
                'Name' => 'تكلفه مصنعيه',
                'Type' => '1',
                'Parent' => '55',
                'Note' => NULL,
                'User' => 11,
                'created_at' => '2022-12-21 02:46:32',
                'updated_at' => '2022-12-21 02:46:32',
                'Account_Code' => '0',
            ),
            93 => 
            array (
                'id' => 15,
                'Code' => '1',
                'Name' => 'الأصول',
                'Type' => '0',
                'Parent' => '0',
                'Note' => NULL,
                'User' => 1,
                'created_at' => NULL,
                'updated_at' => NULL,
                'Account_Code' => '0',
            ),
            94 => 
            array (
                'id' => 2498,
                'Code' => '6060205',
                'Name' => 'تالف الصيانه',
                'Type' => '1',
                'Parent' => '55',
                'Note' => NULL,
                'User' => 11,
                'created_at' => '2023-01-16 18:58:53',
                'updated_at' => '2023-01-16 18:58:53',
                'Account_Code' => '0',
            ),
        ));
        
        
    }
}