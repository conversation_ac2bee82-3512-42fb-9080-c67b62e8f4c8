<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateBaseTables extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Create export_checks table
        Schema::create('export_checks', function (Blueprint $table) {
            $table->id();
            $table->string('Code')->nullable();
            $table->date('Date')->nullable();
            $table->string('Draw')->nullable();
            $table->text('Note')->nullable();
            $table->string('Check_Num')->nullable();
            $table->date('Due_Date')->nullable();
            $table->decimal('Amount', 15, 2)->nullable();
            $table->string('Status')->default('0');
            $table->text('Reason')->nullable();
            $table->string('Check_Type')->nullable();
            $table->string('Coin')->nullable();
            $table->string('Cost_Center')->nullable();
            $table->string('Account')->nullable();
            $table->string('Bank')->nullable();
            $table->string('Pay_Account')->nullable();
            $table->string('Bene_Account')->nullable();
            $table->string('User')->nullable();
            $table->text('File')->nullable();
            $table->timestamps();
        });

        // Create incom_checks table
        Schema::create('incom_checks', function (Blueprint $table) {
            $table->id();
            $table->string('Code')->nullable();
            $table->date('Date')->nullable();
            $table->string('Draw')->nullable();
            $table->text('Note')->nullable();
            $table->string('Check_Num')->nullable();
            $table->date('Due_Date')->nullable();
            $table->decimal('Amount', 15, 2)->nullable();
            $table->string('Status')->default('0');
            $table->text('Reason')->nullable();
            $table->string('Check_Type')->nullable();
            $table->string('Coin')->nullable();
            $table->string('Cost_Center')->nullable();
            $table->string('Account')->nullable();
            $table->string('Bank')->nullable();
            $table->string('Arrest_Account')->nullable();
            $table->string('Bene_Account')->nullable();
            $table->string('User')->nullable();
            $table->text('File')->nullable();
            $table->timestamps();
        });

        // Create company_data table
        Schema::create('company_data', function (Blueprint $table) {
            $table->id();
            $table->string('Name')->nullable();
            $table->string('NameEn')->nullable();
            $table->string('Phone1')->nullable();
            $table->string('Phone2')->nullable();
            $table->string('Phone3')->nullable();
            $table->string('Phone4')->nullable();
            $table->text('Address')->nullable();
            $table->text('AddressEn')->nullable();
            $table->string('Logo')->nullable();
            $table->string('Icon')->nullable();
            $table->text('Print_Text')->nullable();
            $table->text('Print_Text_Footer')->nullable();
            $table->text('Print_Text_Footer_Sales')->nullable();
            $table->text('Print_Text_Footer_Quote')->nullable();
            $table->text('Print_Text_Footer_Manufacturing')->nullable();
            $table->string('Seal')->nullable();
            $table->string('Name_Sales_Bill')->nullable();
            $table->string('Name_Sales_Order_Bill')->nullable();
            $table->string('Name_Quote_Bill')->nullable();
            $table->string('Logo_Store')->nullable();
            $table->string('Icon_Store')->nullable();
            $table->integer('View')->default(0);
            $table->text('Print_Text_Footer_Secretariat')->nullable();
            $table->string('Commercial_Record')->nullable();
            $table->string('Tax_File_Number')->nullable();
            $table->string('Tax_Registration_Number')->nullable();
            $table->string('Tax_activity_code')->nullable();
            $table->string('work_nature')->nullable();
            $table->string('Governrate')->nullable();
            $table->string('City')->nullable();
            $table->string('Place')->nullable();
            $table->string('Nationality')->nullable();
            $table->string('Buliding_Num')->nullable();
            $table->string('Street')->nullable();
            $table->string('Postal_Code')->nullable();
            $table->string('tax_magistrate')->nullable();
            $table->string('Client_ID')->nullable();
            $table->string('Serial_Client_ID')->nullable();
            $table->string('Version_Type')->nullable();
            $table->string('Computer_SN')->nullable();
            $table->string('Invoice_Type')->nullable();
            $table->string('Floor')->nullable();
            $table->string('Room')->nullable();
            $table->string('Landmark')->nullable();
            $table->text('Add_Info')->nullable();
            $table->string('POS_Version')->nullable();
            $table->string('Path')->nullable();
            $table->string('DB_Backup')->nullable();
            $table->timestamps();
        });

        // Create web_sliders table
        Schema::create('web_sliders', function (Blueprint $table) {
            $table->id();
            $table->string('Status')->default('1');
            $table->string('Arabic_Title')->nullable();
            $table->string('English_Title')->nullable();
            $table->text('Arabic_Desc')->nullable();
            $table->text('English_Desc')->nullable();
            $table->string('Image')->nullable();
            $table->string('Type')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('web_sliders');
        Schema::dropIfExists('company_data');
        Schema::dropIfExists('incom_checks');
        Schema::dropIfExists('export_checks');
    }
}
