<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateModulesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('modules', function (Blueprint $table) {
            $table->id();
            
            // ERP Module activation flags (0=disabled, 1=enabled)
            $table->string('Capital')->default('0');
            $table->string('Accounts')->default('1');
            $table->string('Stores')->default('1');
            $table->string('CRM')->default('1');
            $table->string('HR')->default('1');
            $table->string('Manufacturing')->default('1');
            $table->string('Maintenance')->default('1');
            $table->string('Secretariat')->default('1');
            $table->string('Petrol')->default('1');
            $table->string('ECommerce')->default('1');
            $table->string('Shipping')->default('1');
            $table->string('Bill_Electronic')->default('1');
            $table->string('Hotels')->default('1');
            $table->string('Resturant')->default('0');
            $table->string('Traning_Center')->default('0');
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('modules');
    }
}
