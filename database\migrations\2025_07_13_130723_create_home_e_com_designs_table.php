<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateHomeEComDesignsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('home_e_com_designs', function (Blueprint $table) {
            $table->id();
            $table->string('Slider_BG_Type')->nullable();
            $table->string('Slider_BG_Image')->nullable();
            $table->string('Slider_BG_Color')->nullable();
            $table->string('Slider_Button_BG_Color')->nullable();
            $table->string('Slider_Button_Txt_Color')->nullable();
            $table->string('Slider_Button_Hover_BG_Color')->nullable();
            $table->string('Slider_Button_Hover_Txt_Color')->nullable();
            $table->string('Slider_Title_Txt_Color')->nullable();
            $table->string('Slider_Desc_Txt_Color')->nullable();
            $table->string('Ads_Top_Img_First_BG_Color')->nullable();
            $table->string('Ads_Top_Img_First_Before_BG_Color')->nullable();
            $table->string('Ads_Top_Img_Second_BG_Color')->nullable();
            $table->string('Ads_Top_Img_Second_Before_BG_Color')->nullable();
            $table->string('Ads_Top_Img_Button_BG_Color')->nullable();
            $table->string('Ads_Top_Img_Button_Txt_Color')->nullable();
            $table->string('Ads_Top_Img_Button_Hover_BG_Color')->nullable();
            $table->string('Ads_Top_Img_Button_Hover_Txt_Color')->nullable();
            $table->string('Support_Icons_BG_Color')->nullable();
            $table->string('Support_Icons_Txt_Color')->nullable();
            $table->string('Support_Icons_Color')->nullable();
            $table->string('Ads_Bootom_Imgs_BG_Color')->nullable();
            $table->string('Ads_Bootom_Imgs_Middle_BG_Color')->nullable();
            $table->string('Ads_Bootom_Imgs_Button_BG_Color')->nullable();
            $table->string('Ads_Bootom_Imgs_Button_Txt_Color')->nullable();
            $table->string('Ads_Bootom_Imgs_Button_Hover_BG_Color')->nullable();
            $table->string('Ads_Bootom_Imgs_Button_Hover_Txt_Color')->nullable();
            $table->string('Partners_BG_Color')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('home_e_com_designs');
    }
}
