<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateIntrosTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('intros', function (Blueprint $table) {
            $table->id();
            
            // Introduction content
            $table->text('Arabic_About')->nullable();
            $table->text('English_About')->nullable();
            
            // Contact information
            $table->string('Phone_1')->nullable();
            $table->string('Phone_2')->nullable();
            $table->string('Phone_3')->nullable();
            $table->string('Phone_4')->nullable();
            
            // Terms content
            $table->text('Arabic_Terms')->nullable();
            $table->text('English_Terms')->nullable();
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('intros');
    }
}
