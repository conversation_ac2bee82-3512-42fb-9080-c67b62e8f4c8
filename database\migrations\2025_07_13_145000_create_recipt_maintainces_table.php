<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateReciptMaintaincesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('recipt_maintainces', function (Blueprint $table) {
            $table->id();
            
            // Basic maintenance receipt information
            $table->string('Code')->nullable();
            $table->date('Date')->nullable();
            $table->text('Note')->nullable();
            $table->string('Serial_Num')->nullable();
            
            // Financial information
            $table->decimal('Total', 15, 2)->default(0);
            $table->integer('Product_Numbers')->default(0);
            $table->decimal('Total_Qty', 15, 2)->default(0);
            $table->decimal('Total_Discount', 15, 2)->default(0);
            $table->decimal('Total_Cost', 15, 2)->default(0);
            $table->decimal('Total_Bf_Taxes', 15, 2)->default(0);
            $table->decimal('Total_Taxes', 15, 2)->default(0);
            $table->decimal('Pay', 15, 2)->default(0);
            
            // Device information
            $table->string('Draw')->nullable();
            $table->string('Company')->nullable();
            $table->string('Device_Type')->nullable();
            $table->string('Device_Case')->nullable();
            
            // References
            $table->string('Coin')->nullable();
            $table->string('Cost_Center')->nullable();
            $table->string('Account')->nullable();
            $table->string('User')->nullable();
            $table->string('Store')->nullable();
            $table->string('Branch')->nullable();
            
            // Status and workflow
            $table->string('Status')->default('0');
            $table->string('Payment_Method')->nullable();
            $table->string('Password')->nullable();
            
            // Maintenance specific fields
            $table->text('Eng_Note')->nullable();
            $table->string('Reason')->nullable();
            $table->text('Report_Client')->nullable();
            $table->text('Work')->nullable();
            $table->string('Eng')->nullable();
            $table->string('Recipient')->nullable();
            $table->string('RefuseReason')->nullable();
            $table->text('NoteRecived')->nullable();
            $table->string('Returned')->nullable();
            $table->string('CustomerGroup')->nullable();
            
            // Additional fields
            $table->text('Pattern_Image')->nullable();
            $table->string('Time')->nullable();
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('recipt_maintainces');
    }
}
