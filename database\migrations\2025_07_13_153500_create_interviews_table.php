<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateInterviewsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('interviews', function (Blueprint $table) {
            $table->id();
            
            // Interview information
            $table->string('Code')->nullable();
            $table->date('Date')->nullable();
            $table->string('Time')->nullable();
            $table->string('Status')->nullable();
            $table->text('StatusNote')->nullable();
            $table->string('Rate')->nullable();
            $table->text('Note')->nullable();
            
            // References
            $table->string('Emp')->nullable();    // Employee reference
            $table->string('Client')->nullable(); // Client reference
            $table->string('Type')->nullable();   // Interview type reference
            $table->string('User')->nullable();   // Admin user reference
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('interviews');
    }
}
