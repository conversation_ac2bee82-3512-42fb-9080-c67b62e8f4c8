<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateReturnSalesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('return_sales', function (Blueprint $table) {
            $table->id();
            
            // Basic return sales information
            $table->string('Code')->nullable();
            $table->date('Date')->nullable();
            
            // Return quantities and values
            $table->decimal('Total_Return_Qty', 15, 2)->default(0);
            $table->decimal('Total_Return_Value', 15, 2)->default(0);
            $table->decimal('Total_BF_Taxes', 15, 2)->default(0);
            $table->decimal('Total_Taxes', 15, 2)->default(0);
            $table->decimal('Total_Discount', 15, 2)->default(0);
            
            // Reference to original sale
            $table->string('Sales')->nullable(); // References sales table
            $table->decimal('Pay', 15, 2)->default(0);
            $table->string('Payment_Method')->nullable();
            
            // User and system info
            $table->string('User')->nullable();
            $table->string('Time')->nullable();
            $table->string('Branch')->nullable();
            $table->string('CustomerGroup')->nullable();
            $table->string('Refernce_Number')->nullable();
            
            // References to other entities
            $table->string('Safe')->nullable();
            $table->string('Client')->nullable();
            $table->string('Executor')->nullable();
            $table->string('Delegate')->nullable();
            $table->string('Store')->nullable();
            $table->string('Coin')->nullable();
            $table->string('Cost_Center')->nullable();
            $table->string('Ship')->nullable();
            
            // Payment terms
            $table->decimal('Later_Due', 15, 2)->default(0);
            $table->string('InstallCompany')->nullable();
            $table->string('Delivery')->nullable();
            
            // Status and control fields
            $table->string('Sent')->default('0');
            
            // Tax and billing
            $table->string('TaxBill')->nullable();
            $table->string('TaxCode')->nullable();
            
            // Electronic billing
            $table->string('uuid')->nullable();
            $table->string('longId')->nullable();
            $table->string('hashKey')->nullable();
            $table->string('submissionId')->nullable();
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('return_sales');
    }
}
