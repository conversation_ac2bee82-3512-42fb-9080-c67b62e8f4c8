<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSalesOrdersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('sales_orders', function (Blueprint $table) {
            $table->id();
            
            // Order basic information
            $table->string('Order_Number')->unique()->nullable();
            $table->integer('Order_Type')->default(1); // 1=Shop Order, 2=POS Order, etc.
            $table->string('Status')->default('Pending'); // Pending, Processing, Shipped, Delivered, Cancelled
            $table->date('Order_Date')->nullable();
            $table->timestamp('Order_Time')->nullable();
            
            // Customer information
            $table->unsignedBigInteger('Customer_ID')->nullable();
            $table->string('Customer_Name')->nullable();
            $table->string('Customer_Email')->nullable();
            $table->string('Customer_Phone')->nullable();
            
            // Shipping information
            $table->text('Shipping_Address')->nullable();
            $table->string('Shipping_City')->nullable();
            $table->string('Shipping_State')->nullable();
            $table->string('Shipping_Country')->nullable();
            $table->string('Shipping_Postal_Code')->nullable();
            $table->string('Shipping_Method')->nullable();
            $table->decimal('Shipping_Cost', 10, 2)->default(0);
            
            // Billing information
            $table->text('Billing_Address')->nullable();
            $table->string('Billing_City')->nullable();
            $table->string('Billing_State')->nullable();
            $table->string('Billing_Country')->nullable();
            $table->string('Billing_Postal_Code')->nullable();
            
            // Order totals
            $table->decimal('Subtotal', 10, 2)->default(0);
            $table->decimal('Tax_Amount', 10, 2)->default(0);
            $table->decimal('Discount_Amount', 10, 2)->default(0);
            $table->decimal('Total_Amount', 10, 2)->default(0);
            
            // Payment information
            $table->string('Payment_Method')->nullable();
            $table->string('Payment_Status')->default('Pending'); // Pending, Paid, Failed, Refunded
            $table->string('Payment_Reference')->nullable();
            $table->timestamp('Payment_Date')->nullable();
            
            // Additional fields
            $table->text('Notes')->nullable();
            $table->string('Currency', 3)->default('USD');
            $table->unsignedBigInteger('Created_By')->nullable();
            $table->unsignedBigInteger('Updated_By')->nullable();
            
            // Tracking
            $table->string('Tracking_Number')->nullable();
            $table->timestamp('Shipped_Date')->nullable();
            $table->timestamp('Delivered_Date')->nullable();
            
            $table->timestamps();
            
            // Indexes
            $table->index('Order_Type');
            $table->index('Status');
            $table->index('Customer_ID');
            $table->index('Order_Date');
            $table->index('Payment_Status');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('sales_orders');
    }
}
