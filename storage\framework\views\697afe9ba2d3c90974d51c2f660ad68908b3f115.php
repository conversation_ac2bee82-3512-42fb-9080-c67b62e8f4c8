<?php $__env->startSection('content'); ?>
<?php
use App\Models\AccountsDefaultData;
$Def=AccountsDefaultData::orderBy('id','desc')->first();
?>
<title><?php echo e(trans('admin.Safes_Transfer')); ?></title>
   <main id="js-page-content" role="main" class="page-content">
                    <ol class="breadcrumb page-breadcrumb">
                        <li class="breadcrumb-item"><a href="javascript:void(0);"><?php echo e(trans('admin.Accounts')); ?></a></li>
                        <li class="breadcrumb-item"><?php echo e(trans('admin.Safes_Transfer')); ?></li>
                 <li class="position-absolute pos-top pos-right d-none d-sm-block"><span
                                class="js-get-date"></span></li>
                    </ol>
                    
                    
                    <div class="row">
                        <div class="col-lg-12">
                            <div id="panel-2" class="panel">
                                <div class="panel-hdr">
                                    <h2>
                                        <span class="fw-300"><i>   <?php echo e(trans('admin.Safes_Transfer')); ?> </i></span>
                                    </h2>
                                </div>
                                <div class="panel-container show">
                                         <span id="ex"> <?php echo $__env->make('admin.layouts.messages', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>  
                                    <div class="panel-content">
                                <form action="<?php echo e(url('AddSafeTransfer')); ?>" method="post">
                                    <?php echo csrf_field(); ?>

                                          <?php echo view('honeypot::honeypotFormFields'); ?>
                                          
                                          
                                          
                                          
                            <div class="form-row">
                                <div class="form-group col-lg-2">
                                    <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Code')); ?> </label>
                                    <input type="text"  value="<?php echo e($Code); ?>" class="form-control" disabled>
                                    <input type="hidden" name="Code"  value="<?php echo e($Code); ?>">
                                </div>
                                
                                
                                  <?php if(auth()->guard('admin')->user()->emp == 0): ?>
                                <div class="form-group col-lg-2">
                                    <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Date')); ?></label>
                                    <input type="date" name="Date" value="<?php echo e(date('Y-m-d')); ?>" class="form-control" required>
                                </div>
                                  <?php else: ?>
                               
                                        
                                    <?php if(auth()->guard('admin')->user()->Date == 1): ?>
                                <div class="form-group col-lg-2">
                                    <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Date')); ?></label>
                                    <input type="date" name="Date" value="<?php echo e(date('Y-m-d')); ?>" class="form-control" required>
                                </div>
                                
                                
                                    <?php else: ?>
                                <div class="form-group col-lg-2">
                                    <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Date')); ?></label>
                                   <input type="date" name="Date" value="<?php echo e(date('Y-m-d')); ?>" class="form-control" required readonly>
                            </div>
                                    <?php endif; ?>
                                   
                                 <?php endif; ?>    
                                    
                                    
                                <div class="form-group col-lg-2">
                                    <label class="form-label" for=""><?php echo e(trans('admin.Branch')); ?></label>
                                    <select class="select2 form-control w-100" name="Branch">
                                    <option value=""><?php echo e(trans('admin.Branch')); ?></option>        
                                    <?php $__currentLoopData = $Branches; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $bran): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($bran->id); ?>">
                                  
                                 <?php echo e(app()->getLocale() == 'ar' ?$bran->Arabic_Name :$bran->English_Name); ?>            
                                    </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>    
                                    
                                    
                                    
                                    
                                        
                                        
                                <div class="form-group col-lg-2">
                                    <label class="form-label" for=""><?php echo e(trans('admin.Currency')); ?></label>
                                    <select class="select2 form-control w-100" name="Coin" required>
                                    <option value=""><?php echo e(trans('admin.Currency')); ?></option>             
                                                <?php $__currentLoopData = $Coins; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $coin): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($coin->id); ?>" <?php if($coin->id == $Def->Coin): ?> selected <?php endif; ?>>
                                        <?php echo e(app()->getLocale() == 'ar' ?$coin->Arabic_Name :$coin->English_Name); ?>        
                                    </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                                
                                
                                
                                <div class="form-group col-lg-2">
                                    <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Draw')); ?></label>
                                    <input type="number" step="any" name="Draw" value="<?php echo e($Def->Draw); ?>" class="form-control" required />
                                </div>
                                
                                
                                <div class="form-group col-lg-2">
                                    <label class="form-label" for=""><?php echo e(trans('admin.Cost_Center')); ?></label>
                                    <select class="select2 form-control w-100" name="Cost_Center">
                                    <option value=""><?php echo e(trans('admin.Cost_Center')); ?></option>        
                                      <?php $__currentLoopData = $CostCenters; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cost): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($cost->id); ?>">
                                      <?php echo e(app()->getLocale() == 'ar' ?$cost->Arabic_Name :$cost->English_Name); ?>        
                                    </option>
                                      <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div> 
                                
                                
                                
                                        
                                <!-- new row -->
                                <?php if(auth()->guard('admin')->user()->emp != 0): ?>
                                <div class="form-group col-lg-2">
                                    <label class="form-label" for=""><?php echo e(trans('admin.From_Safe')); ?></label>
                                    <select class="js-data-example-ajax form-control w-100" id="Safe"  disabled    required>
                                    <option value="<?php echo e(auth()->guard('admin')->user()->safe); ?>" selected>
                                    <?php echo e(auth()->guard('admin')->user()->safe()->first()->Name); ?>    
                                    </option>    
                                    </select>
                                    <input type="hidden" name="From_Safe"  value="<?php echo e(auth()->guard('admin')->user()->safe); ?>">                   
                                </div>
                                <?php else: ?>
                                
                                
                                <div class="form-group col-lg-2">
                                    <label class="form-label" for=""><?php echo e(trans('admin.From_Safe')); ?></label>
                                    <select class="js-data-example-ajax form-control w-100" id="Safe"  name="From_Safe"  required>
                                    </select>
                                </div>
                                <?php endif; ?>    
                                        
                                        
                                        
                                <div class="form-group col-lg-2">
                                    <label class="form-label"> <?php echo e(trans('admin.Safe_Balance')); ?> </label>
                                    <input type="text" disabled class="form-control" id="SafeBalance">
                                </div>
                                        
                                        
                                <div class="form-group col-lg-2">
                                    <label class="form-label" for=""> <?php echo e(trans('admin.To_Safe')); ?></label>
                                    <select class="select2 form-control w-100"  name="To_Safe"  required>
                                    <option value=""><?php echo e(trans('admin.To_Safe')); ?></option>    
                                    <?php $__currentLoopData = $Safes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sal): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($sal->id); ?>">
                               <?php echo e(app()->getLocale() == 'ar' ?$sal->Name :$sal->NameEn); ?>                
                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                                
                                
                                <div class="form-group col-lg-2">
                                    <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Amount')); ?></label>
                                    <input type="number" name="Amount" id="Amount" onkeyup="ChangeP()" onclick="ChangeP()" onkeypress="ChangeP()"  class="form-control" required>
                                </div>
                                
                                
                                
                                
                                <div class="form-group col-lg-2">
                                    <label class="form-label" for=""><?php echo e(trans('admin.Delegate')); ?></label>
                                    <select class="select2 form-control w-100" name="Delegate">
                                    <option value=""><?php echo e(trans('admin.Delegate')); ?></option>        
                                    <?php $__currentLoopData = $Emps; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $emp): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($emp->id); ?>">
                             
                                        <?php echo e(app()->getLocale() == 'ar' ?$emp->Name :$emp->NameEn); ?>               
                                    </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div> 
                                        
                                        
                                <div class="form-group col-lg-2">
                                    <label class="form-label" for="simpleinput"><?php echo e(trans('admin.File')); ?></label>
                                    <input type="file" name="File">
                                </div>
                                
                                
                                
                                
                                
                                <div class="form-group col-lg-9">
                                    <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Notes')); ?></label>
                                    <input type="text" name="Note" class="form-control">
                                </div>
                                        
                                        
                                <div class="buttons mt-3 pt-2" id="Submit">
                                    <button type="submit" class="btn btn-primary"> <i class="fal fa-folder"></i> <?php echo e(trans('admin.Save')); ?> </button>
                                </div>
                                
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    </div>
                    </div>
                    
                    
                    
                    
                    
                    <div class="row">
                        
                    </div>
                   
                </main>

<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/datagrid/datatables/datatables.bundle.css')); ?>">
    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/formplugins/summernote/summernote.css')); ?>">
    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/formplugins/select2/select2.bundle.css')); ?>">

    <script src="<?php echo e(asset('Admin/js/datagrid/datatables/datatables.bundle.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/datagrid/datatables/datatables.export.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/formplugins/summernote/summernote.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/formplugins/select2/select2.bundle.js')); ?>"></script>

     <script>
        var autoSave = $('#autoSave');
        var interval;
        var timer = function()
        {
            interval = setInterval(function()
            {
                //start slide...
                if (autoSave.prop('checked'))
                    saveToLocal();

                clearInterval(interval);
            }, 3000);
        };

        //save
        var saveToLocal = function()
        {
            localStorage.setItem('summernoteData', $('#saveToLocal').summernote("code"));
            console.log("saved");
        }

        //delete 
        var removeFromLocal = function()
        {
            localStorage.removeItem("summernoteData");
            $('#saveToLocal').summernote('reset');
        }

        $(document).ready(function()
        {
            //init default
            $('.js-summernote').summernote(
            {
                height: 200,
                tabsize: 2,
                placeholder: "Type here...",
                dialogsFade: true,
                toolbar: [
                    ['style', ['style']],
                    ['font', ['strikethrough', 'superscript', 'subscript']],
                    ['font', ['bold', 'italic', 'underline', 'clear']],
                    ['fontsize', ['fontsize']],
                    ['fontname', ['fontname']],
                    ['color', ['color']],
                    ['para', ['ul', 'ol', 'paragraph']],
                    ['height', ['height']]
                    ['table', ['table']],
                    ['insert', ['link', 'picture', 'video']],
                    ['view', ['fullscreen', 'codeview', 'help']]
                ],
                callbacks:
                {
                    //restore from localStorage
                    onInit: function(e)
                    {
                        $('.js-summernote').summernote("code", localStorage.getItem("summernoteData"));
                    },
                    onChange: function(contents, $editable)
                    {
                        clearInterval(interval);
                        timer();
                    }
                }
            });

            //load emojis
            $.ajax(
            {
                url: 'https://api.github.com/emojis',
                async: false
            }).then(function(data)
            {
                window.emojis = Object.keys(data);
                window.emojiUrls = data;
            });

            //init emoji example
            $(".js-hint2emoji").summernote(
            {
                height: 100,
                toolbar: false,
                placeholder: 'type starting with : and any alphabet',
                hint:
                {
                    match: /:([\-+\w]+)$/,
                    search: function(keyword, callback)
                    {
                        callback($.grep(emojis, function(item)
                        {
                            return item.indexOf(keyword) === 0;
                        }));
                    },
                    template: function(item)
                    {
                        var content = emojiUrls[item];
                        return '<img src="' + content + '" width="20" /> :' + item + ':';
                    },
                    content: function(item)
                    {
                        var url = emojiUrls[item];
                        if (url)
                        {
                            return $('<img />').attr('src', url).css('width', 20)[0];
                        }
                        return '';
                    }
                }
            });

            //init mentions example
            $(".js-hint2mention").summernote(
            {
                height: 100,
                toolbar: false,
                placeholder: "type starting with @",
                hint:
                {
                    mentions: ['jayden', 'sam', 'alvin', 'david'],
                    match: /\B@(\w*)$/,
                    search: function(keyword, callback)
                    {
                        callback($.grep(this.mentions, function(item)
                        {
                            return item.indexOf(keyword) == 0;
                        }));
                    },
                    content: function(item)
                    {
                        return '@' + item;
                    }
                }
            });

        });

    </script>
    <script>
      

        function Insert(){
        var debit = document.getElementById('debit').value;
        var creditor = document.getElementById('creditor').value;
        // var accountCode = document.getElementById('account-code').value;
        var accountName = document.getElementById('select-one').value;
        // var centersCost = document.getElementById('centers-cost').value;
        var statement = document.getElementById('statement').value;

        var table =  ` <tr> 
                                <td>${debit}</td>
                                <td>${creditor}</td>
                                <td></td>
                                <td>${accountName}</td>
                                <td></td>
                                <td>${statement}</td>
                                <td>
                                    <button type="button" class="btn btn-default"><i class="fal fa-trash"></i></button>
                                </td>
                             </tr>`;
                             document.getElementById('data-dt').innerHTML += table;
        }

        

    </script>

   <!-- Search Selecet -->
 <script>
        $(document).ready(function()
        {
            $(function()
            {
                $('.select2').select2();

                $(".select2-placeholder-multiple").select2(
                {
                    placeholder: "Select State"
                });
                $(".js-hide-search").select2(
                {
                    minimumResultsForSearch: 1 / 0
                });
                $(".js-max-length").select2(
                {
                    maximumSelectionLength: 2,
                    placeholder: "Select maximum 2 items"
                });
                $(".select2-placeholder").select2(
                {
                    placeholder: "Select a state",
                    allowClear: true
                });

                $(".js-select2-icons").select2(
                {
                    minimumResultsForSearch: 1 / 0,
                    templateResult: icon,
                    templateSelection: icon,
                    escapeMarkup: function(elm)
                    {
                        return elm
                    }
                });

                function icon(elm)
                {
                    elm.element;
                    return elm.id ? "<i class='" + $(elm.element).data("icon") + " mr-2'></i>" + elm.text : elm.text
                }

          
           
      $('#Safe').select2({
    placeholder: "select...",
    ajax: {
        type: "GET",
        dataType: 'json',
        url: 'AllSafes',
        processResults: function (data) {
          return {
            results: $.map(data, function(obj, index) {
              return { id: index, text: obj };
            })
          };
            
            	console.log(data);
            
        },
     data: function (params) {  
   
            
                   var query = {
                            search: params.term,
                        };
                                
            
              $.ajax({
                              url: 'AllSafesJ/'+params.term,
                              type:"GET",
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },

                              success:function(data) {
                                          $('#Safe').empty();  
                                  $.each(data, function(key, value){
   
                         $('#Safe').append('<option value="'+ key +'">' + value + '</option>');
                   
                                  });
                                  
                                        var Balance =$('#SafeBalance').val();
        var Amount =$('#Amount').val();
        
        if(parseFloat(Amount) >  parseFloat(Balance)){
            
            document.getElementById('Submit').style.display='none';
            
        }else{
            
            document.getElementById('Submit').style.display='block';
            
        }
          
                                  
                                  
                                  
                                            var countryId = $('#Safe').val();
                      if(countryId) {
                          $.ajax({
                              url: 'SafeBalanceFilter/'+countryId,
                              type:"GET",
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },
   
                              success:function(data) {
                                  $.each(data, function(key, value){
   
        
                    $('#SafeBalance').val(key); 
                    $('#SafeBalance').val(value); 
                                  });
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });
                      } else {
   
                          $('select[name="state"]').empty();
                      }
   
                                  
                                  
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });
            
            
            
        }
    }
  });

    
$('#Safe').on('select2:select', function (e) {
	console.log("select done", e.params.data);
});
                       
             
      
                
      $('#ToSafe').select2({
    placeholder: "select...",
    ajax: {
        type: "GET",
        dataType: 'json',
        url: 'AllSafes',
        processResults: function (data) {
          return {
            results: $.map(data, function(obj, index) {
              return { id: index, text: obj };
            })
          };
            
            	console.log(data);
            
        },
     data: function (params) {  
   
            
                   var query = {
                            search: params.term,
                        };
                                
            
              $.ajax({
                              url: 'AllSafesJ/'+params.term,
                              type:"GET",
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },

                              success:function(data) {
                                          $('#ToSafe').empty();  
                                  $.each(data, function(key, value){
   
                         $('#ToSafe').append('<option value="'+ key +'">' + value + '</option>');
                   
                                  });
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });
            
            
            
        }
    }
  });

    
$('#ToSafe').on('select2:select', function (e) {
	console.log("select done", e.params.data);
});
                       
                   
                
                
             
            });
        });



    </script>

   <!-- Safe Balance  -->
<script>
   $(document).ready(function() {
   
                  $('#Safe').on('change', function(){
                      var countryId = $(this).val();
                      if(countryId) {
                          $.ajax({
                              url: 'SafeBalanceFilter/'+countryId,
                              type:"GET",
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },
   
                              success:function(data) {
                                  $.each(data, function(key, value){
   
        
                        $('#SafeBalance').val(parseFloat(key).toFixed(2)); 
                    $('#SafeBalance').val(parseFloat(value).toFixed(2)); 
                                  });
                                  
                                          var Balance =$('#SafeBalance').val();
        var Amount =$('#Amount').val();
        
        if(parseFloat(Amount) >  parseFloat(Balance)){
            
            document.getElementById('Submit').style.display='none';
            
        }else{
            
            document.getElementById('Submit').style.display='block';
            
        }
        
                                  
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });
                      } else {
   
                          $('select[name="state"]').empty();
                      }
   
                  });
   
              });
</script>  

<script>
 $(document).ready(function() {

                      var countryId = $('#Safe').val();
                          $.ajax({
                              url: 'SafeBalanceFilter/'+countryId,
                              type:"GET",
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },
   
                              success:function(data) {
                                  $.each(data, function(key, value){
                    $('#SafeBalance').val(key); 
                    $('#SafeBalance').val(value); 
                                  });
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });
             
   
             
   
              });
    
</script>

<script>
    function  ChangeP(){
        
        var Balance =$('#SafeBalance').val();
        var Amount =$('#Amount').val();
        
        if(parseFloat(Amount) >  parseFloat(Balance)){
            
            
            document.getElementById('Submit').style.display='none';
            
        }else{
            
            if(parseFloat(Amount) < 0){
                document.getElementById('Submit').style.display='none';
               }else{
                  document.getElementById('Submit').style.display='block';
               }
         
            
        }
        
        
    }
</script>

<style>
    .form-control {
    border: 2px solid #584576 !important;
   }
   
   body {
    overflow-x: hidden;
   }
   .panel-content {
      overflow: hidden;
   }
</style>

<?php $__env->stopPush(); ?>
<?php echo $__env->make('admin.index', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp2\htdocs\erp\resources\views/admin/Accounts/SafesTransfer.blade.php ENDPATH**/ ?>