<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateItemsGroupsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('items_groups', function (Blueprint $table) {
            $table->id();
            
            // Basic group information
            $table->string('Code')->nullable();
            $table->string('Name')->nullable();
            $table->string('NameEn')->nullable();
            $table->string('Type')->nullable();
            $table->string('Parent')->nullable();
            $table->text('Note')->nullable();
            $table->string('Image')->nullable();
            
            // Group settings
            $table->string('Discount')->nullable();
            $table->string('Sales_Show')->nullable();
            $table->string('Store_Show')->nullable();
            $table->string('Printer')->nullable();
            $table->string('Arrange')->nullable();
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('items_groups');
    }
}
