@extends('admin.index')
@section('content')
    @php
use App\Models\CompanyData;
$Def=CompanyData::orderBy('id','desc')->first();
use App\Models\Modules;
use App\Models\ProductSales;
$Modules=Modules::orderBy('id','desc')->first();


@endphp
<style>


@media only screen and (max-width: 900px){
  .DirctionDiff{
    direction:ltr;
    text-align: left;
    }}


</style>
 <title>OST</title>




               <main id="js-page-content" role="main" class="page-content">





 <div class="row DirctionDiff">
     <div class="col-lg-3">
         <ol class="breadcrumb page-breadcrumb">
            <li class="breadcrumb-item"><a href="{{url('OstAdmin')}}">{{trans('admin.Ost')}}</a></li>
            <li class="breadcrumb-item active"> {{trans('admin.Home')}}</li>
         </ol>
     </div>
     <div class="col-lg-3">
         <h1 class="subheader-title">
           <i class='subheader-icon fal fa-chart-area' style="margin-left:10px;"></i><span class='fw-300'>{{trans('admin.Dashboard')}}




             </span>
         </h1>
     </div>
       @if(auth()->guard('admin')->user()->vend == 0 and  auth()->guard('admin')->user()->cli == 0)
      <div class="col-lg-3 col-md-6">
              @can('نسخ قاعده البيانات')
         <div class="download d-flex">


             @if($Def->DB_Backup == 0)
                  <a href="{{url('BackupDB')}}" class="btn btn-tex btn-primary">
              <i class="fal fa-download"></i>
            </a>
             @else
          <a href="{{url('serverDBBackup')}}" class="btn btn-tex btn-primary">
              <i class="fal fa-download"></i>
            </a>
               @endif


            <H3 class="text-download">{{trans('admin.BackupDB')}}</H3>
         </div>
          @endcan
     </div>
     @endif
     <div class="col-lg-3 col-md-6">
         <ol class="breadcrumb page-breadcrumb">
            <li class="position-absolute pos-top pos-right d-none d-sm-block"><span class="js-get-date"></span></li>
         </ol>
     </div>
 </div>

 <style>
     .btn-tex {
         padding: 6px !important;
         margin-left: 5px;
     }
     .text-download {
         font-size: 15px;
         padding-top: 5px;
     }
 </style>



               @if($Def->HomeMainScreen == 0)
                     @if(auth()->guard('admin')->user()->vend == 0)
                     @if(auth()->guard('admin')->user()->cli == 0)
                    @if($Modules->Stores  ==  1)
                  <div class="row">

                       @can('اضافه  مبيعات')
                                <div class="col-md-2 col-6">
                                 <div class="shortcut">
                                       <img style="width:100px;height:100px" src='{{asset("Admin/assets/images/019.gif")}}'>
                                  <a href="{{url('Sales')}}" > <p>  {{trans('admin.Sales')}} </p></a>
                                 </div>
                               </div>
                      @endcan
                      @can('جدول المبيعات')
                                <div class="col-md-2 col-6">
                                 <div class="shortcut">
                                       <img style="width:100px;height:100px" src='{{asset("Admin/assets/images/020.gif")}}'>
                                  <a href="{{url('SalesSechdule')}}"> <p>{{trans('admin.SalesSechdule')}}</p></a>
                                 </div>
                               </div>
                      @endcan

                            @can('اضافه سند قبض')
                               <div class="col-md-2 col-6">
                                 <div class="shortcut">
                                       <img style="width:100px;height:100px" src='{{asset("Admin/assets/images/003.gif")}}'>
                               <a href="{{url('Receipt_Voucher')}}"> <p> {{trans('admin.Receipt_Voucher')}} </p> </a>
                                 </div>
                                 </div>
                       @endcan
                       @can('اضافه سند صرف')
                                 <div class="col-md-2 col-6">
                                 <div class="shortcut">
                                       <img style="width:100px;height:100px" src='{{asset("Admin/assets/images/004.gif")}}'>
                           <a href="{{url('Payment_Voucher')}}"><p>   {{trans('admin.Payment_Voucher')}}  </p></a>
                                 </div>
                                 </div>
                     @endcan
                       @can('تحويلات الخزائن')
                                   <div class="col-md-2 col-6">
                                 <div class="shortcut">
                                       <img style="width:100px;height:100px" src='{{asset("Admin/assets/images/005.gif")}}'>
                                  <a href="{{url('SafesTransfer')}}"><p> {{trans('admin.Safes_Transfer')}}</p></a>
                                 </div>

                                 </div>
                      @endcan

                          @can('جدول تحويلات الخزائن')
                                 <div class="col-md-2 col-6">
                                 <div class="shortcut">
                                       <img style="width:100px;height:100px" src='{{asset("Admin/assets/images/006.gif")}}'>
                                <a href="{{url('SafesTransferSechdule')}}"><p>  {{trans('admin.Safes_Transfer_Sechdule')}}</p></a>

                                 </div>
                                 </div>
                               @endcan

                      @can('اضافه تحويلات المخازن')
                                    <div class="col-md-2 col-6">
                                 <div class="shortcut">
                                       <img style="width:100px;height:100px" src='{{asset("Admin/assets/images/035.gif")}}'>
                            <a href="{{url('StoresTransfers')}}"><p>  {{trans('admin.Stores_Transfers')}}</p></a>
                                 </div>
                                 </div>
                    @endcan

                      @can('جدول تحويلات المخازن')
                                  <div class="col-md-2 col-6">
                                 <div class="shortcut">
                                       <img style="width:100px;height:100px" src='{{asset("Admin/assets/images/036.gif")}}'>
                                  <a href="{{url('StoresTransfersSechdule')}}"> <p>{{trans('admin.Stores_Transfers_Sechdule')}}</p></a>
                                 </div>
                               </div>
                      @endcan

                       @can('دليل الاصناف')
                                  <div class="col-md-2 col-6">
                                 <div class="shortcut">
                                       <img style="width:100px;height:100px" src='{{asset("Admin/assets/images/031.gif")}}'>
                   <a href="{{url('ItemsGuide')}}"><p>   {{trans('admin.Items_Guide')}} </p></a>
                                 </div>
                                 </div>
                      @endcan

                        @can('اضافه صنف')
                                  <div class="col-md-2 col-6">
                                 <div class="shortcut">
                                       <img style="width:100px;height:100px" src='{{asset("Admin/assets/images/032.gif")}}'>
                  <a href="{{url('Add_Items')}}"><p> {{trans('admin.Add_Items')}}</p></a>
                                 </div>
                                 </div>
                      @endcan

                      @can('جدول الاصناف')
                                    <div class="col-md-2 col-6">
                                 <div class="shortcut">
                                       <img style="width:100px;height:100px" src='{{asset("Admin/assets/images/033.gif")}}'>
                         <a href="{{url('Products_Sechdule')}}"><p>{{trans('admin.Products_Sechdule')}} </p></a>
                                 </div>
                                 </div>
                      @endcan
                          @can('اضافه عملاء')
                                <div class="col-md-2 col-6">
                                 <div class="shortcut">
                                       <img style="width:100px;height:100px" src='{{asset("Admin/assets/images/013.gif")}}'>
                                  <a href="{{url('AddClients')}}"> <p>{{trans('admin.Add_Clients')}}</p></a>
                                 </div>
                               </div>
                      @endcan
                        @can('العملاء')
                                <div class="col-md-2 col-6">
                                 <div class="shortcut">
                                       <img style="width:100px;height:100px" src='{{asset("Admin/assets/images/014.gif")}}'>
                                  <a href="{{url('Clients')}}"> <p> {{trans('admin.Clients')}}</p></a>
                                 </div>
                               </div>
                      @endcan
                           @can('الموردين')
                                 <div class="col-md-2 col-6">
                                 <div class="shortcut">
                                       <img style="width:100px;height:100px" src='{{asset("Admin/assets/images/007.gif")}}'>
                                  <a href="{{url('Vendors')}}"> <p>{{trans('admin.Vendors')}} </p></a>
                                 </div>
                               </div>
                      @endcan
                   @can('اضافه امر شراء')
                                 <div class="col-md-2 col-6">
                                 <div class="shortcut">
                                       <img style="width:100px;height:100px" src='{{asset("Admin/assets/images/008.gif")}}'>
                                  <a href="{{url('PurchasesOrder')}}"> <p>{{trans('admin.PurchasesOrder')}} </p></a>
                                 </div>
                               </div>
                    @endcan
                        @can('جدول آوامر الشراء')
                                 <div class="col-md-2 col-6">
                                 <div class="shortcut">
                                       <img style="width:100px;height:100px" src='{{asset("Admin/assets/images/009.gif")}}'>
                                  <a href="{{url('PurchasesOrderSechdule')}}"> <p>{{trans('admin.PurchasesOrderSechdule')}}</p></a>
                                 </div>
                               </div>
                      @endcan
                      @can('اضافه مشتريات')
                                 <div class="col-md-2 col-6">
                                 <div class="shortcut">
                                       <img style="width:100px;height:100px" src='{{asset("Admin/assets/images/010.gif")}}'>
                                  <a href="{{url('Purchases')}}"> <p>{{trans('admin.Purchases')}} </p></a>
                                 </div>
                               </div>
                      @endcan
                    @can('جدول المشتريات')
                                 <div class="col-md-2 col-6">
                                 <div class="shortcut">
                                       <img style="width:100px;height:100px" src='{{asset("Admin/assets/images/011.gif")}}'>
                                  <a href="{{url('PurchasesSechdule')}}"> <p>{{trans('admin.PurchasesSechdule')}} </p></a>
                                 </div>
                               </div>
                      @endcan
                    @can('الفواتير المعلقه مشتريات')
                                <div class="col-md-2 col-6">
                                 <div class="shortcut">
                                       <img style="width:100px;height:100px" src='{{asset("Admin/assets/images/012.gif")}}'>
                                  <a href="{{url('PurchasesHold')}}"> <p>{{trans('admin.PurchasesHold')}} </p></a>
                                 </div>
                               </div>
                      @endcan


                       @can('اضافه عرض سعر')
                                <div class="col-md-2 col-6">
                                 <div class="shortcut">
                                       <img style="width:100px;height:100px" src='{{asset("Admin/assets/images/015.gif")}}'>
                                  <a href="{{url('Quote')}}"> <p>{{trans('admin.Quote')}} </p></a>
                                 </div>
                               </div>
                      @endcan
                       @can('جدول عرض سعر')
                                <div class="col-md-2 col-6">
                                 <div class="shortcut">
                                       <img style="width:100px;height:100px" src='{{asset("Admin/assets/images/016.gif")}}'>
                                  <a href="{{url('Quote_Sechdule')}}"> <p> {{trans('admin.Quote_Sechdule')}} </p></a>
                                 </div>
                               </div>
                    @endcan
                       @can('اضافه امر بيع')
                                <div class="col-md-2 col-6">
                                 <div class="shortcut">
                                       <img style="width:100px;height:100px" src='{{asset("Admin/assets/images/017.gif")}}'>
                                  <a href="{{url('SalesOrder')}}"> <p>  {{trans('admin.SalesOrder')}} </p></a>
                                 </div>
                               </div>
                     @endcan
                          @can('جدول امر البيع')
                                <div class="col-md-2 col-6">
                                 <div class="shortcut">
                                       <img style="width:100px;height:100px" src='{{asset("Admin/assets/images/018.gif")}}'>
                                  <a href="{{url('SalesOrderSechdule')}}" > <p>  {{trans('admin.SalesOrderSechdule')}} </p></a>
                                 </div>
                               </div>
                      @endcan
                    @can('الفواتير المعلقه مبيعات')
                                <div class="col-md-2 col-6">
                                 <div class="shortcut">
                                       <img style="width:100px;height:100px" src='{{asset("Admin/assets/images/021.gif")}}'>
                                  <a href="{{url('SalesHoldSechdule')}}"> <p> {{trans('admin.SalesHoldSechdule')}} </p></a>

                                 </div>
                               </div>
                    @endcan

                           @can('اضافه موظف')

                                <div class="col-md-2 col-6">
                                 <div class="shortcut">
                                       <img style="width:100px;height:100px" src='{{asset("Admin/assets/images/022.gif")}}'>
                                  <a href="{{url('AddEmp')}}"> <p>   {{trans('admin.Add_Emp')}} </p></a>
                                 </div>
                               </div>
                      @endcan
                       @can('صرف راتب')
                                <div class="col-md-2 col-6">
                                 <div class="shortcut">
                                       <img style="width:100px;height:100px" src='{{asset("Admin/assets/images/023.gif")}}'>
                                  <a href="{{url('AddSalary')}}"> <p>  {{trans('admin.AddSalary')}} </p></a>
                                 </div>
                               </div>
                      @endcan

                         @can('استعلام عن منتج')
                                <div class="col-md-2 col-6">
                                 <div class="shortcut">
                                       <img style="width:100px;height:100px" src='{{asset("Admin/assets/images/024.gif")}}'>
                                  <a href="{{url('Product_Info')}}"> <p>  {{trans('admin.Product_Info')}}</p></a>

                                 </div>
                               </div>
                      @endcan

                            @can('ميزان المراجعه')
                                 <div class="col-md-2 col-6">
                                 <div class="shortcut">
                                       <img style="width:100px;height:100px" src='{{asset("Admin/assets/images/025.gif")}}'>
                      <a href="{{url('Trial_Balance')}}"><p>{{trans('admin.Trial_Balance')}}</p></a>
                                 </div>
                                 </div>
                      @endcan
                         @can('كشف حساب خزنه، بنك')
                                    <div class="col-md-2 col-6">
                                 <div class="shortcut">
                                       <img style="width:100px;height:100px" src='{{asset("Admin/assets/images/026.gif")}}'>
                           <a href="{{url('Safe_Bank_Statement')}}"><p> {{trans('admin.Safe_Bank_Statement')}}</p></a>
                                 </div>
                                 </div>
                        @endcan

                        @can('كشف حساب موردين')
                                   <div class="col-md-2 col-6">
                                 <div class="shortcut">
                                       <img style="width:100px;height:100px" src='{{asset("Admin/assets/images/027.gif")}}'>
                      <a href="{{url('Vendor_Account_Statement')}}"><p>{{trans('admin.Vendor_Account_Statement')}}</p></a>
                                 </div>
                                 </div>
                      @endcan

                         @can('كشف حساب عملاء')
                                  <div class="col-md-2 col-6">
                                 <div class="shortcut">
                                       <img style="width:100px;height:100px" src='{{asset("Admin/assets/images/028.gif")}}'>
                   <a href="{{url('Customer_Account_Statement')}}"><p>{{trans('admin.Customer_Account_Statement')}}</p></a>
                                 </div>
                                 </div>
                      @endcan
                        @can('قائمه الدخل')
                                   <div class="col-md-2 col-6">
                                 <div class="shortcut">
                                       <img style="width:100px;height:100px" src='{{asset("Admin/assets/images/029.gif")}}'>
                    <a href="{{url('Incom_List')}}"><p>{{trans('admin.Incom_List')}} </p></a>
                                 </div>
                                 </div>
                      @endcan
                      @can('قائمه المركز المالي')
                                  <div class="col-md-2 col-6">
                                 <div class="shortcut">
                                       <img style="width:100px;height:100px" src='{{asset("Admin/assets/images/030.gif")}}'>
                <a href="{{url('Financial_Center')}}"><p>{{trans('admin.Financial_Center')}}</p></a>
                                 </div>
                                 </div>
                      @endcan
                         @can('طباعه الباركود')
                                   <div class="col-md-2 col-6">
                                 <div class="shortcut">
                                       <img style="width:100px;height:100px" src='{{asset("Admin/assets/images/034.gif")}}'>
                                 <a href="{{url('BarcodeـPrinting')}}"><p>{{trans('admin.BarcodeـPrinting')}}</p></a>
                                 </div>
                                 </div>
                      @endcan
                        @can('الدليل المحاسبي')
                               <div class="col-md-2 col-6">
                                 <div class="shortcut">
                                       <img style="width:100px;height:100px" src='{{asset("Admin/assets/images/001.gif")}}'>
                                  <a href="{{url('AccountingManual')}}"> <p>{{trans('admin.Accounting_Manual')}}</p></a>
                                 </div>
                               </div>
                       @endcan
                      @can('اضافه قيد يومي')
                                <div class="col-md-2 col-6">
                                 <div class="shortcut">
                                       <img style="width:100px;height:100px" src='{{asset("Admin/assets/images/002.gif")}}'>
                               <a href="{{url('Journalizing')}}">  <p>{{trans('admin.Journalizing')}}</p></a>
                                 </div>
                               </div>
                       @endcan


                            @can('نقاط البيع')
                            @if(auth()->guard('admin')->user()->emp != 0)
                                <div class="col-md-2 col-6">
                                 <div class="shortcut">
                                       <img style="width:100px;height:100px" src='{{asset("Admin/assets/images/1121-iota-internet-of-things-outline.gif")}}'>
                               <a href="{{url('POS')}}">  <p>{{trans('admin.POS')}}</p></a>
                                 </div>
                               </div>
                                        @endif
                         @endcan

                       @can('جرد المخازن')
                                         <div class="col-md-2 col-6">
                                 <div class="shortcut">
                                       <img style="width:100px;height:100px" src='{{asset("Admin/assets/images/219-arrow-8.gif")}}'>
                               <a href="{{url('StoresInventory')}}">  <p>{{trans('admin.StoresInventory')}}</p></a>
                                 </div>
                                 </div>
                      @endcan
                     @can('تحصيل مناديب')

                  <div class="col-md-2 col-6">
                                 <div class="shortcut">
                                       <img style="width:100px;height:100px" src='{{asset("Admin/assets/images/981-consultation-outline.gif")}}'>
                               <a href="{{url('Collection_Delegates')}}">  <p>{{trans('admin.Collection_Delegates')}}</p></a>
                                 </div>
                                 </div>
                      @endcan

                       @can('مبيعات مناديب')
                        <div class="col-md-2 col-6">
                             <div class="shortcut">
                                       <img style="width:100px;height:100px" src='{{asset("Admin/assets/images/153-bar-chart-growth-outline.gif")}}'>
                               <a href="{{url('Sales_Delegates')}}">  <p>{{trans('admin.Sales_Delegates')}}</p></a>
                                 </div>
                                 </div>
                       @endcan


                           </div>
                   @endif
                   @endif
                   @endif


                @else

                   <style>
    .panel.panel-locked:not(.panel-fullscreen) .panel-hdr h2:before{

        display: none !important;
    }
    .custom-control{
      display: none !important;
    }

    .GG{
            background: black;
    padding: 10px;
    color: white;
    margin: 5px;
    border-radius: 10%;
    }

</style>

                 <script>
            /**
             *	This script should be placed right after the body tag for fast execution
             *	Note: the script is written in pure javascript and does not depend on thirdparty library
             **/
            'use strict';

            var classHolder = document.getElementsByTagName("BODY")[0],
                /**
                 * Load from localstorage
                 **/
                themeSettings = (localStorage.getItem('themeSettings')) ? JSON.parse(localStorage.getItem('themeSettings')) :
                {},
                themeURL = themeSettings.themeURL || '',
                themeOptions = themeSettings.themeOptions || '';
            /**
             * Load theme options
             **/
            if (themeSettings.themeOptions)
            {
                classHolder.className = themeSettings.themeOptions;
                console.log("%c✔ Theme settings loaded", "color: #148f32");
            }
            else
            {
                console.log("%c✔ Heads up! Theme settings is empty or does not exist, loading default settings...", "color: #ed1c24");
            }
            if (themeSettings.themeURL && !document.getElementById('mytheme'))
            {
                var cssfile = document.createElement('link');
                cssfile.id = 'mytheme';
                cssfile.rel = 'stylesheet';
                cssfile.href = themeURL;
                document.getElementsByTagName('head')[0].appendChild(cssfile);

            }
            else if (themeSettings.themeURL && document.getElementById('mytheme'))
            {
                document.getElementById('mytheme').href = themeSettings.themeURL;
            }
            /**
             * Save to localstorage
             **/
            var saveSettings = function()
            {
                themeSettings.themeOptions = String(classHolder.className).split(/[^\w-]+/).filter(function(item)
                {
                    return /^(nav|header|footer|mod|display)-/i.test(item);
                }).join(' ');
                if (document.getElementById('mytheme'))
                {
                    themeSettings.themeURL = document.getElementById('mytheme').getAttribute("href");
                };
                localStorage.setItem('themeSettings', JSON.stringify(themeSettings));
            }
            /**
             * Reset settings
             **/
            var resetSettings = function()
            {
                localStorage.setItem("themeSettings", "");
            }

        </script>

        <div class="page-wrapper">
            <div class="page-inner">

                <div class="page-content-wrapper">

                    <main id="js-page-content" role="main" class="page-content">



                <div class="row text-center">
              <div class="col-md-4"></div>
              <div class="col-md-4 text-center GG"><h2 class="text-center">{{trans('admin.Sales')}}</h2> </div>
              <div class="col-md-4"></div>
                        </div>


                        <!-- Sales -->
                        <div class="row">

                            <!-- Sales Monthly -->
                            <div class="col-lg-12">
                                <div id="panel-1" class="panel">
                                    <div class="panel-hdr">
                                        <h2 class="text-center">
                                               {{trans('admin.MonthlySales')}}
                                        </h2>
                                    </div>
                                    <div class="panel-container show">
                                        <div class="panel-content bg-subtlelight-fade">
                                            <div id="js-checkbox-toggles" class="d-flex mb-3" >
                                                <div class="custom-control custom-switch mr-2" >
                                                    <input type="checkbox" class="custom-control-input" name="gra-0" id="gra-0" checked="checked">
                                                    <label class="custom-control-label" for="gra-0" style="padding:inherit">{{trans('admin.pyramidal')}}</label>
                                                </div>
                                                <div class="custom-control custom-switch mr-2" style="display:none !important">
                                                    <input type="checkbox" class="custom-control-input" name="gra-1" id="gra-1" checked="checked">
                                                    <label class="custom-control-label" for="gra-1">Actual Profit</label>
                                                </div>
                                                <div class="custom-control custom-switch mr-2">
                                                    <input type="checkbox" class="custom-control-input" name="gra-2" id="gra-2" checked="checked">
                                                    <label class="custom-control-label" for="gra-2" style="padding:inherit">{{trans('admin.graph')}}</label>
                                                </div>
                                            </div>
                                            <div id="flot-toggles" class="w-100 mt-4" style="height: 300px"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                               <!-- Sales Weekly -->

                            <div class="col-lg-6">
                                <div id="panel-3" class="panel panel-locked" data-panel-sortable data-panel-collapsed data-panel-close>
                                    <div class="panel-hdr">
                                        <h2>
                                               {{trans('admin.WeeklySales')}}
                                        </h2>
                                    </div>
                                    <div class="panel-container show">
                                        <div class="panel-content poisition-relative">
                                            <div class="pb-5 pt-3" style="display: none !important">
                                                <div class="row">
                                                    <div class="col-6 col-xl-3 d-sm-flex align-items-center">
                                                        <div class="p-2 mr-3 bg-info-200 rounded">
                                                            <span class="peity-bar" data-peity="{&quot;fill&quot;: [&quot;#fff&quot;], &quot;width&quot;: 27, &quot;height&quot;: 27 }">3,4,5,8,2</span>
                                                        </div>
                                                        <div>
                                                            <label class="fs-sm mb-0">Bounce Rate</label>
                                                            <h4 class="font-weight-bold mb-0">37.56%</h4>
                                                        </div>
                                                    </div>
                                                    <div class="col-6 col-xl-3 d-sm-flex align-items-center">
                                                        <div class="p-2 mr-3 bg-info-300 rounded">
                                                            <span class="peity-bar" data-peity="{&quot;fill&quot;: [&quot;#fff&quot;], &quot;width&quot;: 27, &quot;height&quot;: 27 }">5,3,1,7,9</span>
                                                        </div>
                                                        <div>
                                                            <label class="fs-sm mb-0">Sessions</label>
                                                            <h4 class="font-weight-bold mb-0">759</h4>
                                                        </div>
                                                    </div>
                                                    <div class="col-6 col-xl-3 d-sm-flex align-items-center">
                                                        <div class="p-2 mr-3 bg-success-300 rounded">
                                                            <span class="peity-bar" data-peity="{&quot;fill&quot;: [&quot;#fff&quot;], &quot;width&quot;: 27, &quot;height&quot;: 27 }">3,4,3,5,5</span>
                                                        </div>
                                                        <div>
                                                            <label class="fs-sm mb-0">New Sessions</label>
                                                            <h4 class="font-weight-bold mb-0">12.17%</h4>
                                                        </div>
                                                    </div>
                                                    <div class="col-6 col-xl-3 d-sm-flex align-items-center">
                                                        <div class="p-2 mr-3 bg-success-500 rounded">
                                                            <span class="peity-bar" data-peity="{&quot;fill&quot;: [&quot;#fff&quot;], &quot;width&quot;: 27, &quot;height&quot;: 27 }">6,4,7,5,6</span>
                                                        </div>
                                                        <div>
                                                            <label class="fs-sm mb-0">Clickthrough</label>
                                                            <h4 class="font-weight-bold mb-0">19.77%</h4>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div id="flotVisit" style="width:100%; height:208px;"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>



                              <!-- Sales Yearly -->
                             <div class="col-lg-6">
                                <div id="panel-2" class="panel panel-locked" data-panel-sortable data-panel-collapsed data-panel-close>
                                    <div class="panel-hdr">
                                        <h2>
                                           {{trans('admin.YearlySales')}}
                                        </h2>
                                    </div>
                                    <div class="panel-container show">
                                        <div class="panel-content poisition-relative">
                                            <div style="display: none !important" class="p-1 position-absolute pos-right pos-top mt-3 mr-3 z-index-cloud d-flex align-items-center justify-content-center">
                                                <div class="border-faded border-top-0 border-left-0 border-bottom-0 py-2 pr-4 mr-3 hidden-sm-down">
                                                    <div class="text-right fw-500 l-h-n d-flex flex-column">
                                                        <div class="h3 m-0 d-flex align-items-center justify-content-end">
                                                            <div class='icon-stack mr-2'>
                                                                <i class="base base-7 icon-stack-3x opacity-100 color-success-600"></i>
                                                                <i class="base base-7 icon-stack-2x opacity-100 color-success-500"></i>
                                                                <i class="fal fa-arrow-up icon-stack-1x opacity-100 color-white"></i>
                                                            </div>
                                                            $44.34 / GE
                                                        </div>
                                                        <span class="m-0 fs-xs text-muted">Increased Profit as per redux margins and estimates</span>
                                                    </div>
                                                </div>
                                                <div class="js-easy-pie-chart color-info-400 position-relative d-inline-flex align-items-center justify-content-center" data-percent="35" data-piesize="95" data-linewidth="10" data-scalelength="5">
                                                    <div class="js-easy-pie-chart color-success-400 position-relative position-absolute pos-left pos-right pos-top pos-bottom d-flex align-items-center justify-content-center" data-percent="65" data-piesize="60" data-linewidth="5" data-scalelength="1" data-scalecolor="#fff">
                                                        <div class="position-absolute pos-top pos-left pos-right pos-bottom d-flex align-items-center justify-content-center fw-500 fs-xl text-dark">78%</div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div id="flot-area" style="width:100%; height:300px;"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>


                                 <div class="col-xl-6">
                                                    <!-- Sales Quarters -->
                                <div id="panel-1" class="panel">
                                    <div class="panel-hdr">
                                        <h2>
                                          {{trans('admin.SalesQuarters')}}
                                        </h2>
                                    </div>
                                    <div class="panel-container show">
                                        <div class="panel-content">

                                            <div id="multiLineLabels" class="ct-chart" style="width:100%; height:300px;"></div>
                                        </div>
                                    </div>
                                </div>


                            </div>


                                   <div class="col-xl-6">
                  <!-- Sales Half -->
                                <div id="panel-7" class="panel">
                                    <div class="panel-hdr">
                                        <h2>
                                          {{trans('admin.SalesHalfs')}}
                                        </h2>
                                    </div>
                                    <div class="panel-container show">
                                        <div class="panel-content">

                                            <div id="extreamResponsive" class="ct-chart" style="width:100%; height:300px;"></div>
                                        </div>
                                    </div>
                                </div>
                                       </div>

                        </div>




                         <div class="row text-center">
              <div class="col-md-4"></div>
              <div class="col-md-4 text-centern GG"><h2 class="text-center">{{trans('admin.Purchases')}}</h2> </div>
              <div class="col-md-4"></div>
                        </div>
                               <!-- Purchases -->
                        <div class="row">

                            <!-- Purchases Monthly -->
                            <div class="col-lg-12">
                                <div id="panel-1" class="panel">
                                    <div class="panel-hdr">
                                        <h2 class="text-center">
                                               {{trans('admin.MonthlyPurchases')}}
                                        </h2>
                                    </div>
                                    <div class="panel-container show">
                                        <div class="panel-content bg-subtlelight-fade">
                                            <div id="js-checkbox-toggles-Purchases" class="d-flex mb-3" >
                                                <div class="custom-control custom-switch mr-2" >
                                                    <input type="checkbox" class="custom-control-input" name="gra-0" id="gra-0" checked="checked">
                                                    <label class="custom-control-label" for="gra-0" style="padding:inherit">{{trans('admin.pyramidal')}}</label>
                                                </div>
                                                <div class="custom-control custom-switch mr-2" style="display:none !important">
                                                    <input type="checkbox" class="custom-control-input" name="gra-1" id="gra-1" checked="checked">
                                                    <label class="custom-control-label" for="gra-1">Actual Profit</label>
                                                </div>
                                                <div class="custom-control custom-switch mr-2">
                                                    <input type="checkbox" class="custom-control-input" name="gra-2" id="gra-2" checked="checked">
                                                    <label class="custom-control-label" for="gra-2" style="padding:inherit">{{trans('admin.graph')}}</label>
                                                </div>
                                            </div>
                                            <div id="flot-toggles-Purchases" class="w-100 mt-4" style="height: 300px"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                               <!-- Purchases Weekly -->

                            <div class="col-lg-6">
                                <div id="panel-3" class="panel panel-locked" data-panel-sortable data-panel-collapsed data-panel-close>
                                    <div class="panel-hdr">
                                        <h2>
                                               {{trans('admin.WeeklyPurchases')}}
                                        </h2>
                                    </div>
                                    <div class="panel-container show">
                                        <div class="panel-content poisition-relative">
                                            <div class="pb-5 pt-3" style="display: none !important">
                                                <div class="row">
                                                    <div class="col-6 col-xl-3 d-sm-flex align-items-center">
                                                        <div class="p-2 mr-3 bg-info-200 rounded">
                                                            <span class="peity-bar" data-peity="{&quot;fill&quot;: [&quot;#fff&quot;], &quot;width&quot;: 27, &quot;height&quot;: 27 }">3,4,5,8,2</span>
                                                        </div>
                                                        <div>
                                                            <label class="fs-sm mb-0">Bounce Rate</label>
                                                            <h4 class="font-weight-bold mb-0">37.56%</h4>
                                                        </div>
                                                    </div>
                                                    <div class="col-6 col-xl-3 d-sm-flex align-items-center">
                                                        <div class="p-2 mr-3 bg-info-300 rounded">
                                                            <span class="peity-bar" data-peity="{&quot;fill&quot;: [&quot;#fff&quot;], &quot;width&quot;: 27, &quot;height&quot;: 27 }">5,3,1,7,9</span>
                                                        </div>
                                                        <div>
                                                            <label class="fs-sm mb-0">Sessions</label>
                                                            <h4 class="font-weight-bold mb-0">759</h4>
                                                        </div>
                                                    </div>
                                                    <div class="col-6 col-xl-3 d-sm-flex align-items-center">
                                                        <div class="p-2 mr-3 bg-success-300 rounded">
                                                            <span class="peity-bar" data-peity="{&quot;fill&quot;: [&quot;#fff&quot;], &quot;width&quot;: 27, &quot;height&quot;: 27 }">3,4,3,5,5</span>
                                                        </div>
                                                        <div>
                                                            <label class="fs-sm mb-0">New Sessions</label>
                                                            <h4 class="font-weight-bold mb-0">12.17%</h4>
                                                        </div>
                                                    </div>
                                                    <div class="col-6 col-xl-3 d-sm-flex align-items-center">
                                                        <div class="p-2 mr-3 bg-success-500 rounded">
                                                            <span class="peity-bar" data-peity="{&quot;fill&quot;: [&quot;#fff&quot;], &quot;width&quot;: 27, &quot;height&quot;: 27 }">6,4,7,5,6</span>
                                                        </div>
                                                        <div>
                                                            <label class="fs-sm mb-0">Clickthrough</label>
                                                            <h4 class="font-weight-bold mb-0">19.77%</h4>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div id="flotVisit-Purchases" style="width:100%; height:208px;"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>



                              <!-- Purchases Yearly -->
                             <div class="col-lg-6">
                                <div id="panel-2" class="panel panel-locked" data-panel-sortable data-panel-collapsed data-panel-close>
                                    <div class="panel-hdr">
                                        <h2>
                                           {{trans('admin.YearlyPurchases')}}
                                        </h2>
                                    </div>
                                    <div class="panel-container show">
                                        <div class="panel-content poisition-relative">
                                            <div style="display: none !important" class="p-1 position-absolute pos-right pos-top mt-3 mr-3 z-index-cloud d-flex align-items-center justify-content-center">
                                                <div class="border-faded border-top-0 border-left-0 border-bottom-0 py-2 pr-4 mr-3 hidden-sm-down">
                                                    <div class="text-right fw-500 l-h-n d-flex flex-column">
                                                        <div class="h3 m-0 d-flex align-items-center justify-content-end">
                                                            <div class='icon-stack mr-2'>
                                                                <i class="base base-7 icon-stack-3x opacity-100 color-success-600"></i>
                                                                <i class="base base-7 icon-stack-2x opacity-100 color-success-500"></i>
                                                                <i class="fal fa-arrow-up icon-stack-1x opacity-100 color-white"></i>
                                                            </div>
                                                            $44.34 / GE
                                                        </div>
                                                        <span class="m-0 fs-xs text-muted">Increased Profit as per redux margins and estimates</span>
                                                    </div>
                                                </div>
                                                <div class="js-easy-pie-chart color-info-400 position-relative d-inline-flex align-items-center justify-content-center" data-percent="35" data-piesize="95" data-linewidth="10" data-scalelength="5">
                                                    <div class="js-easy-pie-chart color-success-400 position-relative position-absolute pos-left pos-right pos-top pos-bottom d-flex align-items-center justify-content-center" data-percent="65" data-piesize="60" data-linewidth="5" data-scalelength="1" data-scalecolor="#fff">
                                                        <div class="position-absolute pos-top pos-left pos-right pos-bottom d-flex align-items-center justify-content-center fw-500 fs-xl text-dark">78%</div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div id="flot-area-Purchases" style="width:100%; height:300px;"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>


                                 <div class="col-xl-6">
                                                    <!-- Purchases Quarters -->
                                <div id="panel-1" class="panel">
                                    <div class="panel-hdr">
                                        <h2>
                                          {{trans('admin.PurchasesQuarters')}}
                                        </h2>
                                    </div>
                                    <div class="panel-container show">
                                        <div class="panel-content">

                                            <div id="multiLineLabels-Purchases" class="ct-chart" style="width:100%; height:300px;"></div>
                                        </div>
                                    </div>
                                </div>


                            </div>


                                   <div class="col-xl-6">
                  <!-- Purchases Half -->
                                <div id="panel-7" class="panel">
                                    <div class="panel-hdr">
                                        <h2>
                                          {{trans('admin.PurchasesHalfs')}}
                                        </h2>
                                    </div>
                                    <div class="panel-container show">
                                        <div class="panel-content">

                                            <div id="extreamResponsive-Purchases" class="ct-chart" style="width:100%; height:300px;"></div>
                                        </div>
                                    </div>
                                </div>
                                       </div>

                        </div>








                                                           <div class="row text-center">
              <div class="col-md-4"></div>
              <div class="col-md-4 text-centern GG"><h2 class="text-center">{{trans('admin.Return_Sales')}}</h2> </div>
              <div class="col-md-4"></div>
                        </div>

                                             <!-- ReturnSales -->

                                <div class="row">


                                       <div class="col-xl-12">
                                                         <div id="panel-13" class="panel">
                                    <div class="panel-hdr">
                                        <h2>
                                       {{trans('admin.ReturnSalesMonthly')}}
                                        </h2>

                                    </div>
                                    <div class="panel-container show">
                                        <div class="panel-content">
                                            <div id="flot-sales" class="w-100" style="height: 350px"></div>
                                        </div>
                                    </div>
                                </div>
                                    </div>

                                       <div class="col-xl-6">
                                             <div id="panel-2" class="panel">
                                    <div class="panel-hdr">
                                        <h2>
                                            {{trans('admin.ReturnSalesWeekly')}}
                                        </h2>

                                    </div>
                                    <div class="panel-container show">
                                        <div class="panel-content">

                                            <div id="flot-bar" style="width:100%; height:300px;"></div>
                                        </div>
                                    </div>
                                </div>
                                    </div>


                                       <div class="col-xl-6">
                                                    <div id="panel-4" class="panel">
                                    <div class="panel-hdr">
                                        <h2>
                                             {{trans('admin.ReturnSalesYearly')}}
                                        </h2>

                                    </div>
                                    <div class="panel-container show">
                                        <div class="panel-content">

                                            <div id="flot-line-alt" style="width:100%; height:300px;"></div>
                                        </div>
                                    </div>
                                </div>
                                    </div>

                                 <div class="col-xl-6">
                                                    <!-- ReturnSales Quarters -->
                                <div id="panel-1" class="panel">
                                    <div class="panel-hdr">
                                        <h2>
                                          {{trans('admin.ReturnSalesQuarters')}}
                                        </h2>
                                    </div>
                                    <div class="panel-container show">
                                        <div class="panel-content">

                                            <div id="multiLineLabels-ReturnSales" class="ct-chart" style="width:100%; height:300px;"></div>
                                        </div>
                                    </div>
                                </div>


                            </div>


                                   <div class="col-xl-6">
                  <!-- ReturnSales Half -->
                                <div id="panel-7" class="panel">
                                    <div class="panel-hdr">
                                        <h2>
                                          {{trans('admin.ReturnSalesHalfs')}}
                                        </h2>
                                    </div>
                                    <div class="panel-container show">
                                        <div class="panel-content">

                                            <div id="extreamResponsive-ReturnSales" class="ct-chart" style="width:100%; height:300px;"></div>
                                        </div>
                                    </div>
                                </div>
                                       </div>

                        </div>




                                          <div class="row text-center">
              <div class="col-md-4"></div>
              <div class="col-md-4 text-centern GG"><h2 class="text-center">{{trans('admin.Return_Purchases')}}</h2> </div>
              <div class="col-md-4"></div>
                        </div>


                                                              <!-- ReturnPurchases -->

                                        <div class="row">


                                       <div class="col-xl-12">
                                                         <div id="panel-13" class="panel">
                                    <div class="panel-hdr">
                                        <h2>
                                       {{trans('admin.ReturnPurchMonthly')}}
                                        </h2>

                                    </div>
                                    <div class="panel-container show">
                                        <div class="panel-content">
                                            <div id="flot-sales-purch" class="w-100" style="height: 350px"></div>
                                        </div>
                                    </div>
                                </div>
                                    </div>

                                       <div class="col-xl-6">
                                             <div id="panel-2" class="panel">
                                    <div class="panel-hdr">
                                        <h2>
                                            {{trans('admin.ReturnPurchWeekly')}}
                                        </h2>

                                    </div>
                                    <div class="panel-container show">
                                        <div class="panel-content">

                                            <div id="flot-bar-purch" style="width:100%; height:300px;"></div>
                                        </div>
                                    </div>
                                </div>
                                    </div>


                                       <div class="col-xl-6">
                                                    <div id="panel-4" class="panel">
                                    <div class="panel-hdr">
                                        <h2>
                                             {{trans('admin.ReturnPurchYearly')}}
                                        </h2>

                                    </div>
                                    <div class="panel-container show">
                                        <div class="panel-content">

                                            <div id="flot-line-alt-purch" style="width:100%; height:300px;"></div>
                                        </div>
                                    </div>
                                </div>
                                    </div>

                                 <div class="col-xl-6">
                                                    <!-- ReturnSales Quarters -->
                                <div id="panel-1" class="panel">
                                    <div class="panel-hdr">
                                        <h2>
                                          {{trans('admin.ReturnPurchQuarters')}}
                                        </h2>
                                    </div>
                                    <div class="panel-container show">
                                        <div class="panel-content">

                                            <div id="multiLineLabels-Purch" class="ct-chart" style="width:100%; height:300px;"></div>
                                        </div>
                                    </div>
                                </div>


                            </div>


                                   <div class="col-xl-6">
                  <!-- ReturnSales Half -->
                                <div id="panel-7" class="panel">
                                    <div class="panel-hdr">
                                        <h2>
                                          {{trans('admin.ReturnPurchHalfs')}}
                                        </h2>
                                    </div>
                                    <div class="panel-container show">
                                        <div class="panel-content">

                                            <div id="extreamResponsive-Purch" class="ct-chart" style="width:100%; height:300px;"></div>
                                        </div>
                                    </div>
                                </div>
                                       </div>

                        </div>






                                                     <div class="row text-center">
              <div class="col-md-4"></div>
              <div class="col-md-4 text-centern GG"><h2 class="text-center">VS</h2> </div>
              <div class="col-md-4"></div>
                        </div>


                        <div class="row">

                          <div class="col-xl-12">
                                           <!-- Overlap Bar (Mobile) -->
                                <div id="panel-5" class="panel">
                                    <div class="panel-hdr">
                                        <h2>
                                          {{trans('admin.Sales')}} VS  {{trans('admin.Purchases')}}
                                        </h2>
                                    </div>
                                    <div class="panel-container show">
                                        <div class="panel-content">

                                            <div id="overlapBarMobile" class="ct-chart" style="width:100%; height:300px;"></div>
                                        </div>
                                    </div>
                                </div>

                            </div>


                        <div class="col-xl-6">
                                        <div id="panel-4" class="panel">
                                            <div class="panel-hdr">
                                                <h2>
                                                          {{trans('admin.Sales')}} VS  {{trans('admin.Return_Sales')}}
                                                </h2>

                                            </div>
                                            <div class="panel-container show">
                                                <div class="panel-content">

                                                    <div id="splilneLine" style="width:100%; height:300px;"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                      <div class="col-xl-6">
                                        <div id="panel-4" class="panel">
                                            <div class="panel-hdr">
                                                <h2>
                                                          {{trans('admin.Purchases')}} VS  {{trans('admin.Return_Purchases')}}
                                                </h2>

                                            </div>
                                            <div class="panel-container show">
                                                <div class="panel-content">

                                                    <div id="splilneLinePurch" style="width:100%; height:300px;"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                         <div class="col-xl-12">
                                        <div id="panel-8" class="panel">
                                            <div class="panel-hdr">
                                                <h2>
                                                  {{trans('admin.Return_Sales')}} VS  {{trans('admin.Return_Purchases')}}
                                                </h2>

                                            </div>
                                            <div class="panel-container show">
                                                <div class="panel-content">

                                                    <div id="barChart">
                                                        <canvas style="width:100%; height:300px;"></canvas>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>



                        </div>



                                                                        <div class="row text-center">
              <div class="col-md-4"></div>
              <div class="col-md-4 text-centern GG"><h2 class="text-center">{{trans('admin.GBSales')}}</h2> </div>
              <div class="col-md-4"></div>
                        </div>



                        <!-- Group Sales -->
                          <div class="row">

                             <div class="col-xl-6">

      <!-- Pie Chart -->
                                <div id="panel-4" class="panel">
                                    <div class="panel-hdr">
                                        <h2>
                                        {{trans('admin.GroupSales')}}
                                        </h2>

                                    </div>
                                    <div class="panel-container show">
                                        <div class="panel-content">
                                                <div id="pieChart">
                                                        <canvas style="width:100%; height:300px;"></canvas>
                                                    </div>
                                        </div>
                                    </div>
                                </div>

                              </div>


                               <div class="col-xl-6">

      <!-- Pie Chart -->
                                <div id="panel-4" class="panel">
                                    <div class="panel-hdr">
                                        <h2>
                                        {{trans('admin.BrandSales')}}
                                        </h2>

                                    </div>
                                    <div class="panel-container show">
                                        <div class="panel-content">
                                                <div id="pieChartB">
                                                        <canvas style="width:100%; height:300px;"></canvas>
                                                    </div>
                                        </div>
                                    </div>
                                </div>

                              </div>

                        </div>



                                                                                     <div class="row text-center">
              <div class="col-md-4"></div>
              <div class="col-md-4 text-centern GG"><h2 class="text-center">{{trans('admin.Expenses')}}</h2> </div>
              <div class="col-md-4"></div>
                        </div>

                        <!-- Expenses -->
                        <div class="row">

                            <div class="col-xl-12">


       <div id="panel-6" class="panel">
                                    <div class="panel-hdr">
                                        <h2>
                                       {{trans('admin.ExpensesMonthly')}}
                                        </h2>
                                    </div>
                                    <div class="panel-container show">
                                        <div class="panel-content">
                                            <div id="lineChartArea" class="ct-chart" style="width:100%; height:300px;"></div>
                                        </div>
                                    </div>
                                </div>


  </div>


                                      <div class="col-xl-6">
                                              <div id="panel-10" class="panel">
                                    <div class="panel-hdr">
                                        <h2>
                                   {{trans('admin.ExpensesWeekly')}}
                                        </h2>

                                    </div>
                                    <div class="panel-container show">
                                        <div class="panel-content">
                                            <div id="usingEvents" class="ct-chart" style="width:100%; height:300px;"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>


                                                <div class="col-xl-6">
                                              <div id="panel-10" class="panel">
                                    <div class="panel-hdr">
                                        <h2>
                                   {{trans('admin.ExpensesYearly')}}
                                        </h2>

                                    </div>
                                    <div class="panel-container show">
                                        <div class="panel-content">
                                            <div id="usingEventsYearly" class="ct-chart" style="width:100%; height:300px;"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>




                                        <div class="col-xl-6">
                                                    <!-- Expenses Quarters -->
                                <div id="panel-1" class="panel">
                                    <div class="panel-hdr">
                                        <h2>
                                          {{trans('admin.ExpensesQuarters')}}
                                        </h2>
                                    </div>
                                    <div class="panel-container show">
                                        <div class="panel-content">

                                            <div id="multiLineLabels-Expenses" class="ct-chart" style="width:100%; height:300px;"></div>
                                        </div>
                                    </div>
                                </div>


                            </div>


                                   <div class="col-xl-6">
                  <!-- Expenses Half -->
                                <div id="panel-7" class="panel">
                                    <div class="panel-hdr">
                                        <h2>
                                          {{trans('admin.ExpensesHalfs')}}
                                        </h2>
                                    </div>
                                    <div class="panel-container show">
                                        <div class="panel-content">

                                            <div id="extreamResponsive-Expenses" class="ct-chart" style="width:100%; height:300px;"></div>
                                        </div>
                                    </div>
                                </div>
                                       </div>


                        </div>





                                  <div class="row text-center">
              <div class="col-md-4"></div>
              <div class="col-md-4 text-centern GG"><h2 class="text-center">{{trans('admin.Vouchers')}}</h2> </div>
              <div class="col-md-4"></div>
                        </div>

                        <!-- Vouchers -->
                          <div class="row">



                               <div class="col-xl-12">
                                         <!-- Line Interpolation -->
                                <div id="panel-8" class="panel">
                                    <div class="panel-hdr">
                                        <h2>
                                      {{trans('admin.VouchersMonthly')}}
                                        </h2>

                                    </div>
                                    <div class="panel-container show">
                                        <div class="panel-content">
                                            <span style="color: #886ab5">{{trans('admin.Payment_Voucher')}}</span>
                                            <span style="color: #fd3995">{{trans('admin.Recipt_Voucher')}}</span>
                                            <div id="lineInterpolation" class="ct-chart" style="width:100%; height:300px;"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>


                                      <div class="col-xl-6">

          <!-- Distributed Series -->
                                <div id="panel-9" class="panel">
                                    <div class="panel-hdr">
                                        <h2>
                                    {{trans('admin.PaymentWeekly')}}
                                        </h2>

                                    </div>
                                    <div class="panel-container show">
                                        <div class="panel-content">
                                            <div id="distributedSeries" class="ct-chart" style="width:100%; height:300px;"></div>
                                        </div>
                                    </div>
                                </div>

                              </div>


                                                <div class="col-xl-6">

          <!-- Distributed Series -->
                                <div id="panel-9" class="panel">
                                    <div class="panel-hdr">
                                        <h2>
                                           {{trans('admin.ReciptWeekly')}}
                                        </h2>

                                    </div>
                                    <div class="panel-container show">
                                        <div class="panel-content">
                                            <div id="distributedSeriesRecipt" class="ct-chart" style="width:100%; height:300px;"></div>
                                        </div>
                                    </div>
                                </div>

                              </div>


                        </div>





                                  <div class="row text-center">
              <div class="col-md-4"></div>
              <div class="col-md-4 text-centern GG"><h2 class="text-center">{{trans('admin.Checks')}}</h2> </div>
              <div class="col-md-4"></div>
                        </div>

                        <!-- Checks -->
                        <div class="row">

                                 <div class="col-xl-12">
                                        <div id="panel-2" class="panel">
                                            <div class="panel-hdr">
                                                <h2>
                                                {{trans('admin.ChecksMonthly')}}
                                                </h2>

                                            </div>
                                            <div class="panel-container show">
                                                <div class="panel-content">

                                                    <div id="linleRegions" style="width:100%; height:300px;"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>


                                                <div class="col-xl-6">

          <!-- Distributed Series -->
                                <div id="panel-9" class="panel">
                                    <div class="panel-hdr">
                                        <h2>
                                         {{trans('admin.IncomChecksWeekly')}}
                                        </h2>

                                    </div>
                                    <div class="panel-container show">
                                        <div class="panel-content">

                                            <div id="distributedSeriesIncom" class="ct-chart" style="width:100%; height:300px;"></div>
                                        </div>
                                    </div>
                                </div>

                              </div>


                                                <div class="col-xl-6">

          <!-- Distributed Series -->
                                <div id="panel-9" class="panel">
                                    <div class="panel-hdr">
                                        <h2>
                                               {{trans('admin.ExportChecksWeekly')}}
                                        </h2>

                                    </div>
                                    <div class="panel-container show">
                                        <div class="panel-content">
                                            <div id="distributedSeriesExport" class="ct-chart" style="width:100%; height:300px;"></div>
                                        </div>
                                    </div>
                                </div>

                              </div>

                        </div>



                                               <div class="row text-center">
              <div class="col-md-4"></div>
              <div class="col-md-4 text-centern GG"><h2 class="text-center">{{trans('admin.Loose_Profit')}}</h2> </div>
              <div class="col-md-4"></div>
                        </div>

                                 <!-- Profit -->

                        <div class="row">

                                  <div class="col-xl-12">


          <div id="panel-3" class="panel">
                                        <div id="panel-10" class="panel">
                                            <div class="panel-hdr">
                                                <h2>
                                                 {{trans('admin.Loose_Profit')}}
                                                </h2>

                                            </div>
                                            <div class="panel-container show">
                                                <div class="panel-content">

                                                    <div id="barlineCombine">
                                                        <canvas style="width:100%; height:300px;"></canvas>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>


  </div>


                        </div>







                    </main>

                </div>
            </div>
        </div>




<input type="hidden" id="toggleOneName1" value="{{trans('admin.pyramidal')}}">
<input type="hidden" id="toggleOneName2" value="{{trans('admin.graph')}}">

<input type="hidden" id="SalesJan" value="{{$SalesJan}}">
<input type="hidden" id="SalesFeb" value="{{$SalesFeb}}">
<input type="hidden" id="SalesMar" value="{{$SalesMar}}">
<input type="hidden" id="SalesApr" value="{{$SalesApr}}">
<input type="hidden" id="SalesMay" value="{{$SalesMay}}">
<input type="hidden" id="SalesJun" value="{{$SalesJun}}">
<input type="hidden" id="SalesJul" value="{{$SalesJul}}">
<input type="hidden" id="SalesAug" value="{{$SalesAug}}">
<input type="hidden" id="SalesSep" value="{{$SalesSep}}">
<input type="hidden" id="SalesOct" value="{{$SalesOct}}">
<input type="hidden" id="SalesNov" value="{{$SalesNov}}">
<input type="hidden" id="SalesDec" value="{{$SalesDec}}">

<input type="hidden" id="SalesSat" value="{{$SalesSat}}">
<input type="hidden" id="SalesSun" value="{{$SalesSun}}">
<input type="hidden" id="SalesMon" value="{{$SalesMon}}">
<input type="hidden" id="SalesTue" value="{{$SalesTue}}">
<input type="hidden" id="SalesWed" value="{{$SalesWed}}">
<input type="hidden" id="SalesThr" value="{{$SalesThr}}">
<input type="hidden" id="SalesFri" value="{{$SalesFri}}">

<input type="hidden" id="SalesPrevY1" value="{{$SalesPrevY1}}">
<input type="hidden" id="SalesPrevY2" value="{{$SalesPrevY2}}">
<input type="hidden" id="SalesPrevY3" value="{{$SalesPrevY3}}">
<input type="hidden" id="SalesPrevY4" value="{{$SalesPrevY4}}">
<input type="hidden" id="SalesPrevYC" value="{{$SalesPrevYC}}">

<input type="hidden" id="YearName1" value='{{date("Y",strtotime("-1 year"))}}'>
<input type="hidden" id="YearName2" value='{{date("Y",strtotime("-2 year"))}}'>
<input type="hidden" id="YearName3" value='{{date("Y",strtotime("-3 year"))}}'>
<input type="hidden" id="YearName4" value='{{date("Y",strtotime("-4 year"))}}'>
<input type="hidden" id="YearNameC" value='{{date("Y")}}'>




<input type="hidden" id="PurchJan" value="{{$PurchJan}}">
<input type="hidden" id="PurchFeb" value="{{$PurchFeb}}">
<input type="hidden" id="PurchMar" value="{{$PurchMar}}">
<input type="hidden" id="PurchApr" value="{{$PurchApr}}">
<input type="hidden" id="PurchMay" value="{{$PurchMay}}">
<input type="hidden" id="PurchJun" value="{{$PurchJun}}">
<input type="hidden" id="PurchJul" value="{{$PurchJul}}">
<input type="hidden" id="PurchAug" value="{{$PurchAug}}">
<input type="hidden" id="PurchSep" value="{{$PurchSep}}">
<input type="hidden" id="PurchOct" value="{{$PurchOct}}">
<input type="hidden" id="PurchNov" value="{{$PurchNov}}">
<input type="hidden" id="PurchDec" value="{{$PurchDec}}">

<input type="hidden" id="PurchSat" value="{{$PurchSat}}">
<input type="hidden" id="PurchSun" value="{{$PurchSun}}">
<input type="hidden" id="PurchMon" value="{{$PurchMon}}">
<input type="hidden" id="PurchTue" value="{{$PurchTue}}">
<input type="hidden" id="PurchWed" value="{{$PurchWed}}">
<input type="hidden" id="PurchThr" value="{{$PurchThr}}">
<input type="hidden" id="PurchFri" value="{{$PurchFri}}">

<input type="hidden" id="PurchPrevY1" value="{{$PurchPrevY1}}">
<input type="hidden" id="PurchPrevY2" value="{{$PurchPrevY2}}">
<input type="hidden" id="PurchPrevY3" value="{{$PurchPrevY3}}">
<input type="hidden" id="PurchPrevY4" value="{{$PurchPrevY4}}">
<input type="hidden" id="PurchPrevYC" value="{{$PurchPrevYC}}">




<input type="hidden" id="ReturnSalesJan" value="{{$ReturnSalesJan}}">
<input type="hidden" id="ReturnSalesFeb" value="{{$ReturnSalesFeb}}">
<input type="hidden" id="ReturnSalesMar" value="{{$ReturnSalesMar}}">
<input type="hidden" id="ReturnSalesApr" value="{{$ReturnSalesApr}}">
<input type="hidden" id="ReturnSalesMay" value="{{$ReturnSalesMay}}">
<input type="hidden" id="ReturnSalesJun" value="{{$ReturnSalesJun}}">
<input type="hidden" id="ReturnSalesJul" value="{{$ReturnSalesJul}}">
<input type="hidden" id="ReturnSalesAug" value="{{$ReturnSalesAug}}">
<input type="hidden" id="ReturnSalesSep" value="{{$ReturnSalesSep}}">
<input type="hidden" id="ReturnSalesOct" value="{{$ReturnSalesOct}}">
<input type="hidden" id="ReturnSalesNov" value="{{$ReturnSalesNov}}">
<input type="hidden" id="ReturnSalesDec" value="{{$ReturnSalesDec}}">

<input type="hidden" id="ReturnSalesSat" value="{{$ReturnSalesSat}}">
<input type="hidden" id="ReturnSalesSun" value="{{$ReturnSalesSun}}">
<input type="hidden" id="ReturnSalesMon" value="{{$ReturnSalesMon}}">
<input type="hidden" id="ReturnSalesTue" value="{{$ReturnSalesTue}}">
<input type="hidden" id="ReturnSalesWed" value="{{$ReturnSalesWed}}">
<input type="hidden" id="ReturnSalesThr" value="{{$ReturnSalesThr}}">
<input type="hidden" id="ReturnSalesFri" value="{{$ReturnSalesFri}}">

<input type="hidden" id="ReturnSalesPrevY1" value="{{$ReturnSalesPrevY1}}">
<input type="hidden" id="ReturnSalesPrevY2" value="{{$ReturnSalesPrevY2}}">
<input type="hidden" id="ReturnSalesPrevY3" value="{{$ReturnSalesPrevY3}}">
<input type="hidden" id="ReturnSalesPrevY4" value="{{$ReturnSalesPrevY4}}">
<input type="hidden" id="ReturnSalesPrevYC" value="{{$ReturnSalesPrevYC}}">


<input type="hidden" id="ReturnPurchJan" value="{{$ReturnPurchJan}}">
<input type="hidden" id="ReturnPurchFeb" value="{{$ReturnPurchFeb}}">
<input type="hidden" id="ReturnPurchMar" value="{{$ReturnPurchMar}}">
<input type="hidden" id="ReturnPurchApr" value="{{$ReturnPurchApr}}">
<input type="hidden" id="ReturnPurchMay" value="{{$ReturnPurchMay}}">
<input type="hidden" id="ReturnPurchJun" value="{{$ReturnPurchJun}}">
<input type="hidden" id="ReturnPurchJul" value="{{$ReturnPurchJul}}">
<input type="hidden" id="ReturnPurchAug" value="{{$ReturnPurchAug}}">
<input type="hidden" id="ReturnPurchSep" value="{{$ReturnPurchSep}}">
<input type="hidden" id="ReturnPurchOct" value="{{$ReturnPurchOct}}">
<input type="hidden" id="ReturnPurchNov" value="{{$ReturnPurchNov}}">
<input type="hidden" id="ReturnPurchDec" value="{{$ReturnPurchDec}}">
<input type="hidden" id="ReturnPurchSat" value="{{$ReturnPurchSat}}">
<input type="hidden" id="ReturnPurchSun" value="{{$ReturnPurchSun}}">
<input type="hidden" id="ReturnPurchMon" value="{{$ReturnPurchMon}}">
<input type="hidden" id="ReturnPurchTue" value="{{$ReturnPurchTue}}">
<input type="hidden" id="ReturnPurchWed" value="{{$ReturnPurchWed}}">
<input type="hidden" id="ReturnPurchThr" value="{{$ReturnPurchThr}}">
<input type="hidden" id="ReturnPurchFri" value="{{$ReturnPurchFri}}">
<input type="hidden" id="ReturnPurchPrevY1" value="{{$ReturnPurchPrevY1}}">
<input type="hidden" id="ReturnPurchPrevY2" value="{{$ReturnPurchPrevY2}}">
<input type="hidden" id="ReturnPurchPrevY3" value="{{$ReturnPurchPrevY3}}">
<input type="hidden" id="ReturnPurchPrevY4" value="{{$ReturnPurchPrevY4}}">
<input type="hidden" id="ReturnPurchPrevYC" value="{{$ReturnPurchPrevYC}}">
<input type="hidden" id="EgmalyMasrofatJan" value="{{$EgmalyMasrofatJan}}">
<input type="hidden" id="EgmalyMasrofatFeb" value="{{$EgmalyMasrofatFeb}}">
<input type="hidden" id="EgmalyMasrofatMar" value="{{$EgmalyMasrofatMar}}">
<input type="hidden" id="EgmalyMasrofatApr" value="{{$EgmalyMasrofatApr}}">
<input type="hidden" id="EgmalyMasrofatMay" value="{{$EgmalyMasrofatMay}}">
<input type="hidden" id="EgmalyMasrofatJun" value="{{$EgmalyMasrofatJun}}">
<input type="hidden" id="EgmalyMasrofatJul" value="{{$EgmalyMasrofatJul}}">
<input type="hidden" id="EgmalyMasrofatAug" value="{{$EgmalyMasrofatAug}}">
<input type="hidden" id="EgmalyMasrofatSep" value="{{$EgmalyMasrofatSep}}">
<input type="hidden" id="EgmalyMasrofatOct" value="{{$EgmalyMasrofatOct}}">
<input type="hidden" id="EgmalyMasrofatNov" value="{{$EgmalyMasrofatNov}}">
<input type="hidden" id="EgmalyMasrofatDec" value="{{$EgmalyMasrofatDec}}">
<input type="hidden" id="EgmalyMasrofatSat" value="{{$EgmalyMasrofatSat}}">
<input type="hidden" id="EgmalyMasrofatSun" value="{{$EgmalyMasrofatSun}}">
<input type="hidden" id="EgmalyMasrofatMon" value="{{$EgmalyMasrofatMon}}">
<input type="hidden" id="EgmalyMasrofatTue" value="{{$EgmalyMasrofatTue}}">
<input type="hidden" id="EgmalyMasrofatWed" value="{{$EgmalyMasrofatWed}}">
<input type="hidden" id="EgmalyMasrofatThr" value="{{$EgmalyMasrofatThr}}">
<input type="hidden" id="EgmalyMasrofatFri" value="{{$EgmalyMasrofatFri}}">
<input type="hidden" id="EgmalyMasrofatY1" value="{{$EgmalyMasrofatY1}}">
<input type="hidden" id="EgmalyMasrofatY2" value="{{$EgmalyMasrofatY2}}">
<input type="hidden" id="EgmalyMasrofatY3" value="{{$EgmalyMasrofatY3}}">
<input type="hidden" id="EgmalyMasrofatY4" value="{{$EgmalyMasrofatY4}}">
<input type="hidden" id="EgmalyMasrofatCY" value="{{$EgmalyMasrofatCY}}">


<input type="hidden" id="PaymentVoucherJan" value="{{$PaymentVoucherJan}}">
<input type="hidden" id="ReciptVoucherJan" value="{{$ReciptVoucherJan}}">
<input type="hidden" id="PaymentVoucherFeb" value="{{$PaymentVoucherFeb}}">
<input type="hidden" id="ReciptVoucherFeb" value="{{$ReciptVoucherFeb}}">
<input type="hidden" id="PaymentVoucherMar" value="{{$PaymentVoucherMar}}">
<input type="hidden" id="ReciptVoucherMar" value="{{$ReciptVoucherMar}}">
<input type="hidden" id="PaymentVoucherApr" value="{{$PaymentVoucherApr}}">
<input type="hidden" id="ReciptVoucherApr" value="{{$ReciptVoucherApr}}">
<input type="hidden" id="PaymentVoucherMay" value="{{$PaymentVoucherMay}}">
<input type="hidden" id="ReciptVoucherMay" value="{{$ReciptVoucherMay}}">
<input type="hidden" id="PaymentVoucherJun" value="{{$PaymentVoucherJun}}">
<input type="hidden" id="ReciptVoucherJun" value="{{$ReciptVoucherJun}}">
<input type="hidden" id="PaymentVoucherJul" value="{{$PaymentVoucherJul}}">
<input type="hidden" id="ReciptVoucherJul" value="{{$ReciptVoucherJul}}">
<input type="hidden" id="PaymentVoucherAug" value="{{$PaymentVoucherAug}}">
<input type="hidden" id="ReciptVoucherAug" value="{{$ReciptVoucherAug}}">
<input type="hidden" id="PaymentVoucherSep" value="{{$PaymentVoucherSep}}">
<input type="hidden" id="ReciptVoucherSep" value="{{$ReciptVoucherSep}}">
<input type="hidden" id="PaymentVoucherOct" value="{{$PaymentVoucherOct}}">
<input type="hidden" id="ReciptVoucherOct" value="{{$ReciptVoucherOct}}">
<input type="hidden" id="PaymentVoucherNov" value="{{$PaymentVoucherNov}}">
<input type="hidden" id="ReciptVoucherNov" value="{{$ReciptVoucherNov}}">
<input type="hidden" id="PaymentVoucherDec" value="{{$PaymentVoucherDec}}">
<input type="hidden" id="ReciptVoucherDec" value="{{$ReciptVoucherDec}}">

<input type="hidden" id="PaymentVoucherSat" value="{{$PaymentVoucherSat}}">
<input type="hidden" id="ReciptVoucherSat" value="{{$ReciptVoucherSat}}">
<input type="hidden" id="PaymentVoucherSun" value="{{$PaymentVoucherSun}}">
<input type="hidden" id="ReciptVoucherSun" value="{{$ReciptVoucherSun}}">
<input type="hidden" id="PaymentVoucherMon" value="{{$PaymentVoucherMon}}">
<input type="hidden" id="ReciptVoucherMon" value="{{$ReciptVoucherMon}}">
<input type="hidden" id="PaymentVoucherTue" value="{{$PaymentVoucherTue}}">
<input type="hidden" id="ReciptVoucherTue" value="{{$ReciptVoucherTue}}">
<input type="hidden" id="PaymentVoucherWed" value="{{$PaymentVoucherWed}}">
<input type="hidden" id="ReciptVoucherWed" value="{{$ReciptVoucherWed}}">
<input type="hidden" id="PaymentVoucherThr" value="{{$PaymentVoucherThr}}">
<input type="hidden" id="ReciptVoucherThr" value="{{$ReciptVoucherThr}}">
<input type="hidden" id="PaymentVoucherFri" value="{{$PaymentVoucherFri}}">
<input type="hidden" id="ReciptVoucherFri" value="{{$ReciptVoucherFri}}">

<input type="hidden" id="IncomChecksJan" value="{{$IncomChecksJan}}">
<input type="hidden" id="ExportChecksJan" value="{{$ExportChecksJan}}">
<input type="hidden" id="IncomChecksFeb" value="{{$IncomChecksFeb}}">
<input type="hidden" id="ExportChecksFeb" value="{{$ExportChecksFeb}}">
<input type="hidden" id="IncomChecksMar" value="{{$IncomChecksMar}}">
<input type="hidden" id="ExportChecksMar" value="{{$ExportChecksMar}}">
<input type="hidden" id="IncomChecksApr" value="{{$IncomChecksApr}}">
<input type="hidden" id="ExportChecksApr" value="{{$ExportChecksApr}}">
<input type="hidden" id="IncomChecksMay" value="{{$IncomChecksMay}}">
<input type="hidden" id="ExportChecksMay" value="{{$ExportChecksMay}}">
<input type="hidden" id="IncomChecksJun" value="{{$IncomChecksJun}}">
<input type="hidden" id="ExportChecksJun" value="{{$ExportChecksJun}}">
<input type="hidden" id="IncomChecksJul" value="{{$IncomChecksJul}}">
<input type="hidden" id="ExportChecksJul" value="{{$ExportChecksJul}}">
<input type="hidden" id="IncomChecksAug" value="{{$IncomChecksAug}}">
<input type="hidden" id="ExportChecksAug" value="{{$ExportChecksAug}}">
<input type="hidden" id="IncomChecksSep" value="{{$IncomChecksSep}}">
<input type="hidden" id="ExportChecksSep" value="{{$ExportChecksSep}}">
<input type="hidden" id="IncomChecksOct" value="{{$IncomChecksOct}}">
<input type="hidden" id="ExportChecksOct" value="{{$ExportChecksOct}}">
<input type="hidden" id="IncomChecksNov" value="{{$IncomChecksNov}}">
<input type="hidden" id="ExportChecksNov" value="{{$ExportChecksNov}}">
<input type="hidden" id="IncomChecksDec" value="{{$IncomChecksDec}}">
<input type="hidden" id="ExportChecksDec" value="{{$ExportChecksDec}}">

<input type="hidden" id="IncomChecksSat" value="{{$IncomChecksSat}}">
<input type="hidden" id="ExportChecksSat" value="{{$ExportChecksSat}}">
<input type="hidden" id="IncomChecksSun" value="{{$IncomChecksSun}}">
<input type="hidden" id="ExportChecksSun" value="{{$ExportChecksSun}}">
<input type="hidden" id="IncomChecksMon" value="{{$IncomChecksMon}}">
<input type="hidden" id="ExportChecksMon" value="{{$ExportChecksMon}}">
<input type="hidden" id="IncomChecksTue" value="{{$IncomChecksTue}}">
<input type="hidden" id="ExportChecksTue" value="{{$ExportChecksTue}}">
<input type="hidden" id="IncomChecksWed" value="{{$IncomChecksWed}}">
<input type="hidden" id="ExportChecksWed" value="{{$ExportChecksWed}}">
<input type="hidden" id="IncomChecksThr" value="{{$IncomChecksThr}}">
<input type="hidden" id="ExportChecksThr" value="{{$ExportChecksThr}}">
<input type="hidden" id="IncomChecksFri" value="{{$IncomChecksFri}}">
<input type="hidden" id="ExportChecksFri" value="{{$ExportChecksFri}}">

<input type="hidden" id="SafyRab7Jan" value="{{$SafyRab7Jan}}">
<input type="hidden" id="SafyRab7Feb" value="{{$SafyRab7Feb}}">
<input type="hidden" id="SafyRab7Mar" value="{{$SafyRab7Mar}}">
<input type="hidden" id="SafyRab7Apr" value="{{$SafyRab7Apr}}">
<input type="hidden" id="SafyRab7May" value="{{$SafyRab7May}}">
<input type="hidden" id="SafyRab7Jun" value="{{$SafyRab7Jun}}">
<input type="hidden" id="SafyRab7Jul" value="{{$SafyRab7Jul}}">
<input type="hidden" id="SafyRab7Aug" value="{{$SafyRab7Aug}}">
<input type="hidden" id="SafyRab7Sep" value="{{$SafyRab7Sep}}">
<input type="hidden" id="SafyRab7Oct" value="{{$SafyRab7Oct}}">
<input type="hidden" id="SafyRab7Nov" value="{{$SafyRab7Nov}}">
<input type="hidden" id="SafyRab7Dec" value="{{$SafyRab7Dec}}">



@foreach($Groups as $grop)
@php  $Vals=ProductSales::where('Group',$grop->id)->get()->sum('Total');  @endphp
<input type="hidden" name="pname[]" value="{{$grop->Name}}">
<input type="hidden" name="pVal[]" value="{{$Vals}}">
@endforeach

@foreach($Brands as $bran)
@php  $BVals=ProductSales::where('Brand',$bran->id)->get()->sum('Total');  @endphp
<input type="hidden" name="bname[]" value="{{$bran->Name}}">
<input type="hidden" name="bVal[]" value="{{$BVals}}">
@endforeach
                @endif



               </main>
               <!-- this overlay is activated only when mobile menu is triggered -->
                 <!-- base css -->





@endsection


@push('js')


           <link rel="stylesheet" media="screen, print" href="{{asset('Admin/css/statistics/chartjs/chartjs.css')}}">
        <link rel="stylesheet" media="screen, print" href="{{asset('Admin/css/statistics/chartist/chartist.css')}}">
        <link rel="stylesheet" media="screen, print" href="{{asset('Admin/css/statistics/c3/c3.css')}}">


        <!-- The order of scripts is irrelevant. Please check out the plugin pages for more details about these plugins below: -->
        <script src="{{asset('Admin/js/statistics/peity/peity.bundle.js')}}"></script>
        <script src="{{asset('Admin/js/statistics/flot/flot.bundle.js')}}"></script>
        <script src="{{asset('Admin/js/statistics/easypiechart/easypiechart.bundle.js')}}"></script>
        <script src="{{asset('Admin/js/datagrid/datatables/datatables.bundle.js')}}"></script>
       <script src="{{asset('Admin/js/statistics/chartjs/chartjs.bundle.js')}}"></script>
       <script src="{{asset('Admin/js/statistics/chartist/chartist.js')}}"></script>
        <script src="{{asset('Admin/js/statistics/d3/d3.js')}}"></script>
        <script src="{{asset('Admin/js/statistics/c3/c3.js')}}"></script>
        <script src="{{asset('Admin/js/statistics/demo-data/demo-c3.js')}}"></script>



<!-- Sales -->
        <script>
            /* data of sales */

               var SalesJan=$('#SalesJan').val();
               var SalesFeb=$('#SalesFeb').val();
               var SalesMar=$('#SalesMar').val();
               var SalesApr=$('#SalesApr').val();
               var SalesMay=$('#SalesMay').val();
               var SalesJun=$('#SalesJun').val();
               var SalesJul=$('#SalesJul').val();
               var SalesAug=$('#SalesAug').val();
               var SalesSep=$('#SalesSep').val();
               var SalesOct=$('#SalesOct').val();
               var SalesNov=$('#SalesNov').val();
               var SalesDec=$('#SalesDec').val();

               var SalesSat=$('#SalesSat').val();
               var SalesSun=$('#SalesSun').val();
               var SalesMon=$('#SalesMon').val();
               var SalesTue=$('#SalesTue').val();
               var SalesWed=$('#SalesWed').val();
               var SalesThr=$('#SalesThr').val();
               var SalesFri=$('#SalesFri').val();

               var SalesPrevY1=$('#SalesPrevY1').val();
               var SalesPrevY2=$('#SalesPrevY2').val();
               var SalesPrevY3=$('#SalesPrevY3').val();
               var SalesPrevY4=$('#SalesPrevY4').val();
               var SalesPrevYC=$('#SalesPrevYC').val();

               var YearName1=$('#YearName1').val();
               var YearName2=$('#YearName2').val();
               var YearName3=$('#YearName3').val();
               var YearName4=$('#YearName4').val();
               var YearNameC=$('#YearNameC').val();









            var dataTargetProfit = [
                [1354586000000, SalesJan],
                [1364587000000, SalesFeb],
                [1374588000000, SalesMar],
                [1384589000000, SalesApr],
                [1394590000000, SalesMay],
                [1404591000000, SalesJun],
                [1414592000000, SalesJul],
                [1424593000000, SalesAug],
                [1434594000000, SalesSep],
                [1444595000000, SalesOct],
                [1454596000000, SalesNov],
                [1464597000000, SalesDec]
            ]

            var dataProfit = [
                [1354587000000, 900],
                [1364587000000, 65],
                [1374588000000, 98],
                [1384589000000, 83],
                [1394590000000, 980],
                [1404591000000, 808],
                [1414592000000, 720],
                [1424593000000, 674],
                [1434594000000, 23],
                [1444595000000, 79],
                [1454596000000, 88],
                [1464597000000, 36]
            ]

            var dataSignups = [
                [1354586000000, SalesJan],
                [1364587000000, SalesFeb],
                [1374588000000, SalesMar],
                [1384589000000, SalesApr],
                [1394590000000, SalesMay],
                [1404591000000, SalesJun],
                [1414592000000, SalesJul],
                [1424593000000, SalesAug],
                [1434594000000, SalesSep],
                [1444595000000, SalesOct],
                [1454596000000, SalesNov],
                [1464597000000, SalesDec]
            ]


            var dataSet1 = [
                [0, SalesPrevY4],
                [100, SalesPrevY3],
                [200, SalesPrevY2],
                [300, SalesPrevY1],
                [400, SalesPrevYC]





            ];
            var dataSet2 = [
                [0, SalesPrevY4],
                [100, SalesPrevY3],
                [200, SalesPrevY2],
                [300, SalesPrevY1],
                [400, SalesPrevYC]

            ];

            var pyrmid=$('#toggleOneName1').val();
            var graph=$('#toggleOneName2').val();
            $(document).ready(function()
            {
                /* f Sales Monthly*/
                var flot_toggle = function()
                {

                    var data = [
                    {
                        label: pyrmid,
                        data: dataTargetProfit,
                        color: color.info._400,
                        bars:
                        {
                            show: true,
                            align: "center",
                            barWidth: 30 * 30 * 60 * 1000 * 80,
                            lineWidth: 0,
                            fillColor:
                            {
                                colors: [
                                {
                                    opacity: 0.9
                                },
                                {
                                    opacity: 0.1
                                }]
                            }
                        },
                        lines:
                        {
                            show: true,
                            lineWidth: 2
                        },
                        highlightColor: 'rgba(255,255,255,0.3)',
                        shadowSize: 0
                    },
                    {
                        label: "",
                        data: dataProfit,
                        color: "",
                        lines:
                        {
                            show: false,
                            lineWidth: 2
                        },
                        shadowSize: 0,
                        points:
                        {
                            show: false
                        }
                    },
                    {
                        label:graph,
                        data: dataSignups,
                        color: color.warning._500,
                        lines:
                        {
                            show: true,
                            lineWidth: 2
                        },
                        shadowSize: 0,
                        points:
                        {
                            show: true
                        }
                    }]

                    var options = {
                        grid:
                        {
                            hoverable: true,
                            clickable: true,
                            tickColor: 'rgba(0,0,0,0.05)',
                            borderWidth: 1,
                            borderColor: 'rgba(0,0,0,0.05)'
                        },
                        tooltip: {
        show: true,
        content: "Total: %y"
   },
                        tooltipOpts:
                        {
                            cssClass: 'tooltip-inner',
                            defaultTheme: false
                        },
                        xaxis:
                        {
                              mode: "times",
            tickLength: 0,
            ticks: [
                [1354586000000, 'Jan'],
                [1364587000000, 'Feb'],
                [1374588000000, 'March'],
                [1384589000000, 'April'],
                [1394590000000, 'May'],
                [1404591000000, 'June'],
                [1414592000000, 'Jul'],
                [1424593000000, 'Aug'],
                [1434594000000, 'Sep'],
                [1444595000000, 'Oct'],
                [1454596000000, 'Nov'],
                [1464597000000, 'Desc'],
            ]

                        },
                        yaxes:
                        {


                            tickColor: 'rgba(0,0,0,0.05)'

                        }

                    };

                    var plot2 = null;

                    function plotNow()
                    {
                        var d = [];
                        $("#js-checkbox-toggles").find(':checkbox').each(function()
                        {
                            if ($(this).is(':checked'))
                            {
                                d.push(data[$(this).attr("name").substr(4, 1)]);
                            }
                        });
                        if (d.length > 0)
                        {
                            if (plot2)
                            {
                                plot2.setData(d);
                                plot2.draw();
                            }
                            else
                            {
                                plot2 = $.plot($("#flot-toggles"), d, options);
                            }
                        }

                    };

                    $("#js-checkbox-toggles").find(':checkbox').on('change', function()
                    {
                        plotNow();
                    });
                    plotNow()
                }
                flot_toggle();
                /*  Sales Monthly-- end*/


              /* ========================================= */

                /* Sales Yearly */
                var flotArea = $.plot($('#flot-area'), [
                {
                    data: dataSet1,
                    label: 'Yearly',
                    color: color.success._200
                },
                {
                    data: dataSet2,
                    label: 'Yearly',
                    color: color.info._200
                }],
                {
                    series:
                    {
                        lines:
                        {
                            show: true,
                            lineWidth: 2,
                            fill: true,
                            fillColor:
                            {
                                colors: [
                                {
                                    opacity: 0
                                },
                                {
                                    opacity: 0.5
                                }]
                            }
                        },
                        shadowSize: 0
                    },
                    points:
                    {
                        show: true,
                    },
                    legend:
                    {
                        noColumns: 1,
                        position: 'nw'
                    },
                    grid:
                    {
                        hoverable: true,
                        clickable: true,
                        borderColor: '#ddd',
                        tickColor: 'rgba(0,0,0,0.05)',
                        aboveData: true,
                        borderWidth: 0,
                        labelMargin: 5,
                        backgroundColor: 'transparent'
                    },
            tooltip: {
        show: true,
        content: "Total: %y"
   },
                        tooltipOpts:
                        {
                            cssClass: 'tooltip-inner',
                            defaultTheme: false
                        },

                    yaxis:
                    {

                        tickColor: 'rgba(0,0,0,0.05)'

                    },
                    xaxis:
                    {
                        tickLength: 1,
                        color: '#eee',
                        tickColor: 'rgba(0,0,0,0.05)',
                          ticks: [
                            [0, YearName4],
                            [100, YearName3],
                            [200, YearName2],
                            [300, YearName1],
                            [400, YearNameC]

                        ],
                        font:
                        {
                            size: 10,
                            color: '#999'
                        }
                    }

                });




                /* Sales Weekly  */

                var flotVisit = $.plot('#flotVisit', [
                {
                    data: [
                        [2, SalesSat],
                        [3, SalesSun],
                        [4, SalesMon],
                        [5, SalesTue],
                        [6, SalesWed],
                        [7, SalesThr],
                        [8, SalesFri]

                    ],
                    color: color.success._200
                },
                {
                    data: [
                        [2, SalesSat],
                        [3, SalesSun],
                        [4, SalesMon],
                        [5, SalesTue],
                        [6, SalesWed],
                        [7, SalesThr],
                        [8, SalesFri]
                    ],
                    color: color.info._200
                }],
                {
                    series:
                    {
                        shadowSize: 0,
                        lines:
                        {
                            show: true,
                            lineWidth: 2,
                            fill: true,
                            fillColor:
                            {
                                colors: [
                                {
                                    opacity: 0
                                },
                                {
                                    opacity: 0.12
                                }]
                            }
                        }
                    },
                    grid:
                    {
                               hoverable: true,
                            clickable: true,
                            tickColor: 'rgba(0,0,0,0.05)',
                            borderWidth: 1,
                            borderColor: 'rgba(0,0,0,0.05)'
                    },

                        tooltip: {
        show: true,
        content: "Total: %y"
   },
                        tooltipOpts:
                        {
                            cssClass: 'tooltip-inner',
                            defaultTheme: false
                        },


                    yaxis:
                    {

                        tickColor: 'rgba(0,0,0,0.05)',

                        font:
                        {
                            color: '#444',
                            size: 10
                        }
                    },
                    xaxis:
                    {

                        tickColor: 'rgba(0,0,0,0.05)',
                        ticks: [
                            [2, 'Sat'],
                            [3, 'Sun'],
                            [4, 'Mon'],
                            [5, 'Tue'],
                            [6, 'Wed'],
                            [7, 'Thr'],
                            [8, 'Fri']

                        ],
                        font:
                        {
                            color: '#999',
                            size: 9
                        }
                    }
                });


            });

        </script>
<script>
                       /* Sales Half */
            var extreamResponsive = function()
            {

                                      var SalesJan=$('#SalesJan').val();
               var SalesFeb=$('#SalesFeb').val();
               var SalesMar=$('#SalesMar').val();
               var SalesApr=$('#SalesApr').val();
               var SalesMay=$('#SalesMay').val();
               var SalesJun=$('#SalesJun').val();
               var SalesJul=$('#SalesJul').val();
               var SalesAug=$('#SalesAug').val();
               var SalesSep=$('#SalesSep').val();
               var SalesOct=$('#SalesOct').val();
               var SalesNov=$('#SalesNov').val();
               var SalesDec=$('#SalesDec').val();
                new Chartist.Bar('#extreamResponsive',
                {
                    labels: ['Half 1', 'Half 2'],
                    series: [
                        [SalesJan, SalesJul],
                        [SalesFeb, SalesAug],
                        [SalesMar, SalesSep],
                        [SalesApr, SalesOct],
                        [SalesMay, SalesNov],
                        [SalesJun, SalesDec]

                    ]
                },
                {
                    // Default mobile configuration
                    stackBars: true,
                    axisX:
                    {
                        labelInterpolationFnc: function(value)
                        {
                            return value.split(/\s+/).map(function(word)
                            {
                                return word[0];
                            }).join('');
                        }

                    },
                    axisY:
                    {

                    }
                }, [
                    // Options override for media > 400px
                    ['screen and (min-width: 400px)',
                    {
                        reverseData: true,
                        horizontalBars: true,
                        axisX:
                        {
                            labelInterpolationFnc: Chartist.noop
                        },
                        axisY:
                        {
                            offset: 60
                        }
                    }],
                    // Options override for media > 800px
                    ['screen and (min-width: 800px)',
                    {
                        stackBars: false,
                        seriesBarDistance: 10
                    }],
                    // Options override for media > 1000px
                    ['screen and (min-width: 1000px)',
                    {
                        reverseData: false,
                        horizontalBars: false,
                        seriesBarDistance: 15
                    }]
                ]);
            }


            /* Sales Quarters */
            var multiLineLabels = function()
            {

                             var SalesJan=$('#SalesJan').val();
               var SalesFeb=$('#SalesFeb').val();
               var SalesMar=$('#SalesMar').val();
               var SalesApr=$('#SalesApr').val();
               var SalesMay=$('#SalesMay').val();
               var SalesJun=$('#SalesJun').val();
               var SalesJul=$('#SalesJul').val();
               var SalesAug=$('#SalesAug').val();
               var SalesSep=$('#SalesSep').val();
               var SalesOct=$('#SalesOct').val();
               var SalesNov=$('#SalesNov').val();
               var SalesDec=$('#SalesDec').val();

                new Chartist.Bar('#multiLineLabels',
                {
                    labels: ['First quarter of the year', 'Second quarter of the year', 'Third quarter of the year', 'Fourth quarter of the year'],
                    series: [
                        [SalesJan, SalesApr, SalesJul, SalesOct],
                        [SalesFeb, SalesMay, SalesAug, SalesNov],
                        [SalesMar, SalesJun, SalesSep, SalesDec]
                    ]
                },
                {
                    seriesBarDistance: 10,
                    axisX:
                    {

                    },
                    axisY:
                    {
                        scaleMinSpace: 15
                    }


                });
            }


            $(document).ready(function()
            {

   extreamResponsive();
                   multiLineLabels();


            });

</script>



<!-- Purchases -->
        <script>
            /* data of Purchases */

               var PurchJan=$('#PurchJan').val();
               var PurchFeb=$('#PurchFeb').val();
               var PurchMar=$('#PurchMar').val();
               var PurchApr=$('#PurchApr').val();
               var PurchMay=$('#PurchMay').val();
               var PurchJun=$('#PurchJun').val();
               var PurchJul=$('#PurchJul').val();
               var PurchAug=$('#PurchAug').val();
               var PurchSep=$('#PurchSep').val();
               var PurchOct=$('#PurchOct').val();
               var PurchNov=$('#PurchNov').val();
               var PurchDec=$('#PurchDec').val();

               var PurchSat=$('#PurchSat').val();
               var PurchSun=$('#PurchSun').val();
               var PurchMon=$('#PurchMon').val();
               var PurchTue=$('#PurchTue').val();
               var PurchWed=$('#PurchWed').val();
               var PurchThr=$('#PurchThr').val();
               var PurchFri=$('#PurchFri').val();

               var PurchPrevY1=$('#PurchPrevY1').val();
               var PurchPrevY2=$('#PurchPrevY2').val();
               var PurchPrevY3=$('#PurchPrevY3').val();
               var PurchPrevY4=$('#PurchPrevY4').val();
               var PurchPrevYC=$('#PurchPrevYC').val();

               var YearName1=$('#YearName1').val();
               var YearName2=$('#YearName2').val();
               var YearName3=$('#YearName3').val();
               var YearName4=$('#YearName4').val();
               var YearNameC=$('#YearNameC').val();



            var dataTargetProfitPurch = [
                [1354586000000, PurchJan],
                [1364587000000, PurchFeb],
                [1374588000000, PurchMar],
                [1384589000000, PurchApr],
                [1394590000000, PurchMay],
                [1404591000000, PurchJun],
                [1414592000000, PurchJul],
                [1424593000000, PurchAug],
                [1434594000000, PurchSep],
                [1444595000000, PurchOct],
                [1454596000000, PurchNov],
                [1464597000000, PurchDec]
            ]

            var dataProfitPurch = [
                [1354587000000, 900],
                [1364587000000, 65],
                [1374588000000, 98],
                [1384589000000, 83],
                [1394590000000, 980],
                [1404591000000, 808],
                [1414592000000, 720],
                [1424593000000, 674],
                [1434594000000, 23],
                [1444595000000, 79],
                [1454596000000, 88],
                [1464597000000, 36]
            ]

            var dataSignupsPurch = [
                [1354586000000, PurchJan],
                [1364587000000, PurchFeb],
                [1374588000000, PurchMar],
                [1384589000000, PurchApr],
                [1394590000000, PurchMay],
                [1404591000000, PurchJun],
                [1414592000000, PurchJul],
                [1424593000000, PurchAug],
                [1434594000000, PurchSep],
                [1444595000000, PurchOct],
                [1454596000000, PurchNov],
                [1464597000000, PurchDec]
            ]


            var dataSetPurch1 = [
                [0, PurchPrevY4],
                [100, PurchPrevY3],
                [200, PurchPrevY2],
                [300, PurchPrevY1],
                [400, PurchPrevYC]





            ];
            var dataSetPurch2 = [
                [0, PurchPrevY4],
                [100, PurchPrevY3],
                [200, PurchPrevY2],
                [300, PurchPrevY1],
                [400, PurchPrevYC]

            ];

            var pyrmid=$('#toggleOneName1').val();
            var graph=$('#toggleOneName2').val();
            $(document).ready(function()
            {
                /* f Purchases Monthly*/
                var flot_togglePurch = function()
                {

                    var data = [
                    {
                        label: pyrmid,
                        data: dataTargetProfitPurch,
                        color:"red",
                        bars:
                        {
                            show: true,
                            align: "center",
                            barWidth: 30 * 30 * 60 * 1000 * 80,
                            lineWidth: 0,
                            fillColor:
                            {
                                colors: [
                                {
                                    opacity: 0.9
                                },
                                {
                                    opacity: 0.1
                                }]
                            }
                        },
                        lines:
                        {
                            show: true,
                            lineWidth: 2
                        },
                        highlightColor: 'rgba(255,255,255,0.3)',
                        shadowSize: 0
                    },
                    {
                        label: "",
                        data: dataProfitPurch,
                        color: "",
                        lines:
                        {
                            show: false,
                            lineWidth: 2
                        },
                        shadowSize: 0,
                        points:
                        {
                            show: false
                        }
                    },
                    {
                        label:graph,
                        data: dataSignupsPurch,
                         color:"green",
                        lines:
                        {
                            show: true,
                            lineWidth: 2
                        },
                        shadowSize: 0,
                        points:
                        {
                            show: true
                        }
                    }]

                    var options = {
                        grid:
                        {
                            hoverable: true,
                            clickable: true,
                            tickColor: 'rgba(0,0,0,0.05)',
                            borderWidth: 1,
                            borderColor: 'rgba(0,0,0,0.05)'
                        },
                        tooltip: {
        show: true,
        content: "Total: %y"
   },
                        tooltipOpts:
                        {
                            cssClass: 'tooltip-inner',
                            defaultTheme: false
                        },
                        xaxis:
                        {
                              mode: "times",
            tickLength: 0,
            ticks: [
                [1354586000000, 'Jan'],
                [1364587000000, 'Feb'],
                [1374588000000, 'March'],
                [1384589000000, 'April'],
                [1394590000000, 'May'],
                [1404591000000, 'June'],
                [1414592000000, 'Jul'],
                [1424593000000, 'Aug'],
                [1434594000000, 'Sep'],
                [1444595000000, 'Oct'],
                [1454596000000, 'Nov'],
                [1464597000000, 'Desc'],
            ]

                        },
                        yaxes:
                        {


                            tickColor: 'rgba(0,0,0,0.05)'

                        }

                    };

                    var plot2 = null;

                    function plotNow()
                    {
                        var d = [];
                        $("#js-checkbox-toggles-Purchases").find(':checkbox').each(function()
                        {
                            if ($(this).is(':checked'))
                            {
                                d.push(data[$(this).attr("name").substr(4, 1)]);
                            }
                        });
                        if (d.length > 0)
                        {
                            if (plot2)
                            {
                                plot2.setData(d);
                                plot2.draw();
                            }
                            else
                            {
                                plot2 = $.plot($("#flot-toggles-Purchases"), d, options);
                            }
                        }

                    };

                    $("#js-checkbox-toggles-Purchases").find(':checkbox').on('change', function()
                    {
                        plotNow();
                    });
                    plotNow()
                }
                flot_togglePurch();
                /*  Purchases Monthly-- end*/


              /* ========================================= */

                /* Purchases Yearly */
                var flotAreaPurch = $.plot($('#flot-area-Purchases'), [
                {
                    data: dataSetPurch1,
                    label: 'Yearly',
                    color: "darkblue"
                },
                {
                    data: dataSetPurch2,
                    label: 'Yearly',
                    color: "blue"
                }],
                {
                    series:
                    {
                        lines:
                        {
                            show: true,
                            lineWidth: 2,
                            fill: true,
                            fillColor:
                            {
                                colors: [
                                {
                                    opacity: 0
                                },
                                {
                                    opacity: 0.5
                                }]
                            }
                        },
                        shadowSize: 0
                    },
                    points:
                    {
                        show: true,
                    },
                    legend:
                    {
                        noColumns: 1,
                        position: 'nw'
                    },
                    grid:
                    {
                        hoverable: true,
                        clickable: true,
                        borderColor: '#ddd',
                        tickColor: 'rgba(0,0,0,0.05)',
                        aboveData: true,
                        borderWidth: 0,
                        labelMargin: 5,
                        backgroundColor: 'transparent'
                    },
            tooltip: {
        show: true,
        content: "Total: %y"
   },
                        tooltipOpts:
                        {
                            cssClass: 'tooltip-inner',
                            defaultTheme: false
                        },

                    yaxis:
                    {

                        tickColor: 'rgba(0,0,0,0.05)'

                    },
                    xaxis:
                    {
                        tickLength: 1,
                        color: '#eee',
                        tickColor: 'rgba(0,0,0,0.05)',
                          ticks: [
                            [0, YearName4],
                            [100, YearName3],
                            [200, YearName2],
                            [300, YearName1],
                            [400, YearNameC]

                        ],
                        font:
                        {
                            size: 10,
                            color: '#999'
                        }
                    }

                });




                /* Sales Weekly  */

                var flotVisitPurch = $.plot('#flotVisit-Purchases', [
                {
                    data: [
                        [2, PurchSat],
                        [3, PurchSun],
                        [4, PurchMon],
                        [5, PurchTue],
                        [6, PurchWed],
                        [7, PurchThr],
                        [8, PurchFri]

                    ],
                    color: "darkred"
                },
                {
                    data: [
                        [2, PurchSat],
                        [3, PurchSun],
                        [4, PurchMon],
                        [5, PurchTue],
                        [6, PurchWed],
                        [7, PurchThr],
                        [8, PurchFri]
                    ],
                    color: "red"
                }],
                {
                    series:
                    {
                        shadowSize: 0,
                        lines:
                        {
                            show: true,
                            lineWidth: 2,
                            fill: true,
                            fillColor:
                            {
                                colors: [
                                {
                                    opacity: 0
                                },
                                {
                                    opacity: 0.12
                                }]
                            }
                        }
                    },
                    grid:
                    {
                               hoverable: true,
                            clickable: true,
                            tickColor: 'rgba(0,0,0,0.05)',
                            borderWidth: 1,
                            borderColor: 'rgba(0,0,0,0.05)'
                    },

                        tooltip: {
        show: true,
        content: "Total: %y"
   },
                        tooltipOpts:
                        {
                            cssClass: 'tooltip-inner',
                            defaultTheme: false
                        },


                    yaxis:
                    {

                        tickColor: 'rgba(0,0,0,0.05)',

                        font:
                        {
                            color: '#444',
                            size: 10
                        }
                    },
                    xaxis:
                    {

                        tickColor: 'rgba(0,0,0,0.05)',
                        ticks: [
                            [2, 'Sat'],
                            [3, 'Sun'],
                            [4, 'Mon'],
                            [5, 'Tue'],
                            [6, 'Wed'],
                            [7, 'Thr'],
                            [8, 'Fri']

                        ],
                        font:
                        {
                            color: '#999',
                            size: 9
                        }
                    }
                });


            });

        </script>
<script>
                       /* Purchases Half */
            var extreamResponsivePurchases = function()
            {

                                      var SalesJan=$('#PurchJan').val();
               var SalesFeb=$('#PurchFeb').val();
               var SalesMar=$('#PurchMar').val();
               var SalesApr=$('#PurchApr').val();
               var SalesMay=$('#PurchMay').val();
               var SalesJun=$('#PurchJun').val();
               var SalesJul=$('#PurchJul').val();
               var SalesAug=$('#PurchAug').val();
               var SalesSep=$('#PurchSep').val();
               var SalesOct=$('#PurchOct').val();
               var SalesNov=$('#PurchNov').val();
               var SalesDec=$('#PurchDec').val();
                new Chartist.Bar('#extreamResponsive-Purchases',
                {
                    labels: ['Half 1', 'Half 2'],
                    series: [
                        [SalesJan, SalesJul],
                        [SalesFeb, SalesAug],
                        [SalesMar, SalesSep],
                        [SalesApr, SalesOct],
                        [SalesMay, SalesNov],
                        [SalesJun, SalesDec]

                    ]
                },
                {
                    // Default mobile configuration
                    stackBars: true,
                    axisX:
                    {
                        labelInterpolationFnc: function(value)
                        {
                            return value.split(/\s+/).map(function(word)
                            {
                                return word[0];
                            }).join('');
                        }

                    },
                    axisY:
                    {

                    }
                }, [
                    // Options override for media > 400px
                    ['screen and (min-width: 400px)',
                    {
                        reverseData: true,
                        horizontalBars: true,
                        axisX:
                        {
                            labelInterpolationFnc: Chartist.noop
                        },
                        axisY:
                        {
                            offset: 60
                        }
                    }],
                    // Options override for media > 800px
                    ['screen and (min-width: 800px)',
                    {
                        stackBars: false,
                        seriesBarDistance: 10
                    }],
                    // Options override for media > 1000px
                    ['screen and (min-width: 1000px)',
                    {
                        reverseData: false,
                        horizontalBars: false,
                        seriesBarDistance: 15
                    }]
                ]);
            }


            /* Sales Quarters */
            var multiLineLabelsPurchases = function()
            {

                                 var SalesJan=$('#PurchJan').val();
               var SalesFeb=$('#PurchFeb').val();
               var SalesMar=$('#PurchMar').val();
               var SalesApr=$('#PurchApr').val();
               var SalesMay=$('#PurchMay').val();
               var SalesJun=$('#PurchJun').val();
               var SalesJul=$('#PurchJul').val();
               var SalesAug=$('#PurchAug').val();
               var SalesSep=$('#PurchSep').val();
               var SalesOct=$('#PurchOct').val();
               var SalesNov=$('#PurchNov').val();
               var SalesDec=$('#PurchDec').val();

                new Chartist.Bar('#multiLineLabels-Purchases',
                {
                    labels: ['First quarter of the year', 'Second quarter of the year', 'Third quarter of the year', 'Fourth quarter of the year'],
                    series: [
                        [SalesJan, SalesApr, SalesJul, SalesOct],
                        [SalesFeb, SalesMay, SalesAug, SalesNov],
                        [SalesMar, SalesJun, SalesSep, SalesDec]
                    ]
                },
                {
                    seriesBarDistance: 10,
                    axisX:
                    {

                    },
                    axisY:
                    {
                        scaleMinSpace: 15
                    }


                });
            }


            $(document).ready(function()
            {

   extreamResponsivePurchases();
                   multiLineLabelsPurchases();


            });

</script>



<!-- ReturnSales -->
<script>

               var ReturnSalesJan=$('#ReturnSalesJan').val();
               var ReturnSalesFeb=$('#ReturnSalesFeb').val();
               var ReturnSalesMar=$('#ReturnSalesMar').val();
               var ReturnSalesApr=$('#ReturnSalesApr').val();
               var ReturnSalesMay=$('#ReturnSalesMay').val();
               var ReturnSalesJun=$('#ReturnSalesJun').val();
               var ReturnSalesJul=$('#ReturnSalesJul').val();
               var ReturnSalesAug=$('#ReturnSalesAug').val();
               var ReturnSalesSep=$('#ReturnSalesSep').val();
               var ReturnSalesOct=$('#ReturnSalesOct').val();
               var ReturnSalesNov=$('#ReturnSalesNov').val();
               var ReturnSalesDec=$('#ReturnSalesDec').val();

               var ReturnSalesSat=$('#ReturnSalesSat').val();
               var ReturnSalesSun=$('#ReturnSalesSun').val();
               var ReturnSalesMon=$('#ReturnSalesMon').val();
               var ReturnSalesTue=$('#ReturnSalesTue').val();
               var ReturnSalesWed=$('#ReturnSalesWed').val();
               var ReturnSalesThr=$('#ReturnSalesThr').val();
               var ReturnSalesFri=$('#ReturnSalesFri').val();

               var ReturnSalesPrevY1=$('#ReturnSalesPrevY1').val();
               var ReturnSalesPrevY2=$('#ReturnSalesPrevY2').val();
               var ReturnSalesPrevY3=$('#ReturnSalesPrevY3').val();
               var ReturnSalesPrevY4=$('#ReturnSalesPrevY4').val();
               var ReturnSalesPrevYC=$('#ReturnSalesPrevYC').val();

               var YearName1=$('#YearName1').val();
               var YearName2=$('#YearName2').val();
               var YearName3=$('#YearName3').val();
               var YearName4=$('#YearName4').val();
               var YearNameC=$('#YearNameC').val();


       var dataSales = [
                [1196463600000, ReturnSalesJan],
                [1196550000000, ReturnSalesFeb],
                [1196636400000, ReturnSalesMar],
                [1196722800000, ReturnSalesApr],
                [1196809200000, ReturnSalesMay],
                [1196895600000, ReturnSalesJun],
                [1196982000000, ReturnSalesJul],
                [1197068400000, ReturnSalesAug],
                [1197154800000, ReturnSalesSep],
                [1197241200000, ReturnSalesOct],
                [1197327600000, ReturnSalesNov],
                [1197414000000, ReturnSalesDec]

            ];
    var dataSet3 = [
                [0, ReturnSalesPrevY4],
                [1, ReturnSalesPrevY3],
                [2, ReturnSalesPrevY2],
                [3, ReturnSalesPrevY1],
                [4, ReturnSalesPrevYC]

            ];
            var dataSet4 = [
                [0, ReturnSalesPrevY4],
                [1, ReturnSalesPrevY3],
                [2, ReturnSalesPrevY2],
                [3, ReturnSalesPrevY1],
                [4, ReturnSalesPrevYC]
            ];
      $(document).ready(function()
            {

             /* sales chart */
                var plotSales = $.plot($('#flot-sales'), [
                {
                    data: dataSales,
                }],
                {
                    series:
                    {
                        lines:
                        {
                            show: true,
                            lineWidth: 1,
                            fill: true,
                            fillColor:
                            {
                                colors: [
                                {
                                    opacity: 0.1
                                },
                                {
                                    opacity: 0.15
                                }]
                            }
                        },
                        points:
                        {
                            show: true
                        },
                        shadowSize: 0
                    },
                    selection:
                    {
                        mode: "x"
                    },
                            grid:
                        {
                            hoverable: true,
                            clickable: true,
                            tickColor: 'rgba(0,0,0,0.05)',
                            borderWidth: 1,
                            borderColor: 'rgba(0,0,0,0.05)'
                        },
                        tooltip: {
        show: true,
        content: "Total: %y"
   },
                        tooltipOpts:
                        {
                            cssClass: 'tooltip-inner',
                            defaultTheme: false
                        },
                    colors: [color.primary._500],
                    xaxis:
                    {
                        mode: "time",
                           ticks: [
                [1196463600000, 'Jan'],
                [1196550000000, 'Feb'],
                [1196636400000, 'March'],
                [1196722800000, 'April'],
                [1196809200000, 'May'],
                [1196895600000, 'June'],
                [1196982000000, 'Jul'],
                [1197068400000, 'Aug'],
                [1197154800000, 'Sep'],
                [1197241200000, 'Oct'],
                [1197327600000, 'Nov'],
                [1197414000000, 'Desc'],
            ]
                    }
                });
                /* sales chart -- end */


                  /* flot bar */
                var flotBar = $.plot("#flot-bar", [
                {
                    data: [
                        [0, ReturnSalesSat],
                        [2, ReturnSalesSun],
                        [4, ReturnSalesMon],
                        [6, ReturnSalesTue],
                        [8, ReturnSalesWed],
                        [10, ReturnSalesThr],
                        [12, ReturnSalesFri],

                    ]
                }],
                {
                    series:
                    {
                        bars:
                        {
                            show: true,
                            lineWidth: 0,
                            fillColor: color.fusion._200
                        }
                    },
                            grid:
                        {
                            hoverable: true,
                            clickable: true,
                            tickColor: 'rgba(0,0,0,0.05)',
                            borderWidth: 1,
                            borderColor: 'rgba(0,0,0,0.05)'
                        },
                        tooltip: {
        show: true,
        content: "Total: %y"
   },
                        tooltipOpts:
                        {
                            cssClass: 'tooltip-inner',
                            defaultTheme: false
                        },
                    yaxis:
                    {

                    },
                    xaxis:
                    {
                        tickColor: '#eee',
                        font:
                        {
                            color: '#999',
                            size: 10
                        },
                            ticks: [
                            [0, 'Sat'],
                            [2, 'Sun'],
                            [4, 'Mon'],
                            [6, 'Tue'],
                            [8, 'Wed'],
                            [10, 'Thr'],
                            [12, 'Fri']

                        ],
                    }
                });
                /* flot bar lines -- end */


           /* flot lines tooltip */
                var flotLineAlt = $.plot($('#flot-line-alt'), [
                {
                    data: dataSet3,
                    label: 'Yearly',
                    color: color.danger._500
                },
                {
                    data: dataSet4,
                    label: 'Yearly',
                    color: color.success._500
                }],
                {
                    series:
                    {
                        lines:
                        {
                            show: true,
                            lineWidth: 1
                        },
                        shadowSize: 0
                    },
                    points:
                    {
                        show: true,
                    },
                    legend:
                    {
                        noColumns: 1,
                        position: 'nw'
                    },
                             grid:
                        {
                            hoverable: true,
                            clickable: true,
                            tickColor: 'rgba(0,0,0,0.05)',
                            borderWidth: 1,
                            borderColor: 'rgba(0,0,0,0.05)'
                        },
                        tooltip: {
        show: true,
        content: "Total: %y"
   },
                        tooltipOpts:
                        {
                            cssClass: 'tooltip-inner',
                            defaultTheme: false
                        },
                    yaxis:
                    {

                    },
                    xaxis:
                    {
                        color: '#eee',
                        font:
                        {
                            size: 10,
                            color: '#999'
                        },
                           ticks: [
                            [0, YearName4],
                            [1, YearName3],
                            [2, YearName2],
                            [3, YearName1],
                            [4, YearNameC]

                        ],

                    }
                });
                /* flot lines tooltip -- end */
  });

</script>

<script>
                       /* Purchases Half */
            var extreamResponsiveReturnSales = function()
            {

               var ReturnSalesJan=$('#ReturnSalesJan').val();
               var ReturnSalesFeb=$('#ReturnSalesFeb').val();
               var ReturnSalesMar=$('#ReturnSalesMar').val();
               var ReturnSalesApr=$('#ReturnSalesApr').val();
               var ReturnSalesMay=$('#ReturnSalesMay').val();
               var ReturnSalesJun=$('#ReturnSalesJun').val();
               var ReturnSalesJul=$('#ReturnSalesJul').val();
               var ReturnSalesAug=$('#ReturnSalesAug').val();
               var ReturnSalesSep=$('#ReturnSalesSep').val();
               var ReturnSalesOct=$('#ReturnSalesOct').val();
               var ReturnSalesNov=$('#ReturnSalesNov').val();
               var ReturnSalesDec=$('#ReturnSalesDec').val();
                new Chartist.Bar('#extreamResponsive-ReturnSales',
                {
                    labels: ['Half 1', 'Half 2'],
                    series: [
                        [ReturnSalesJan, ReturnSalesJul],
                        [ReturnSalesFeb, ReturnSalesAug],
                        [ReturnSalesMar, ReturnSalesSep],
                        [ReturnSalesApr, ReturnSalesOct],
                        [ReturnSalesMay, ReturnSalesNov],
                        [ReturnSalesJun, ReturnSalesDec]

                    ]
                },
                {
                    // Default mobile configuration
                    stackBars: true,
                    axisX:
                    {
                        labelInterpolationFnc: function(value)
                        {
                            return value.split(/\s+/).map(function(word)
                            {
                                return word[0];
                            }).join('');
                        }

                    },
                    axisY:
                    {

                    }
                }, [
                    // Options override for media > 400px
                    ['screen and (min-width: 400px)',
                    {
                        reverseData: true,
                        horizontalBars: true,
                        axisX:
                        {
                            labelInterpolationFnc: Chartist.noop
                        },
                        axisY:
                        {
                            offset: 60
                        }
                    }],
                    // Options override for media > 800px
                    ['screen and (min-width: 800px)',
                    {
                        stackBars: false,
                        seriesBarDistance: 10
                    }],
                    // Options override for media > 1000px
                    ['screen and (min-width: 1000px)',
                    {
                        reverseData: false,
                        horizontalBars: false,
                        seriesBarDistance: 15
                    }]
                ]);
            }


            /* Sales Quarters */
            var multiLineLabelsReturnSales = function()
            {

        var ReturnSalesJan=$('#ReturnSalesJan').val();
               var ReturnSalesFeb=$('#ReturnSalesFeb').val();
               var ReturnSalesMar=$('#ReturnSalesMar').val();
               var ReturnSalesApr=$('#ReturnSalesApr').val();
               var ReturnSalesMay=$('#ReturnSalesMay').val();
               var ReturnSalesJun=$('#ReturnSalesJun').val();
               var ReturnSalesJul=$('#ReturnSalesJul').val();
               var ReturnSalesAug=$('#ReturnSalesAug').val();
               var ReturnSalesSep=$('#ReturnSalesSep').val();
               var ReturnSalesOct=$('#ReturnSalesOct').val();
               var ReturnSalesNov=$('#ReturnSalesNov').val();
               var ReturnSalesDec=$('#ReturnSalesDec').val();

                new Chartist.Bar('#multiLineLabels-ReturnSales',
                {
                    labels: ['First quarter of the year', 'Second quarter of the year', 'Third quarter of the year', 'Fourth quarter of the year'],
                    series: [
                        [ReturnSalesJan, ReturnSalesApr, ReturnSalesJul, ReturnSalesOct],
                        [ReturnSalesFeb, ReturnSalesMay, ReturnSalesAug, ReturnSalesNov],
                        [ReturnSalesMar, ReturnSalesJun, ReturnSalesSep, ReturnSalesDec]
                    ]
                },
                {
                    seriesBarDistance: 10,
                    axisX:
                    {

                    },
                    axisY:
                    {
                        scaleMinSpace: 15
                    }


                });
            }


            $(document).ready(function()
            {

   extreamResponsiveReturnSales();
                   multiLineLabelsReturnSales();

            });

</script>



<!-- ReturnPurchases -->
<script>

               var ReturnPurchJan=$('#ReturnPurchJan').val();
               var ReturnPurchFeb=$('#ReturnPurchFeb').val();
               var ReturnPurchMar=$('#ReturnPurchMar').val();
               var ReturnPurchApr=$('#ReturnPurchApr').val();
               var ReturnPurchMay=$('#ReturnPurchMay').val();
               var ReturnPurchJun=$('#ReturnPurchJun').val();
               var ReturnPurchJul=$('#ReturnPurchJul').val();
               var ReturnPurchAug=$('#ReturnPurchAug').val();
               var ReturnPurchSep=$('#ReturnPurchSep').val();
               var ReturnPurchOct=$('#ReturnPurchOct').val();
               var ReturnPurchNov=$('#ReturnPurchNov').val();
               var ReturnPurchDec=$('#ReturnPurchDec').val();

               var ReturnPurchSat=$('#ReturnPurchSat').val();
               var ReturnPurchSun=$('#ReturnPurchSun').val();
               var ReturnPurchMon=$('#ReturnPurchMon').val();
               var ReturnPurchTue=$('#ReturnPurchTue').val();
               var ReturnPurchWed=$('#ReturnPurchWed').val();
               var ReturnPurchThr=$('#ReturnPurchThr').val();
               var ReturnPurchFri=$('#ReturnPurchFri').val();

               var ReturnPurchPrevY1=$('#ReturnPurchPrevY1').val();
               var ReturnPurchPrevY2=$('#ReturnPurchPrevY2').val();
               var ReturnPurchPrevY3=$('#ReturnPurchPrevY3').val();
               var ReturnPurchPrevY4=$('#ReturnPurchPrevY4').val();
               var ReturnPurchPrevYC=$('#ReturnPurchPrevYC').val();

               var YearName1=$('#YearName1').val();
               var YearName2=$('#YearName2').val();
               var YearName3=$('#YearName3').val();
               var YearName4=$('#YearName4').val();
               var YearNameC=$('#YearNameC').val();


       var dataSalesPurch = [
                [1196463600000, ReturnPurchJan],
                [1196550000000, ReturnPurchFeb],
                [1196636400000, ReturnPurchMar],
                [1196722800000, ReturnPurchApr],
                [1196809200000, ReturnPurchMay],
                [1196895600000, ReturnPurchJun],
                [1196982000000, ReturnPurchJul],
                [1197068400000, ReturnPurchAug],
                [1197154800000, ReturnPurchSep],
                [1197241200000, ReturnPurchOct],
                [1197327600000, ReturnPurchNov],
                [1197414000000, ReturnPurchDec]

            ];
    var dataSet3Purch = [
                [0, ReturnPurchPrevY4],
                [1, ReturnPurchPrevY3],
                [2, ReturnPurchPrevY2],
                [3, ReturnPurchPrevY1],
                [4, ReturnPurchPrevYC]

            ];
            var dataSet4Purch = [
                [0, ReturnPurchPrevY4],
                [1, ReturnPurchPrevY3],
                [2, ReturnPurchPrevY2],
                [3, ReturnPurchPrevY1],
                [4, ReturnPurchPrevYC]
            ];
      $(document).ready(function()
            {

             /* sales chart */
                var plotSales = $.plot($('#flot-sales-purch'), [
                {
                    data: dataSalesPurch,
                }],
                {
                    series:
                    {
                        lines:
                        {
                            show: true,
                            lineWidth: 1,
                            fill: true,
                            fillColor:
                            {
                                colors: [
                                {
                                    opacity: 0.1
                                },
                                {
                                    opacity: 0.15
                                }]
                            }
                        },
                        points:
                        {
                            show: true
                        },
                        shadowSize: 0
                    },
                    selection:
                    {
                        mode: "x"
                    },
                            grid:
                        {
                            hoverable: true,
                            clickable: true,
                            tickColor: 'rgba(0,0,0,0.05)',
                            borderWidth: 1,
                            borderColor: 'rgba(0,0,0,0.05)'
                        },
                        tooltip: {
        show: true,
        content: "Total: %y"
   },
                        tooltipOpts:
                        {
                            cssClass: 'tooltip-inner',
                            defaultTheme: false
                        },
                    colors: [color.primary._500],
                    xaxis:
                    {
                        mode: "time",
                           ticks: [
                [1196463600000, 'Jan'],
                [1196550000000, 'Feb'],
                [1196636400000, 'March'],
                [1196722800000, 'April'],
                [1196809200000, 'May'],
                [1196895600000, 'June'],
                [1196982000000, 'Jul'],
                [1197068400000, 'Aug'],
                [1197154800000, 'Sep'],
                [1197241200000, 'Oct'],
                [1197327600000, 'Nov'],
                [1197414000000, 'Desc'],
            ]
                    }
                });
                /* sales chart -- end */


                  /* flot bar */
                var flotBar = $.plot("#flot-bar-purch", [
                {
                    data: [
                        [0, ReturnPurchSat],
                        [2, ReturnPurchSun],
                        [4, ReturnPurchMon],
                        [6, ReturnPurchTue],
                        [8, ReturnPurchWed],
                        [10, ReturnPurchThr],
                        [12, ReturnPurchFri],

                    ]
                }],
                {
                    series:
                    {
                        bars:
                        {
                            show: true,
                            lineWidth: 0,
                            fillColor: color.fusion._200
                        }
                    },
                            grid:
                        {
                            hoverable: true,
                            clickable: true,
                            tickColor: 'rgba(0,0,0,0.05)',
                            borderWidth: 1,
                            borderColor: 'rgba(0,0,0,0.05)'
                        },
                        tooltip: {
        show: true,
        content: "Total: %y"
   },
                        tooltipOpts:
                        {
                            cssClass: 'tooltip-inner',
                            defaultTheme: false
                        },
                    yaxis:
                    {

                    },
                    xaxis:
                    {
                        tickColor: '#eee',
                        font:
                        {
                            color: '#999',
                            size: 10
                        },
                            ticks: [
                            [0, 'Sat'],
                            [2, 'Sun'],
                            [4, 'Mon'],
                            [6, 'Tue'],
                            [8, 'Wed'],
                            [10, 'Thr'],
                            [12, 'Fri']

                        ],
                    }
                });
                /* flot bar lines -- end */


           /* flot lines tooltip */
                var flotLineAlt = $.plot($('#flot-line-alt-purch'), [
                {
                    data: dataSet3Purch,
                    label: 'Yearly',
                    color: color.danger._500
                },
                {
                    data: dataSet4Purch,
                    label: 'Yearly',
                    color: color.success._500
                }],
                {
                    series:
                    {
                        lines:
                        {
                            show: true,
                            lineWidth: 1
                        },
                        shadowSize: 0
                    },
                    points:
                    {
                        show: true,
                    },
                    legend:
                    {
                        noColumns: 1,
                        position: 'nw'
                    },
                             grid:
                        {
                            hoverable: true,
                            clickable: true,
                            tickColor: 'rgba(0,0,0,0.05)',
                            borderWidth: 1,
                            borderColor: 'rgba(0,0,0,0.05)'
                        },
                        tooltip: {
        show: true,
        content: "Total: %y"
   },
                        tooltipOpts:
                        {
                            cssClass: 'tooltip-inner',
                            defaultTheme: false
                        },
                    yaxis:
                    {

                    },
                    xaxis:
                    {
                        color: '#eee',
                        font:
                        {
                            size: 10,
                            color: '#999'
                        },
                           ticks: [
                            [0, YearName4],
                            [1, YearName3],
                            [2, YearName2],
                            [3, YearName1],
                            [4, YearNameC]

                        ],

                    }
                });
                /* flot lines tooltip -- end */
  });

</script>

<script>
                       /* Purchases Half */
            var extreamResponsivePurch = function()
            {

               var ReturnPurchJan=$('#ReturnPurchJan').val();
               var ReturnPurchFeb=$('#ReturnPurchFeb').val();
               var ReturnPurchMar=$('#ReturnPurchMar').val();
               var ReturnPurchApr=$('#ReturnPurchApr').val();
               var ReturnPurchMay=$('#ReturnPurchMay').val();
               var ReturnPurchJun=$('#ReturnPurchJun').val();
               var ReturnPurchJul=$('#ReturnPurchJul').val();
               var ReturnPurchAug=$('#ReturnPurchAug').val();
               var ReturnPurchSep=$('#ReturnPurchSep').val();
               var ReturnPurchOct=$('#ReturnPurchOct').val();
               var ReturnPurchNov=$('#ReturnPurchNov').val();
               var ReturnPurchDec=$('#ReturnPurchDec').val();
                new Chartist.Bar('#extreamResponsive-Purch',
                {
                    labels: ['Half 1', 'Half 2'],
                    series: [
                        [ReturnPurchJan, ReturnPurchJul],
                        [ReturnPurchFeb, ReturnPurchAug],
                        [ReturnPurchMar, ReturnPurchSep],
                        [ReturnPurchApr, ReturnPurchOct],
                        [ReturnPurchMay, ReturnPurchNov],
                        [ReturnPurchJun, ReturnPurchDec]

                    ]
                },
                {
                    // Default mobile configuration
                    stackBars: true,
                    axisX:
                    {
                        labelInterpolationFnc: function(value)
                        {
                            return value.split(/\s+/).map(function(word)
                            {
                                return word[0];
                            }).join('');
                        }

                    },
                    axisY:
                    {

                    }
                }, [
                    // Options override for media > 400px
                    ['screen and (min-width: 400px)',
                    {
                        reverseData: true,
                        horizontalBars: true,
                        axisX:
                        {
                            labelInterpolationFnc: Chartist.noop
                        },
                        axisY:
                        {
                            offset: 60
                        }
                    }],
                    // Options override for media > 800px
                    ['screen and (min-width: 800px)',
                    {
                        stackBars: false,
                        seriesBarDistance: 10
                    }],
                    // Options override for media > 1000px
                    ['screen and (min-width: 1000px)',
                    {
                        reverseData: false,
                        horizontalBars: false,
                        seriesBarDistance: 15
                    }]
                ]);
            }


            /* Sales Quarters */
            var multiLineLabelsPurch = function()
            {

            var ReturnPurchJan=$('#ReturnPurchJan').val();
               var ReturnPurchFeb=$('#ReturnPurchFeb').val();
               var ReturnPurchMar=$('#ReturnPurchMar').val();
               var ReturnPurchApr=$('#ReturnPurchApr').val();
               var ReturnPurchMay=$('#ReturnPurchMay').val();
               var ReturnPurchJun=$('#ReturnPurchJun').val();
               var ReturnPurchJul=$('#ReturnPurchJul').val();
               var ReturnPurchAug=$('#ReturnPurchAug').val();
               var ReturnPurchSep=$('#ReturnPurchSep').val();
               var ReturnPurchOct=$('#ReturnPurchOct').val();
               var ReturnPurchNov=$('#ReturnPurchNov').val();
               var ReturnPurchDec=$('#ReturnPurchDec').val();

                new Chartist.Bar('#multiLineLabels-Purch',
                {
                    labels: ['First quarter of the year', 'Second quarter of the year', 'Third quarter of the year', 'Fourth quarter of the year'],
                    series: [
                        [ReturnPurchJan, ReturnPurchApr, ReturnPurchJul, ReturnPurchOct],
                        [ReturnPurchFeb, ReturnPurchMay, ReturnPurchAug, ReturnPurchNov],
                        [ReturnPurchMar, ReturnPurchJun, ReturnPurchSep, ReturnPurchDec]
                    ]
                },
                {
                    seriesBarDistance: 10,
                    axisX:
                    {

                    },
                    axisY:
                    {
                        scaleMinSpace: 15
                    }


                });
            }


            $(document).ready(function()
            {

   extreamResponsivePurch();
                   multiLineLabelsPurch();

            });

</script>


<!-- VS -->
<script>

                var colors = [color.success._500, color.danger._500, color.info._500, color.primary._500, color.warning._500];
   var ReturnPurchJan=$('#ReturnPurchJan').val();
               var ReturnPurchFeb=$('#ReturnPurchFeb').val();
               var ReturnPurchMar=$('#ReturnPurchMar').val();
               var ReturnPurchApr=$('#ReturnPurchApr').val();
               var ReturnPurchMay=$('#ReturnPurchMay').val();
               var ReturnPurchJun=$('#ReturnPurchJun').val();
               var ReturnPurchJul=$('#ReturnPurchJul').val();
               var ReturnPurchAug=$('#ReturnPurchAug').val();
               var ReturnPurchSep=$('#ReturnPurchSep').val();
               var ReturnPurchOct=$('#ReturnPurchOct').val();
               var ReturnPurchNov=$('#ReturnPurchNov').val();
               var ReturnPurchDec=$('#ReturnPurchDec').val();


                   var ReturnSalesJan=$('#ReturnSalesJan').val();
               var ReturnSalesFeb=$('#ReturnSalesFeb').val();
               var ReturnSalesMar=$('#ReturnSalesMar').val();
               var ReturnSalesApr=$('#ReturnSalesApr').val();
               var ReturnSalesMay=$('#ReturnSalesMay').val();
               var ReturnSalesJun=$('#ReturnSalesJun').val();
               var ReturnSalesJul=$('#ReturnSalesJul').val();
               var ReturnSalesAug=$('#ReturnSalesAug').val();
               var ReturnSalesSep=$('#ReturnSalesSep').val();
               var ReturnSalesOct=$('#ReturnSalesOct').val();
               var ReturnSalesNov=$('#ReturnSalesNov').val();
               var ReturnSalesDec=$('#ReturnSalesDec').val();


              var PurchJan=$('#PurchJan').val();
               var PurchFeb=$('#PurchFeb').val();
               var PurchMar=$('#PurchMar').val();
               var PurchApr=$('#PurchApr').val();
               var PurchMay=$('#PurchMay').val();
               var PurchJun=$('#PurchJun').val();
               var PurchJul=$('#PurchJul').val();
               var PurchAug=$('#PurchAug').val();
               var PurchSep=$('#PurchSep').val();
               var PurchOct=$('#PurchOct').val();
               var PurchNov=$('#PurchNov').val();
               var PurchDec=$('#PurchDec').val();


          var SalesJan=$('#SalesJan').val();
               var SalesFeb=$('#SalesFeb').val();
               var SalesMar=$('#SalesMar').val();
               var SalesApr=$('#SalesApr').val();
               var SalesMay=$('#SalesMay').val();
               var SalesJun=$('#SalesJun').val();
               var SalesJul=$('#SalesJul').val();
               var SalesAug=$('#SalesAug').val();
               var SalesSep=$('#SalesSep').val();
               var SalesOct=$('#SalesOct').val();
               var SalesNov=$('#SalesNov').val();
               var SalesDec=$('#SalesDec').val();


     var splilneLine = c3.generate(
            {
                bindto: "#splilneLine",

                data:
                {

                      x: 'x',
                    columns: [
                        ['x','Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Desc'],
                        ['Sales', SalesJan, SalesFeb, SalesMar, SalesApr, SalesMay, SalesJun, SalesJul, SalesAug, SalesSep, SalesOct, SalesNov, SalesDec],
                        ['Return Sales', ReturnSalesJan, ReturnSalesFeb, ReturnSalesMar, ReturnSalesApr, ReturnSalesMay, ReturnSalesJun, ReturnSalesJul, ReturnSalesAug, ReturnSalesSep, ReturnSalesOct, ReturnSalesNov, ReturnSalesDec]
                    ],
                    type: 'spline'
                },
                color:
                {
                    pattern: colors
                },
                     axis:
                {
                    x:
                    {
                       type: 'category',

                          x:['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Desc'],

                    }
                }
            });

         var splilneLinePurch = c3.generate(
            {
                bindto: "#splilneLinePurch",
                data:
                {
                          x: 'x',
                    columns: [
                              ['x','Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Desc'],
                        ['Purchases', PurchJan, PurchFeb, PurchMar, PurchApr, PurchMay, PurchJun, PurchJul, PurchAug, PurchSep, PurchOct, PurchNov, PurchDec],
                        ['Return Purchases', ReturnPurchJan, ReturnPurchFeb, ReturnPurchMar, ReturnPurchApr, ReturnPurchMay, ReturnPurchJun, ReturnPurchJul, ReturnPurchAug, ReturnPurchSep, ReturnPurchOct, ReturnPurchNov, ReturnPurchDec]
                    ],
                    type: 'spline'
                },
                color:
                {
                    pattern: colors
                },
                         axis:
                {
                    x:
                    {
                       type: 'category',

                          x:['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Desc'],

                    }
                }
            });


             var barChart = c3.generate(
            {


                bindto: "#barChart",
                data:
                {
                         x: 'x',
                    columns: [
                    ['x','Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Desc'],
                        ['Return Sales', ReturnSalesJan, ReturnSalesFeb, ReturnSalesMar, ReturnSalesApr, ReturnSalesMay, ReturnSalesJun, ReturnSalesJul, ReturnSalesAug, ReturnSalesSep, ReturnSalesOct, ReturnSalesNov, ReturnSalesDec],
                        ['Return Purchases', ReturnPurchJan, ReturnPurchFeb, ReturnPurchMar, ReturnPurchApr, ReturnPurchMay, ReturnPurchJun, ReturnPurchJul, ReturnPurchAug, ReturnPurchSep, ReturnPurchOct, ReturnPurchNov, ReturnPurchDec]
                    ],
                    type: 'bar'
                },
                color:
                {
                    pattern: colors
                },
                bar:
                {
                    width:
                    {
                        ratio: 0.8 // this makes bar width 50% of length between ticks
                    }
                    // or
                    //width: 100 // this makes bar width 100px
                },
                  axis:
                {
                    x:
                    {
                       type: 'category',

                          x:['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Desc'],

                    }
                }

            });






</script>

<!-- Purch VS Sales -->
<script>
                      /* overlap bar mobile */
            var overlapBarMobile = function()
            {

                 var SalesJan=$('#SalesJan').val();
               var SalesFeb=$('#SalesFeb').val();
               var SalesMar=$('#SalesMar').val();
               var SalesApr=$('#SalesApr').val();
               var SalesMay=$('#SalesMay').val();
               var SalesJun=$('#SalesJun').val();
               var SalesJul=$('#SalesJul').val();
               var SalesAug=$('#SalesAug').val();
               var SalesSep=$('#SalesSep').val();
               var SalesOct=$('#SalesOct').val();
               var SalesNov=$('#SalesNov').val();
               var SalesDec=$('#SalesDec').val();

            var PurchJan=$('#PurchJan').val();
               var PurchFeb=$('#PurchFeb').val();
               var PurchMar=$('#PurchMar').val();
               var PurchApr=$('#PurchApr').val();
               var PurchMay=$('#PurchMay').val();
               var PurchJun=$('#PurchJun').val();
               var PurchJul=$('#PurchJul').val();
               var PurchAug=$('#PurchAug').val();
               var PurchSep=$('#PurchSep').val();
               var PurchOct=$('#PurchOct').val();
               var PurchNov=$('#PurchNov').val();
               var PurchDec=$('#PurchDec').val();



                var data = {
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                    series: [
                        [SalesJan, SalesFeb, SalesMar, SalesApr, SalesMay, SalesJun, SalesJul, SalesAug, SalesSep, SalesOct, SalesNov, SalesDec],
                        [PurchJan, PurchFeb, PurchMar, PurchApr, PurchMay, PurchJun, PurchJul, PurchAug, PurchSep, PurchOct, PurchNov, PurchDec]
                    ]
                };

                var options = {
                    seriesBarDistance: 10
                };

                var responsiveOptions = [
                    ['screen and (max-width: 640px)',
                    {

                        axisX:
                        {

                        }
                    }]
                ];

                new Chartist.Bar('#overlapBarMobile', data, options, responsiveOptions);
            }
            /* overlap bar mobile -- end */


            $(document).ready(function()
            {
      overlapBarMobile();
   });
</script>



<!-- GBB Salees -->
<script>

              var colors = [color.success._500, color.danger._500, color.info._500, color.primary._500, color.warning._500];

        var names = $("input[name='pname[]']")
              .map(function(){return $(this).val();}).get();


        var values = $("input[name='pVal[]']")
              .map(function(){return $(this).val();}).get();


var total=parseFloat(values[0]) + parseFloat(values[1]) + parseFloat(values[2]) + parseFloat(values[3]) + parseFloat(values[4]);
    var one= parseFloat(values[0]) / parseFloat(total);
    var two= parseFloat(values[1]) / parseFloat(total);
    var three= parseFloat(values[2]) / parseFloat(total);
    var four= parseFloat(values[3]) / parseFloat(total);
    var five= parseFloat(values[4]) / parseFloat(total);



            var namesB = $("input[name='bname[]']")
              .map(function(){return $(this).val();}).get();


        var valuesB = $("input[name='bVal[]']")
              .map(function(){return $(this).val();}).get();


var totalB=parseFloat(valuesB[0]) + parseFloat(valuesB[1]) + parseFloat(valuesB[2]) + parseFloat(valuesB[3]) + parseFloat(valuesB[4]);
    var oneB= parseFloat(valuesB[0]) / parseFloat(totalB);
    var twoB= parseFloat(valuesB[1]) / parseFloat(totalB);
    var threeB= parseFloat(valuesB[2]) / parseFloat(totalB);
    var fourB= parseFloat(valuesB[3]) / parseFloat(totalB);
    var fiveB= parseFloat(valuesB[4]) / parseFloat(totalB);


                    var pieChart = c3.generate(
            {

                bindto: "#pieChart",
                data:
                {

                    columns: [

                         [names[0],one],
                         [names[1],two],
                         [names[2],three],
                         [names[3],four],
                         [names[4],five]

                    ],
                    type: 'pie' //,

                },
                color:
                {
                    pattern: colors
                }
            });


                  var pieChartB = c3.generate(
            {

                bindto: "#pieChartB",
                data:
                {

                    columns: [

                         [namesB[0],oneB],
                         [namesB[1],twoB],
                         [namesB[2],threeB],
                         [namesB[3],fourB],
                         [namesB[4],fiveB]

                    ],
                    type: 'pie' //,

                },
                color:
                {
                    pattern: colors
                }
            });



            var pieChartUnload = function()
            {
                $("#pieChartUnload").attr("disabled", true);
                $("#pieChartUnload").text("unloading datasets...")
                setTimeout(function()
                {
                    pieChart.unload(
                    {
                        ids: names[0]
                    });
                    pieChart.unload(
                    {
                        ids: names[1]
                    });
                    pieChart.unload(
                    {
                        ids: names[2]
                    });
                    pieChart.unload(
                    {
                        ids: names[3]
                    });
                    pieChart.unload(
                    {
                        ids: names[4]
                    });

                }, 1000);
                setTimeout(function()
                {
                    $("#pieChartUnload").text("unload complete")
                }, 2000);
            };


        var pieChartUnloadB = function()
            {
                $("#pieChartUnloadB").attr("disabled", true);
                $("#pieChartUnloadB").text("unloading datasets...")
                setTimeout(function()
                {
                    pieChart.unload(
                    {
                        ids: namesB[0]
                    });
                    pieChart.unload(
                    {
                        ids: namesB[1]
                    });
                    pieChart.unload(
                    {
                        ids: namesB[2]
                    });
                    pieChart.unload(
                    {
                        ids: namesB[3]
                    });
                    pieChart.unload(
                    {
                        ids: namesB[4]
                    });

                }, 1000);
                setTimeout(function()
                {
                    $("#pieChartUnloadB").text("unload complete")
                }, 2000);
            };


</script>


<!-- Expenses -->
<script>
          /* Monthly area */
            var lineChartArea = function()
            {
                   var EgmalyMasrofatJan=$('#EgmalyMasrofatJan').val();
               var EgmalyMasrofatFeb=$('#EgmalyMasrofatFeb').val();
               var EgmalyMasrofatMar=$('#EgmalyMasrofatMar').val();
               var EgmalyMasrofatApr=$('#EgmalyMasrofatApr').val();
               var EgmalyMasrofatMay=$('#EgmalyMasrofatMay').val();
               var EgmalyMasrofatJun=$('#EgmalyMasrofatJun').val();
               var EgmalyMasrofatJul=$('#EgmalyMasrofatJul').val();
               var EgmalyMasrofatAug=$('#EgmalyMasrofatAug').val();
               var EgmalyMasrofatSep=$('#EgmalyMasrofatSep').val();
               var EgmalyMasrofatOct=$('#EgmalyMasrofatOct').val();
               var EgmalyMasrofatNov=$('#EgmalyMasrofatNov').val();
               var EgmalyMasrofatDec=$('#EgmalyMasrofatDec').val();

                new Chartist.Line('#lineChartArea',
                {
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug','Sep','Oct','Nov','Dec'],
                    series: [
                        [EgmalyMasrofatJan, EgmalyMasrofatFeb, EgmalyMasrofatMar, EgmalyMasrofatApr, EgmalyMasrofatMay, EgmalyMasrofatJun, EgmalyMasrofatJul, EgmalyMasrofatAug,EgmalyMasrofatSep,EgmalyMasrofatOct,EgmalyMasrofatNov,EgmalyMasrofatDec ]
                    ]
                },
                {
                    low: 0,
                    showArea: true,
                    fullWidth: true
                });
            }
            /* line chart area -- end */

                   /* Weekly events */
            var usingEvents = function()
            {
                      var EgmalyMasrofatSat=$('#EgmalyMasrofatSat').val();
               var EgmalyMasrofatSun=$('#EgmalyMasrofatSun').val();
               var EgmalyMasrofatMon=$('#EgmalyMasrofatMon').val();
               var EgmalyMasrofatTue=$('#EgmalyMasrofatTue').val();
               var EgmalyMasrofatWed=$('#EgmalyMasrofatWed').val();
               var EgmalyMasrofatThr=$('#EgmalyMasrofatThr').val();
               var EgmalyMasrofatFri=$('#EgmalyMasrofatFri').val();

                var chart = new Chartist.Line('#usingEvents',
                {
                    labels: ['Sat', 'Sun', 'Mon', 'Tue', 'Wed','Thr','Fri'],
                    series: [
                        [EgmalyMasrofatSat, EgmalyMasrofatSun, EgmalyMasrofatMon, EgmalyMasrofatTue, EgmalyMasrofatWed,EgmalyMasrofatThr,EgmalyMasrofatFri]
                    ]
                });

                // Listening for draw events that get emitted by the Chartist chart
                chart.on('draw', function(data)
                {
                    // If the draw event was triggered from drawing a point on the line chart
                    if (data.type === 'point')
                    {
                        // We are creating a new path SVG element that draws a triangle around the point coordinates
                        var triangle = new Chartist.Svg('path',
                        {
                            d: ['M',
                                data.x,
                                data.y - 15,
                                'L',
                                data.x - 15,
                                data.y + 8,
                                'L',
                                data.x + 15,
                                data.y + 8,
                                'z'
                            ].join(' '),
                            style: 'fill-opacity: 1'
                        }, 'ct-area');

                        // With data.element we get the Chartist SVG wrapper and we can replace the original point drawn by Chartist with our newly created triangle
                        data.element.replace(triangle);
                    }
                });
            }
            /* using events -- end */


                  /* Yearly events */
            var usingEventsYearly = function()
            {
                    var EgmalyMasrofatY1=$('#EgmalyMasrofatY1').val();
               var EgmalyMasrofatY2=$('#EgmalyMasrofatY2').val();
               var EgmalyMasrofatY3=$('#EgmalyMasrofatY3').val();
               var EgmalyMasrofatY4=$('#EgmalyMasrofatY4').val();
               var EgmalyMasrofatCY=$('#EgmalyMasrofatCY').val();

               var YearName1=$('#YearName1').val();
               var YearName2=$('#YearName2').val();
               var YearName3=$('#YearName3').val();
               var YearName4=$('#YearName4').val();
               var YearNameC=$('#YearNameC').val();
                var chart = new Chartist.Line('#usingEventsYearly',
                {
                    labels: [YearName4, YearName3, YearName2, YearName1, YearNameC],
                    series: [
                        [EgmalyMasrofatY4, EgmalyMasrofatY3, EgmalyMasrofatY2, EgmalyMasrofatY1, EgmalyMasrofatCY]
                    ]
                });



                // Listening for draw events that get emitted by the Chartist chart
                chart.on('draw', function(data)
                {
                    // If the draw event was triggered from drawing a point on the line chart
                    if (data.type === 'point')
                    {
                        // We are creating a new path SVG element that draws a triangle around the point coordinates
                        var triangle = new Chartist.Svg('path',
                        {
                            d: ['M',
                                data.x,
                                data.y - 15,
                                'L',
                                data.x - 15,
                                data.y + 8,
                                'L',
                                data.x + 15,
                                data.y + 8,
                                'z'
                            ].join(' '),
                            style: 'fill-opacity: 1'
                        }, 'ct-area');

                        // With data.element we get the Chartist SVG wrapper and we can replace the original point drawn by Chartist with our newly created triangle
                        data.element.replace(triangle);
                    }
                });
            }
            /* using events -- end */


           $(document).ready(function()
            {
                lineChartArea();
                  usingEvents();
                  usingEventsYearly();

            });
</script>
<script>
                       /* Expenses Half */
            var extreamResponsiveExpenses = function()
            {

     var EgmalyMasrofatJan=$('#EgmalyMasrofatJan').val();
               var EgmalyMasrofatFeb=$('#EgmalyMasrofatFeb').val();
               var EgmalyMasrofatMar=$('#EgmalyMasrofatMar').val();
               var EgmalyMasrofatApr=$('#EgmalyMasrofatApr').val();
               var EgmalyMasrofatMay=$('#EgmalyMasrofatMay').val();
               var EgmalyMasrofatJun=$('#EgmalyMasrofatJun').val();
               var EgmalyMasrofatJul=$('#EgmalyMasrofatJul').val();
               var EgmalyMasrofatAug=$('#EgmalyMasrofatAug').val();
               var EgmalyMasrofatSep=$('#EgmalyMasrofatSep').val();
               var EgmalyMasrofatOct=$('#EgmalyMasrofatOct').val();
               var EgmalyMasrofatNov=$('#EgmalyMasrofatNov').val();
               var EgmalyMasrofatDec=$('#EgmalyMasrofatDec').val();
                new Chartist.Bar('#extreamResponsive-Expenses',
                {
                    labels: ['Half 1', 'Half 2'],
                    series: [
                        [EgmalyMasrofatJan, EgmalyMasrofatJul],
                        [EgmalyMasrofatFeb, EgmalyMasrofatAug],
                        [EgmalyMasrofatMar, EgmalyMasrofatSep],
                        [EgmalyMasrofatApr, EgmalyMasrofatOct],
                        [EgmalyMasrofatMay, EgmalyMasrofatNov],
                        [EgmalyMasrofatJun, EgmalyMasrofatDec]

                    ]
                },
                {
                    // Default mobile configuration
                    stackBars: true,
                    axisX:
                    {
                        labelInterpolationFnc: function(value)
                        {
                            return value.split(/\s+/).map(function(word)
                            {
                                return word[0];
                            }).join('');
                        }

                    },
                    axisY:
                    {

                    }
                }, [
                    // Options override for media > 400px
                    ['screen and (min-width: 400px)',
                    {
                        reverseData: true,
                        horizontalBars: true,
                        axisX:
                        {
                            labelInterpolationFnc: Chartist.noop
                        },
                        axisY:
                        {
                            offset: 60
                        }
                    }],
                    // Options override for media > 800px
                    ['screen and (min-width: 800px)',
                    {
                        stackBars: false,
                        seriesBarDistance: 10
                    }],
                    // Options override for media > 1000px
                    ['screen and (min-width: 1000px)',
                    {
                        reverseData: false,
                        horizontalBars: false,
                        seriesBarDistance: 15
                    }]
                ]);
            }


            /* Sales Quarters */
            var multiLineLabelsExpenses = function()
            {

             var EgmalyMasrofatJan=$('#EgmalyMasrofatJan').val();
               var EgmalyMasrofatFeb=$('#EgmalyMasrofatFeb').val();
               var EgmalyMasrofatMar=$('#EgmalyMasrofatMar').val();
               var EgmalyMasrofatApr=$('#EgmalyMasrofatApr').val();
               var EgmalyMasrofatMay=$('#EgmalyMasrofatMay').val();
               var EgmalyMasrofatJun=$('#EgmalyMasrofatJun').val();
               var EgmalyMasrofatJul=$('#EgmalyMasrofatJul').val();
               var EgmalyMasrofatAug=$('#EgmalyMasrofatAug').val();
               var EgmalyMasrofatSep=$('#EgmalyMasrofatSep').val();
               var EgmalyMasrofatOct=$('#EgmalyMasrofatOct').val();
               var EgmalyMasrofatNov=$('#EgmalyMasrofatNov').val();
               var EgmalyMasrofatDec=$('#EgmalyMasrofatDec').val();

                new Chartist.Bar('#multiLineLabels-Expenses',
                {
                    labels: ['First quarter of the year', 'Second quarter of the year', 'Third quarter of the year', 'Fourth quarter of the year'],
                    series: [
                        [EgmalyMasrofatJan, EgmalyMasrofatApr, EgmalyMasrofatJul, EgmalyMasrofatOct],
                        [EgmalyMasrofatFeb, EgmalyMasrofatMay, EgmalyMasrofatAug, EgmalyMasrofatNov],
                        [EgmalyMasrofatMar, EgmalyMasrofatJun, EgmalyMasrofatSep, EgmalyMasrofatDec]
                    ]
                },
                {
                    seriesBarDistance: 10,
                    axisX:
                    {

                    },
                    axisY:
                    {
                        scaleMinSpace: 15
                    }


                });
            }


            $(document).ready(function()
            {

   extreamResponsiveExpenses();
                   multiLineLabelsExpenses();


            });

</script>


<!-- Vouchers -->
<script>



         /* line interpolation */
            var lineInterpolation = function()
            {

                 var PaymentVoucherJan=$('#PaymentVoucherJan').val();
               var PaymentVoucherFeb=$('#PaymentVoucherFeb').val();
               var PaymentVoucherMar=$('#PaymentVoucherMar').val();
               var PaymentVoucherApr=$('#PaymentVoucherApr').val();
               var PaymentVoucherMay=$('#PaymentVoucherMay').val();
               var PaymentVoucherJun=$('#PaymentVoucherJun').val();
               var PaymentVoucherJul=$('#PaymentVoucherJul').val();
               var PaymentVoucherAug=$('#PaymentVoucherAug').val();
               var PaymentVoucherSep=$('#PaymentVoucherSep').val();
               var PaymentVoucherOct=$('#PaymentVoucherOct').val();
               var PaymentVoucherNov=$('#PaymentVoucherNov').val();
               var PaymentVoucherDec=$('#PaymentVoucherDec').val();

                 var ReciptVoucherJan=$('#ReciptVoucherJan').val();
               var ReciptVoucherFeb=$('#ReciptVoucherFeb').val();
               var ReciptVoucherMar=$('#ReciptVoucherMar').val();
               var ReciptVoucherApr=$('#ReciptVoucherApr').val();
               var ReciptVoucherMay=$('#ReciptVoucherMay').val();
               var ReciptVoucherJun=$('#ReciptVoucherJun').val();
               var ReciptVoucherJul=$('#ReciptVoucherJul').val();
               var ReciptVoucherAug=$('#ReciptVoucherAug').val();
               var ReciptVoucherSep=$('#ReciptVoucherSep').val();
               var ReciptVoucherOct=$('#ReciptVoucherOct').val();
               var ReciptVoucherNov=$('#ReciptVoucherNov').val();
               var ReciptVoucherDec=$('#ReciptVoucherDec').val();

                var chart = new Chartist.Line('#lineInterpolation',
                {
                   labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug','Sep','Oct','Nov','Dec'],
                    series: [

                        [PaymentVoucherJan, PaymentVoucherFeb, PaymentVoucherMar, PaymentVoucherApr, PaymentVoucherMay,PaymentVoucherJun,PaymentVoucherJul,PaymentVoucherAug,PaymentVoucherSep,PaymentVoucherOct,PaymentVoucherNov,PaymentVoucherDec],

                        [ReciptVoucherJan, ReciptVoucherFeb, ReciptVoucherMar, ReciptVoucherApr, ReciptVoucherMay,ReciptVoucherJun,ReciptVoucherJul,ReciptVoucherAug,ReciptVoucherSep,ReciptVoucherOct,ReciptVoucherNov,ReciptVoucherDec]

                    ]
                },
                {
                    // Remove this configuration to see that chart rendered with cardinal spline interpolation
                    // Sometimes, on large jumps in data values, it's better to use simple smoothing.
                    lineSmooth: Chartist.Interpolation.simple(
                    {
                        divisor: 2
                    }),
                    fullWidth: true,
                    chartPadding:
                    {
                        right: 20
                    },
                    low: 0
                });
            }
            /* line interpolation -- end */

                        /* distributed series */
            var distributedSeries = function()
            {


                    var PaymentVoucherSat=$('#PaymentVoucherSat').val();
               var PaymentVoucherSun=$('#PaymentVoucherSun').val();
               var PaymentVoucherMon=$('#PaymentVoucherMon').val();
               var PaymentVoucherTue=$('#PaymentVoucherTue').val();
               var PaymentVoucherWed=$('#PaymentVoucherWed').val();
               var PaymentVoucherThr=$('#PaymentVoucherThr').val();
               var PaymentVoucherFri=$('#PaymentVoucherFri').val();


                new Chartist.Bar('#distributedSeries',
                {
                    labels: ['Sat', 'Sun', 'Mon', 'Tue', 'Wed', 'Thr', 'Fri'],
                    series: [PaymentVoucherSat, PaymentVoucherSun, PaymentVoucherMon, PaymentVoucherTue, PaymentVoucherWed, PaymentVoucherThr, PaymentVoucherFri]
                },
                {
                    distributeSeries: true
                });
            }
            /* distributed series -- end */

                         /* distributed series */
            var distributedSeriesRecipt = function()
            {


                    var ReciptVoucherSat=$('#ReciptVoucherSat').val();
               var ReciptVoucherSun=$('#ReciptVoucherSun').val();
               var ReciptVoucherMon=$('#ReciptVoucherMon').val();
               var ReciptVoucherTue=$('#ReciptVoucherTue').val();
               var ReciptVoucherWed=$('#ReciptVoucherWed').val();
               var ReciptVoucherThr=$('#ReciptVoucherThr').val();
               var ReciptVoucherFri=$('#ReciptVoucherFri').val();


                new Chartist.Bar('#distributedSeriesRecipt',
                {
                    labels: ['Sat', 'Sun', 'Mon', 'Tue', 'Wed', 'Thr', 'Fri'],
                    series: [ReciptVoucherSat, ReciptVoucherSun, ReciptVoucherMon, ReciptVoucherTue, ReciptVoucherWed, ReciptVoucherThr, ReciptVoucherFri]
                },
                {
                    distributeSeries: true
                });
            }
            /* distributed series -- end */


                        /* initilize all charts on DOM ready */
            $(document).ready(function()
            {
      lineInterpolation();
                  distributedSeries();
                  distributedSeriesRecipt();

            });

</script>


<!-- Checks -->
<script>

                         /* distributed series */
            var distributedSeriesIncom = function()
            {


                    var IncomChecksSat=$('#IncomChecksSat').val();
               var IncomChecksSun=$('#IncomChecksSun').val();
               var IncomChecksMon=$('#IncomChecksMon').val();
               var IncomChecksTue=$('#IncomChecksTue').val();
               var IncomChecksWed=$('#IncomChecksWed').val();
               var IncomChecksThr=$('#IncomChecksThr').val();
               var IncomChecksFri=$('#IncomChecksFri').val();


                new Chartist.Bar('#distributedSeriesIncom',
                {
                    labels: ['Sat', 'Sun', 'Mon', 'Tue', 'Wed', 'Thr', 'Fri'],
                    series: [IncomChecksSat, IncomChecksSun, IncomChecksMon, IncomChecksTue, IncomChecksWed, IncomChecksThr, IncomChecksFri]
                },
                {
                    distributeSeries: true
                });
            }
            /* distributed series -- end */

                         /* distributed series */
            var distributedSeriesExport = function()
            {


                var ExportChecksSat=$('#ExportChecksSat').val();
               var ExportChecksSun=$('#ExportChecksSun').val();
               var ExportChecksMon=$('#ExportChecksMon').val();
               var ExportChecksTue=$('#ExportChecksTue').val();
               var ExportChecksWed=$('#ExportChecksWed').val();
               var ExportChecksThr=$('#ExportChecksThr').val();
               var ExportChecksFri=$('#ExportChecksFri').val();


                new Chartist.Bar('#distributedSeriesExport',
                {
                    labels: ['Sat', 'Sun', 'Mon', 'Tue', 'Wed', 'Thr', 'Fri'],
                    series: [ExportChecksSat, ExportChecksSun, ExportChecksMon, ExportChecksTue, ExportChecksWed, ExportChecksThr, ExportChecksFri]
                },
                {
                    distributeSeries: true
                });
            }
            /* distributed series -- end */


                        /* initilize all charts on DOM ready */
            $(document).ready(function()
            {

                  distributedSeriesIncom();
                  distributedSeriesExport();

            });


</script>
<script>

           var colors = [color.success._500, color.danger._500, color.info._500, color.primary._500, color.warning._500];


           var IncomChecksJan=$('#IncomChecksJan').val();
               var IncomChecksFeb=$('#IncomChecksFeb').val();
               var IncomChecksMar=$('#IncomChecksMar').val();
               var IncomChecksApr=$('#IncomChecksApr').val();
               var IncomChecksMay=$('#IncomChecksMay').val();
               var IncomChecksJun=$('#IncomChecksJun').val();
               var IncomChecksJul=$('#IncomChecksJul').val();
               var IncomChecksAug=$('#IncomChecksAug').val();
               var IncomChecksSep=$('#IncomChecksSep').val();
               var IncomChecksOct=$('#IncomChecksOct').val();
               var IncomChecksNov=$('#IncomChecksNov').val();
               var IncomChecksDec=$('#IncomChecksDec').val();


      var ExportChecksJan=$('#ExportChecksJan').val();
               var ExportChecksFeb=$('#ExportChecksFeb').val();
               var ExportChecksMar=$('#ExportChecksMar').val();
               var ExportChecksApr=$('#ExportChecksApr').val();
               var ExportChecksMay=$('#ExportChecksMay').val();
               var ExportChecksJun=$('#ExportChecksJun').val();
               var ExportChecksJul=$('#ExportChecksJul').val();
               var ExportChecksAug=$('#ExportChecksAug').val();
               var ExportChecksSep=$('#ExportChecksSep').val();
               var ExportChecksOct=$('#ExportChecksOct').val();
               var ExportChecksNov=$('#ExportChecksNov').val();
               var ExportChecksDec=$('#ExportChecksDec').val();

         var linleRegions = c3.generate(
            {
                bindto: "#linleRegions",
                data:
                {
                    columns: [
                        ['Incom Checks', IncomChecksJan, IncomChecksFeb, IncomChecksMar, IncomChecksApr, IncomChecksMay, IncomChecksJun, IncomChecksJul, IncomChecksAug, IncomChecksSep, IncomChecksOct, IncomChecksNov, IncomChecksDec],
                        ['Export Checks', ExportChecksJan, ExportChecksFeb, ExportChecksMar, ExportChecksApr, ExportChecksMay, ExportChecksJun, ExportChecksJul, ExportChecksAug, ExportChecksSep, ExportChecksOct, ExportChecksNov, ExportChecksDec]

                    ],
                    regions:
                    {
                        'data1': [
                        {
                            'start': 1,
                            'end': 2,
                            'style': 'dashed'
                        },
                        {
                            'start': 3
                        }], // currently 'dashed' style only
                        'data2': [
                        {
                            'end': 3
                        }]
                    }
                },
                color:
                {
                    pattern: colors
                }
            });


</script>


<!-- Profit -->
<script>
          /* bar & line combine */
            var barlineCombine = function()
            {

                                var SafyRab7Jan=$('#SafyRab7Jan').val();
               var SafyRab7Feb=$('#SafyRab7Feb').val();
               var SafyRab7Mar=$('#SafyRab7Mar').val();
               var SafyRab7Apr=$('#SafyRab7Apr').val();
               var SafyRab7May=$('#SafyRab7May').val();
               var SafyRab7Jun=$('#SafyRab7Jun').val();
               var SafyRab7Jul=$('#SafyRab7Jul').val();
               var SafyRab7Aug=$('#SafyRab7Aug').val();
               var SafyRab7Sep=$('#SafyRab7Sep').val();
               var SafyRab7Oct=$('#SafyRab7Oct').val();
               var SafyRab7Nov=$('#SafyRab7Nov').val();
               var SafyRab7Dec=$('#SafyRab7Dec').val();


                var barlineCombineData = {
                    labels: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul","Aug","Sep","Oct","Nov","Dec"],
                    datasets: [
                    {
                        type: 'line',
                        label: 'Pyrmidal',
                        borderColor: color.danger._300,
                        pointBackgroundColor: color.danger._500,
                        pointBorderColor: color.danger._500,
                        pointBorderWidth: 1,
                        borderWidth: 2,
                        pointRadius: 4,
                        pointHoverRadius: 5,
                        fill: false,
                        data: [
                            SafyRab7Jan,
                           SafyRab7Feb,
                           SafyRab7Mar,
                           SafyRab7Apr,
                           SafyRab7May,
                            SafyRab7Jun,
                            SafyRab7Jul,
                            SafyRab7Aug,
                            SafyRab7Sep,
                            SafyRab7Oct,
                            SafyRab7Nov,
                           SafyRab7Dec
                        ]
                    },
                    {
                        type: 'bar',
                        label: 'Graph',
                        backgroundColor: color.success._300,
                        borderColor: color.success._500,
                        data: [
                         SafyRab7Jan,
                           SafyRab7Feb,
                           SafyRab7Mar,
                           SafyRab7Apr,
                           SafyRab7May,
                            SafyRab7Jun,
                            SafyRab7Jul,
                            SafyRab7Aug,
                            SafyRab7Sep,
                            SafyRab7Oct,
                            SafyRab7Nov,
                           SafyRab7Dec
                        ],
                        borderWidth: 1
                    }]

                };
                var config = {
                    type: 'bar',
                    data: barlineCombineData,
                    options:
                    {
                        responsive: true,
                        legend:
                        {
                            position: 'top',
                        },
                        title:
                        {
                            display: true,
                            text: 'Chart.js Bar Chart'
                        },
                        scales:
                        {
                            xAxes: [
                            {
                                display: true,
                                scaleLabel:
                                {
                                    display: false,
                                    labelString: '6 months forecast'
                                },
                                gridLines:
                                {
                                    display: true,
                                    color: "#f2f2f2"
                                },
                                ticks:
                                {
                                    beginAtZero: true,
                                    fontSize: 11
                                }
                            }],
                            yAxes: [
                            {
                                display: true,
                                scaleLabel:
                                {
                                    display: false,
                                    labelString: 'Profit margin (approx)'
                                },
                                gridLines:
                                {
                                    display: true,
                                    color: "#f2f2f2"
                                },
                                ticks:
                                {
                                    beginAtZero: true,
                                    fontSize: 11
                                }
                            }]
                        }
                    }
                }
                new Chart($("#barlineCombine > canvas").get(0).getContext("2d"), config);
            }
            /* bar & line combine -- end */

          /* initilize all charts on DOM ready */
            $(document).ready(function()
            {


                barlineCombine();
     });
</script>



@endpush
