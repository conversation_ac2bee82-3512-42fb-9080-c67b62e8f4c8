<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Stancl\Tenancy\Database\Concerns\CentralConnection;

class PaymentVoucher extends Model
{
    use HasFactory, CentralConnection;
        protected $table = 'payment_vouchers';
      protected $fillable = [
        'Code',
        'Date',
        'Draw',
        'Coin',
        'Cost_Center',
        'Total_Debaitor',
        'Safe',
        'Note',
        'Shift',
        'Store',
        'User',
        'Branch',
           'Status',
          'File',

    ];

        public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }
        public function Cost_Center()
    {
        return $this->belongsTo(CostCenter::class,'Cost_Center');
    }

        public function Safe()
    {
        return $this->belongsTo(AcccountingManual::class,'Safe');
    }

         public function Store()
    {
        return $this->belongsTo(Stores::class,'Store');
    }

            public function User()
    {
        return $this->belongsTo(Employess::class,'User');
    }

            public function PaymentVoucherDetails()
    {
        return $this->hasOne(PaymentVoucherDetails::class);
    }

          public function Branch()
    {
        return $this->belongsTo(Branches::class,'Branch');
    }



}
