<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Stancl\Tenancy\Database\Concerns\CentralConnection;

class UsersMoves extends Model
{
    use HasFactory, CentralConnection;


      protected $table = 'users_moves';
      protected $fillable = [
        'User',
        'Date',
        'Time',
        'Screen',
        'ScreenEn',
        'Type',
        'TypeEn',
        'Explain',
        'ExplainEn',


    ];


       public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }

}
