<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateNotificationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('notifications', function (Blueprint $table) {
            $table->id();
            
            // Basic notification information
            $table->date('Date')->nullable();
            $table->string('Status')->default('0');
            
            // Notification content
            $table->string('Noti_Ar_Name')->nullable();
            $table->string('Noti_En_Name')->nullable();
            
            // Notification type
            $table->string('Type')->nullable();
            $table->string('TypeEn')->nullable();
            $table->string('Type_Code')->nullable();
            
            // References to related entities
            $table->string('Emp')->nullable();     // Employee reference
            $table->string('Client')->nullable();  // Client reference
            $table->string('Product')->nullable(); // Product reference
            $table->string('Store')->nullable();   // Store reference
            $table->string('Safe')->nullable();    // Safe reference
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('notifications');
    }
}
