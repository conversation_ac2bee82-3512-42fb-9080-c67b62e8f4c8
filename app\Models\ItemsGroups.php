<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Stancl\Tenancy\Database\Concerns\CentralConnection;

class ItemsGroups extends Model
{
    use HasFactory, CentralConnection;
      protected $table = 'items_groups';
      protected $fillable = [
        'Code',
        'Name',
        'NameEn',
        'Type',
        'Parent',
        'Note',
        'Image',
        'Discount',
        'Sales_Show',
        'Store_Show',
        'Printer',
        'Arrange',


    ];

              public function Products()
    {
        return $this->hasOne(Products::class);
    }

                           public function ProductMoves()
    {
        return $this->hasOne(ProductMoves::class);
    }



}
