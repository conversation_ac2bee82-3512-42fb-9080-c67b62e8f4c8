<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRabihEducationTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rabih_education', function (Blueprint $table) {
            $table->id();
            
            // Education content information
            $table->string('Arabic_Name')->nullable();  // Arabic title/name
            $table->string('English_Name')->nullable(); // English title/name
            $table->text('Video')->nullable();          // Video URL or embed code
            $table->string('Package')->nullable();      // Package reference (references packages table)
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('rabih_education');
    }
}
