<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateGeneralDailiesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('general_dailies', function (Blueprint $table) {
            $table->id();

            // Basic journal entry information
            $table->string('Code')->nullable();
            $table->date('Date')->nullable();
            $table->string('Type')->nullable();
            $table->string('TypeEn')->nullable();
            $table->string('Code_Type')->nullable();

            // Accounting amounts
            $table->decimal('Debitor', 15, 2)->default(0);
            $table->decimal('Creditor', 15, 2)->default(0);
            $table->text('Statement')->nullable();
            $table->string('Draw')->nullable();

            // Currency amounts
            $table->decimal('Debitor_Coin', 15, 2)->default(0);
            $table->decimal('Creditor_Coin', 15, 2)->default(0);

            // References to other entities
            $table->unsignedBigInteger('Account')->nullable(); // References acccounting_manuals.id
            $table->string('Coin')->nullable();
            $table->string('Cost_Center')->nullable();
            $table->string('userr')->nullable(); // User who created the entry
            $table->string('Branch')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('general_dailies');
    }
}
