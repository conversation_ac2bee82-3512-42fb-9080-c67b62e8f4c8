<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Stancl\Tenancy\Database\Concerns\CentralConnection;

class DefaultDataShowHide extends Model
{
    use HasFactory, CentralConnection;
      protected $table = 'default_data_show_hides';
      protected $fillable = [
        'Status',
        'Shipping_Company',
        'Vendor_Date',
        'Expire_Date',
        'Total_BF_Taxes',
        'Total_Taxes',
        'Coin',
        'Draw',
        'Delegate_Sale',
        'Executor_Sale',
        'Delegate_Purchase',
        'Note',
        'Refrence_Number',
        'Cost_Center',
        'Branch',
        'Serial_Num',
        'Pass',
        'Pattern_Image',
        'Barcode_Print',
        'Unit_Print',
        'Total_BF_Print',
        'Discount_Print',
        'Tax_Print',
        'A4',
        'A5',
        'CM8',
        'Search_Typical',
        'Validity_Product',
        'Group_Brand',
        'Patch_Number',
        'Manufacturing_Model_Shortcomings',
        'Totuch_Screen',
        'Tax_POS',
        'TotalDiscountPrint',
        'TotalTaxPrint',
        'ProductsNumber',
        'TotalQtyPrint',
        'Credit',
        'Barcode',
        'Taknet',
        'Address',
        'Phone1',
        'Phone2',
        'Phone3',
        'Phone4',
        'Text',
        'Seal',
        'Code_Report',
        'Unit',
        'Refrence_Number_Print',
        'Icon_Payment_Recipt',
        'SearchCode',
        'TaxOnTotal',
        'TotalBfTax',
        'AvQty',
        'Disc',
        'Tax',
        'Store',
        'TaxBill',
        'Change_Way_Stores_Transfer',
        'Note_POS',
        'Open_Drawer',
        'client_delivery',
        'POS_RecivedDate',
        'POS_Qty',
        'POS_Barcode',

        'Show_File_ReciptVoucher',
        'Show_File_PaymentVoucher',
        'Show_File_Sales',
        'Show_File_Purchases',
        'Show_File_Checks',
        'Show_File_InsurancePaper',
        'Show_File_TransferStores',

        'Thickness_Print',
        'Height_Print',

          'Thickness',
        'Height',
        'Items_Guide_Store_Show',
        'Sales_Pro_Desc',




    ];

}
