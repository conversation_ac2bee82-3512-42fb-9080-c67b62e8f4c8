<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSupPagesEComDesignsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('sup_pages_e_com_designs', function (Blueprint $table) {
            $table->id();

            // About section colors
            $table->string('About_Title_Color')->nullable();
            $table->string('About_Txt_Color')->nullable();

            // Blogs section colors
            $table->string('Blogs_Title_Color')->nullable();
            $table->string('Blogs_Txt_Color')->nullable();
            $table->string('Blogs_Hover_Txt_Color')->nullable();

            // Contact section colors
            $table->string('Contact_Title_Color')->nullable();
            $table->string('Contact_Txt_Color')->nullable();
            $table->string('Contact_Form_Input_Border_Color')->nullable();
            $table->string('Contact_Form_Input_Txt_Color')->nullable();
            $table->string('Contact_Form_Button_BG_Color')->nullable();
            $table->string('Contact_Form_Button_Txt_Color')->nullable();
            $table->string('Contact_Form_Button_Hover_BG_Color')->nullable();
            $table->string('Contact_Form_Button_Hover_Txt_Color')->nullable();

            // FAQ section colors
            $table->string('Faq_Title_Color')->nullable();
            $table->string('Faq_Q_BG_Color')->nullable();
            $table->string('Faq_Q_Txt_Color')->nullable();
            $table->string('Faq_A_Line_Color')->nullable();
            $table->string('Faq_A_BG_Color')->nullable();
            $table->string('Faq_A_Txt_Color')->nullable();

            // MyAccount section colors
            $table->string('MyAccount_Box_BG_Color')->nullable();
            $table->string('MyAccount_Box_Button_BG_Color')->nullable();
            $table->string('MyAccount_Box_Button_Txt_Color')->nullable();
            $table->string('MyAccount_Box_Button_Hover_BG_Color')->nullable();
            $table->string('MyAccount_Box_Button_Hover_Txt_Color')->nullable();
            $table->string('MyAccount_Box_Input_Border_Color')->nullable();
            $table->string('MyAccount_Box_Input_Txt_Color')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('sup_pages_e_com_designs');
    }
}
