<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateProDetailsImgsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('pro_details_imgs', function (Blueprint $table) {
            $table->id();
            
            // Advertisement/Image information
            $table->string('Image')->nullable();
            $table->string('Link')->nullable();
            $table->string('Status')->default('1');
            $table->string('Type')->nullable();
            $table->string('Title')->nullable();
            $table->text('Description')->nullable();
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('pro_details_imgs');
    }
}
