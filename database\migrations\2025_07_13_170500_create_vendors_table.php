<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateVendorsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('vendors', function (Blueprint $table) {
            $table->id();
            
            // Vendor basic information
            $table->string('Code')->nullable();                    // Vendor code
            $table->string('Name')->nullable();                    // Vendor name (Arabic)
            $table->string('NameEn')->nullable();                  // Vendor name (English)
            $table->string('Phone')->nullable();                   // Primary phone
            $table->string('Phone2')->nullable();                  // Secondary phone
            
            // Business information
            $table->string('Commercial_Register')->nullable();     // Commercial register number
            $table->string('Tax_Card')->nullable();                // Tax card number
            $table->string('Price_Level')->nullable();             // Price level
            $table->string('Tax_Registration_Number')->nullable(); // Tax registration number
            $table->string('Tax_activity_code')->nullable();       // Tax activity code
            $table->string('work_nature')->nullable();             // Nature of work
            
            // Location information
            $table->string('Governrate')->nullable();              // Governorate reference
            $table->string('City')->nullable();                    // City reference
            $table->string('Place')->nullable();                   // Place reference
            $table->string('Nationality')->nullable();             // Nationality reference
            $table->string('Buliding_Num')->nullable();            // Building number
            $table->string('Street')->nullable();                  // Street
            $table->string('Postal_Code')->nullable();             // Postal code
            $table->string('tax_magistrate')->nullable();          // Tax magistrate
            $table->string('Floor')->nullable();                   // Floor
            $table->string('Room')->nullable();                    // Room
            $table->string('Landmark')->nullable();                // Landmark
            $table->text('Add_Info')->nullable();                  // Additional information
            
            // References
            $table->string('Account')->nullable();                  // Account reference
            $table->string('User')->nullable();                    // User reference
            $table->string('Responsible')->nullable();             // Responsible employee reference
            $table->string('Pro_Group')->nullable();               // Product group reference
            $table->string('Brand')->nullable();                   // Brand reference
            $table->string('SearchCode')->nullable();              // Search code
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('vendors');
    }
}
