<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Stancl\Tenancy\Database\Concerns\CentralConnection;

class PurchasesDefaultData extends Model
{
    use HasFactory, CentralConnection;
             protected $table = 'purchases_default_data';
      protected $fillable = [
        'Payment_Method',
        'Status',
        'V_and_C',
        'Safe',
        'Vendor',
        'Delegate',
        'Store',
        'Coin',
        'Brand',
        'Group',
        'English_Name',
        'Expire',
        'Empp',
        'Discount',
        'Quality_Qty',

    ];

         public function Safe()
    {
        return $this->belongsTo(AcccountingManual::class,'Safe');
    }
          public function Vendor()
    {
        return $this->belongsTo(AcccountingManual::class,'Vendor');
    }
          public function Delegate()
    {
        return $this->belongsTo(Employess::class,'Delegate');
    }
          public function Store()
    {
        return $this->belongsTo(Stores::class,'Store');
    }
          public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }          public function Brand()
    {
        return $this->belongsTo(Brands::class,'Brand');
    }          public function Group()
    {
        return $this->belongsTo(ItemsGroups::class,'Group');
    }


}
