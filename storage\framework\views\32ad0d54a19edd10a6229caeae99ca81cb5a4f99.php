<?php $__env->startSection('content'); ?>
<?php
use App\Models\PurchasesDefaultData;
$Def=PurchasesDefaultData::orderBy('id','desc')->first();
if(is_null($Def)){
    $Def = new App\Models\PurchasesDefaultData();
    $Def->V_and_C = null;
}
use App\Models\DefaultDataShowHide;
$show=DefaultDataShowHide::orderBy('id','desc')->first();
      $credStyle="none";
?>



<style>
    #NewVend input , #NewVend .select2-container--default .select2-selection--single{
        border: 1px solid #817a7a!important;
    }
</style>
  <title><?php echo e(trans('admin.PurchasesOrder')); ?></title>

    <main id="js-page-content" role="main" class="page-content">
                    <ol class="breadcrumb page-breadcrumb">
                        <li class="breadcrumb-item"><a href="javascript:void(0);"><?php echo e(trans('admin.Purchases')); ?> </a></li>
                        <li class="breadcrumb-item active"><?php echo e(trans('admin.PurchasesOrder')); ?> </li>
                        <li class="position-absolute pos-top pos-right d-none d-sm-block"><span
                                class="js-get-date"></span></li>
                    </ol>

    <form id="form" action="<?php echo e(url('AddPurchasesOrder')); ?>" method="post" enctype="multipart/form-data">
        <?php echo csrf_field(); ?>

               <?php echo view('honeypot::honeypotFormFields'); ?>
                    <div class="row">
                        <div class="col-lg-12">
                            <div id="panel-2" class="panel"  style="background: #ccbed59e;">
                                <div class="panel-hdr">
                                    <h2>
                                        <span class="fw-300"><i>   <?php echo e(trans('admin.PurchasesOrder')); ?>  </i></span>
                                    </h2>
                                </div>
                                <div class="panel-container show">
                                      <span id="ex"> <?php echo $__env->make('admin.layouts.messages', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>
                                    <div class="panel-content">
                                    <div class="form-row">
                                        <div class="form-group col-lg-2">
                                            <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Code')); ?> </label>
                                            <input type="text" value="<?php echo e($Code); ?>" class="form-control " disabled>
                                            <input type="hidden" name="Code" value="<?php echo e($Code); ?>" class="form-control">
                                        </div>




                                     <?php if(auth()->guard('admin')->user()->emp == 0): ?>
                      <div class="form-group col-lg-3">
                                    <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Date')); ?></label>
                                    <input type="date" name="Date" value="<?php echo e(date('Y-m-d')); ?>" class="form-control" required>
                                </div>
                                        <?php else: ?>

                                         <?php if(auth()->guard('admin')->user()->Date == 1): ?>
                                   <div class="form-group col-lg-3">
                                    <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Date')); ?></label>
                                    <input type="date" name="Date" value="<?php echo e(date('Y-m-d')); ?>" class="form-control" required>
                                </div>
                                        <?php else: ?>
                                 <div class="form-group col-lg-3">
                                    <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Date')); ?></label>
                                    <input type="date" name="Date" value="<?php echo e(date('Y-m-d')); ?>" class="form-control" required readonly>
                                </div>
                                        <?php endif; ?>

                                        <?php endif; ?>
                                        <div class="form-group col-lg-2">
                                            <label class="form-label" for=""><?php echo e(trans('admin.Store')); ?></label>
                                            <select class="select2 form-control w-100" id="store" name="Store" required>

                                            <?php $__currentLoopData = $Stores; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $stor): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($stor->id); ?>" <?php if(optional($Def)->Store == $stor->id): ?> selected <?php endif; ?>>

                                     <?php echo e(app()->getLocale() == 'ar' ?$stor->Name :$stor->NameEn); ?>

                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                        </div>
                                        <div class="form-group col-lg-2">
                                            <label class="form-label" for=""><?php echo e(trans('admin.Safe')); ?></label>
                                            <select class="select2 form-control w-100" id="SSAFE" name="Safe" required>
                                            <?php $__currentLoopData = $Safes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $safe): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($safe->id); ?>" <?php if(optional($Def)->Safe == $safe->id): ?> selected <?php endif; ?>>


                                        <?php echo e(app()->getLocale() == 'ar' ?$safe->Name :$safe->NameEn); ?>

                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                        </div>

                                     <?php if($show->Coin == 1): ?>
                                        <div class="form-group col-lg-2">
                                            <label class="form-label" for=""><?php echo e(trans('admin.Coin')); ?></label>
                                            <select class="select2 form-control w-100" name="Coin" required>
                                                 <option value=""> <?php echo e(trans('admin.Coin')); ?></option>
                                            <?php $__currentLoopData = $Coins; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $coin): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($coin->id); ?>" <?php if(optional($Def)->Coin == $coin->id): ?> selected <?php endif; ?>>

                               <?php echo e(app()->getLocale() == 'ar' ?$coin->Arabic_Name :$coin->English_Name); ?>

                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                        </div>
                                     <?php else: ?>
                                          <div class="form-group col-lg-2" style="display: none">
                                            <label class="form-label" for=""><?php echo e(trans('admin.Coin')); ?></label>
                                            <select class="select2 form-control w-100" name="Coin" required>
                                                 <option value=""> <?php echo e(trans('admin.Coin')); ?></option>
                                            <?php $__currentLoopData = $Coins; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $coin): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($coin->id); ?>" <?php if($Def->Coin == $coin->id): ?> selected <?php endif; ?>>
                                            <?php echo e(app()->getLocale() == 'ar' ?$coin->Arabic_Name :$coin->English_Name); ?>

                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                        </div>
                                    <?php endif; ?>

                                           <?php if($show->Draw == 1): ?>
                                        <div class="form-group col-lg-2">
                                            <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Draw')); ?></label>
                                            <input type="text" name="Draw" value="1" class="form-control" required>
                                        </div>
                                        <?php else: ?>
                                          <div class="form-group col-lg-2" style="display: none">
                                            <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Draw')); ?></label>
                                            <input type="text" name="Draw" value="1" class="form-control" required>
                                        </div>
                                        <?php endif; ?>

                                                 <?php if(auth()->guard('admin')->user()->emp != 0): ?>



                                          <div class="form-group col-lg-2">
                           <label class="form-label" for=""> <?php echo e(trans('admin.Payment_Method')); ?></label>
                           <select class="select2 form-control w-100" name="Payment_Method" id="Payment_Method" onchange="Statuss()" required>
                                <?php if(auth()->guard('admin')->user()->Cash == 1): ?>
                           <option value="Cash" <?php if($Def->Payment_Method == 'Cash'): ?> selected <?php endif; ?>><?php echo e(trans('admin.Cash')); ?> </option>
                               <?php endif; ?>
                                 <?php if(auth()->guard('admin')->user()->Later == 1): ?>
                           <option value="Later" <?php if($Def->Payment_Method == 'Later'): ?> selected <?php endif; ?>><?php echo e(trans('admin.Later')); ?></option>
                               <?php endif; ?>

                           </select>
                        </div>

                         <?php else: ?>
                                   <div class="form-group col-lg-2">
                           <label class="form-label" for=""> <?php echo e(trans('admin.Payment_Method')); ?></label>
                           <select class="select2 form-control w-100" name="Payment_Method" id="Payment_Method" onchange="Statuss()" required>
                           <option value="Cash" <?php if($Def->Payment_Method == 'Cash'): ?> selected <?php endif; ?>><?php echo e(trans('admin.Cash')); ?> </option>
                           <option value="Later" <?php if($Def->Payment_Method == 'Later'): ?> selected <?php endif; ?>><?php echo e(trans('admin.Later')); ?></option>

                           </select>
                        </div>

                         <?php endif; ?>


                                           <?php if($show->Status == 1): ?>
                                        <div class="form-group col-lg-2">
                                            <label class="form-label" for=""><?php echo e(trans('admin.Status')); ?></label>
         <select class="select2 form-control w-100" name="Status" onchange="Statuss()" id="Status"  required>
                                                <option value="1" <?php if($Def->Status == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Recived')); ?> </option>
                                                <option value="0" <?php if($Def->Status == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Pending')); ?></option>
                                            </select>
                                        </div>
                                        <?php else: ?>
                                    <div class="form-group col-lg-2" style="display: none">
                                            <label class="form-label" for=""><?php echo e(trans('admin.Status')); ?></label>
         <select class="select2 form-control w-100" name="Status" onchange="Statuss()" id="Status"  required>
                                                <option value="1" <?php if($Def->Status == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Recived')); ?> </option>
                                                <option value="0" <?php if($Def->Status == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Pending')); ?></option>
                                            </select>
                                        </div>
                                        <?php endif; ?>


                                               <?php if($show->Cost_Center == 1): ?>
                                        <div class="form-group col-lg-2">
                                            <label class="form-label" for=""> <?php echo e(trans('admin.Cost_Center')); ?> </label>
                                            <select class="select2 form-control w-100" name="Cost_Center">
                                            <option value=""> <?php echo e(trans('admin.Cost_Center')); ?></option>
                                            <?php $__currentLoopData = $CostCenters; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cost): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($cost->id); ?>">
                                                       <?php echo e(app()->getLocale() == 'ar' ?$cost->Arabic_Name :$cost->English_Name); ?>

                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                        </div>
                                        <?php else: ?>
                                         <div class="form-group col-lg-2" style="display: none">
                                            <label class="form-label" for=""> <?php echo e(trans('admin.Cost_Center')); ?> </label>
                                            <select class="select2 form-control w-100" name="Cost_Center">
                                            <option value=""> <?php echo e(trans('admin.Cost_Center')); ?></option>
                                            <?php $__currentLoopData = $CostCenters; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cost): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($cost->id); ?>">
                                             <?php echo e(app()->getLocale() == 'ar' ?$cost->Arabic_Name :$cost->English_Name); ?>

                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                        </div>
                                        <?php endif; ?>

                                          <?php if($show->Delegate_Purchase == 1): ?>
                                        <div class="form-group col-lg-2">
                                            <label class="form-label" for=""><?php echo e(trans('admin.Delegate')); ?></label>
                           <select class="select2 form-control w-100" name="Delegate">
                                          <option value=""> <?php echo e(trans('admin.Delegate')); ?></option>
                                            <?php $__currentLoopData = $Employess; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $emp): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                         <option value="<?php echo e($emp->id); ?>" <?php if(optional($Def)->Delegate == $emp->id): ?> selected <?php endif; ?>>

                              <?php echo e(app()->getLocale() == 'ar' ?$emp->Name :$emp->NameEn); ?>

                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                        </div>
                                        <?php else: ?>
                                         <div class="form-group col-lg-2" style="display: none">
                                            <label class="form-label" for=""><?php echo e(trans('admin.Delegate')); ?></label>
                                            <select class="select2 form-control w-100" name="Delegate">
                                          <option value=""> <?php echo e(trans('admin.Delegate')); ?></option>
                                            <?php $__currentLoopData = $Employess; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $emp): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                         <option value="<?php echo e($emp->id); ?>" <?php if($Def->Delegate == $emp->id): ?> selected <?php endif; ?>>
                                          <?php echo e(app()->getLocale() == 'ar' ?$emp->Name :$emp->NameEn); ?>

                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                        </div>
                                        <?php endif; ?>

                                            <?php if($show->Vendor_Date == 1): ?>
                                        <div class="form-group col-lg-2">
                                <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Vendor_Bill_Date')); ?></label>
                     <input type="date" name="Vendor_Bill_Date" value="<?php echo e(old('Vendor_Bill_Date')); ?>" class="form-control">
                                        </div>
                                        <?php else: ?>
                                         <div class="form-group col-lg-2" style="display: none">
                                <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Vendor_Bill_Date')); ?></label>
                     <input type="date" name="Vendor_Bill_Date" value="<?php echo e(old('Vendor_Bill_Date')); ?>" class="form-control">
                                        </div>
                                        <?php endif; ?>


                                          <?php if($show->Refrence_Number == 1): ?>
                                        <div class="form-group col-lg-2">
                             <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Refrence_Number')); ?></label>
                   <input type="text" name="Refernce_Number" value="<?php echo e(old('Refernce_Number')); ?>" class="form-control">
                                        </div>
                                        <?php else: ?>
                                          <div class="form-group col-lg-2" style="display: none">
                             <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Refrence_Number')); ?></label>
                   <input type="text" name="Refernce_Number" value="<?php echo e(old('Refernce_Number')); ?>" class="form-control">
                                        </div>
                                        <?php endif; ?>


                                           <div class="form-group col-lg-3">
                                    <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Purchases_Date')); ?></label>
                                    <input type="date" name="Purchases_Date" value="<?php echo e(date('Y-m-d')); ?>" class="form-control">
                                </div>

                                        <div class="form-group col-lg-3">
                                            <label class="form-label" for=""><?php echo e(trans('admin.Account')); ?> (<?php echo e(trans('admin.Vendor')); ?>)</label>
                                      <select  class="select2 form-control w-100" id="vendor" name="Vendor" required>
                                          <option value="<?php echo e(optional($Def)->Vendor); ?>">

                                       <?php echo e(app()->getLocale() == 'ar' ?optional($Def->Vendor()->first())->Name :optional($Def->Vendor()->first())->NameEn); ?>

                                          </option>
                                            </select>
                                        </div>


  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('خانه رصيد حساب مورد')): ?>   <?php $credStyle="block"; ?> <?php endif; ?>



                                        <div class="form-group col-lg-1" style="display: <?php echo e($credStyle); ?>">
                                 <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Account_Credit')); ?> </label>
                             <input type="text" id="AccountCredit" value="0" class="form-control " disabled>
                                        </div>

                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('ايقونه اضافه صنف')): ?>
                    <div class="form-group col-lg-1 col-1">
                  <button style="margin: 22px" type="button" class="btn btn-default" data-toggle="modal" data-target="#NewClient">
                                            <i class="fal fa-pen"></i>
                                     </button>

                               <!-- Modal New Product -->
            <div class="modal fade" id="NewClient" tabindex="-1" role="dialog" aria-hidden="true">
                        <div class="modal-dialog modal-dialog-centered modal-xl" role="document">
                            <div class="modal-content">
                                <div class="modal-header">

                                    <h4 class="modal-body row">


                                             <div class="form-group col-md-6">
                                       <label class="form-label" for="">
                                       <?php echo e(trans('admin.Product_Type')); ?>

                                       </label><span class="strick">*</span>
                                       <select class="select2 form-control w-100" id="P_Type"  >
                                          <option value="Completed"><?php echo e(trans('admin.Completed')); ?> </option>
                                          <option value="Raw"><?php echo e(trans('admin.Raw')); ?> </option>
                                          <option value="Service"><?php echo e(trans('admin.Service')); ?></option>
                                          <option value="Subscribe"><?php echo e(trans('admin.Subscribe')); ?></option>
                                          <option value="Industrial"><?php echo e(trans('admin.Industrial')); ?></option>
                                          <option value="Serial"><?php echo e(trans('admin.Serial')); ?></option>
                                       </select>
                                    </div>
                                           <div class="form-group col-md-6">
                                       <label class="form-label" for="">  <?php echo e(trans('admin.Brand')); ?> </label>
                                       <select class="select2 form-control w-100" id="Brand">
                                          <?php $__currentLoopData = $Brands; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $brand): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                          <option value="<?php echo e($brand->id); ?>">

                             <?php echo e(app()->getLocale() == 'ar' ?$brand->Name :$brand->NameEn); ?>

                                           </option>
                                          <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                       </select>
                                    </div>
                                <div class="form-group col-md-4">
                                       <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Product_Ar_Name')); ?>

                                       </label><span class="strick">*</span>
                <input type="text" id="P_Ar_Name"  placeholder="<?php echo e(trans('admin.Product_Ar_Name')); ?> "  class="form-control" >
                                    </div>
                                    <div class="form-group col-md-4">
                                       <label class="form-label" for="simpleinput">  <?php echo e(trans('admin.Product_En_Name')); ?></label>
                                       <input type="text" id="P_En_Name" placeholder="<?php echo e(trans('admin.Product_En_Name')); ?> "  class="form-control">
                                    </div>
                                   <div class="form-group col-md-4">
                                       <label class="form-label" for="">  <?php echo e(trans('admin.Group')); ?>  </label><span class="strick">*</span>
                                       <select class="select2 form-control w-100" id="Group" >
                                          <?php $__currentLoopData = $ItemsGroups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $group): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                 <option value="<?php echo e($group->id); ?>">
                                     <?php echo e(app()->getLocale() == 'ar' ?$group->Name :$group->NameEn); ?>

                                           </option>
                                          <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                       </select>
                                    </div>

                                                     <div class="form-group col-md-4">
                                       <label class="form-label" for="">  <?php echo e(trans('admin.Unit')); ?>  </label><span class="strick">*</span>
                          <select class="select2 form-control w-100" id="unit" >
                                                                  <?php $__currentLoopData = $Units; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $uni): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                              <option value="<?php echo e($uni->id); ?>">

                                 <?php echo e(app()->getLocale() == 'ar' ?$uni->Name :$uni->NameEn); ?>

                              </option>
                                                                  <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                               </select>
                                    </div>

                                          <div class="form-group col-md-4">
                                      <label class="form-label" for="">
                                       <?php echo e(trans('admin.Rate')); ?>

                                       </label><span class="strick">*</span>
                  <input type="number" step="any" id="Rate" class="form-control"  value="1" placeholder="<?php echo e(trans('admin.Rate')); ?>">
                                                            </div>

                                    <div class="form-group col-md-4" style="position:relative;">
                                  <label class="form-label" for="">
                                       <?php echo e(trans('admin.Barcode')); ?>

                                       </label><span class="strick">*</span>
                                     <input type="text" id="Barcode" class="form-control" placeholder="<?php echo e(trans('admin.Barcode')); ?>">

                                                            </div>
                                                            <div class="form-group col-md-4">
                                                      <label class="form-label" for="">
                                 <?php echo e(trans('admin.Price_One')); ?>

                                       </label><span class="strick">*</span>
                  <input type="number" step="any" id="Price" class="form-control"placeholder=" <?php echo e(trans('admin.Price_One')); ?>" value="0" >
                                                            </div>
                                                             <div class="form-group col-md-4">
                                                                          <label class="form-label" for="">
                                 <?php echo e(trans('admin.Price_Two')); ?>

                                       </label><span class="strick">*</span>
            <input type="number" step="any" id="Price_Two" class="form-control" placeholder=" <?php echo e(trans('admin.Price_Two')); ?>" value="0" >
                                                            </div>
                                                            <div class="form-group col-md-4">
                                                                  <label class="form-label" for="">
                                 <?php echo e(trans('admin.Price_Three')); ?>

                                       </label><span class="strick">*</span>
              <input type="number" step="any" id="Price_Three" class="form-control" placeholder=" <?php echo e(trans('admin.Price_Three')); ?>"  value="0">
                                                            </div>


                                    </h4>
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                        <span aria-hidden="true"><i class="fal fa-times"></i></span>
                                    </button>
                                </div>

                                <div class="modal-footer">
                  <button type="button" class="btn btn-secondary" data-dismiss="modal"> <?php echo e(trans('admin.No')); ?></button>
                   <button type="button" onclick="NewProducts()"  class="btn btn-primary"> <?php echo e(trans('admin.AddNew')); ?></button>
                                </div>

                            </div>
                        </div>
                    </div>

                                        </div>
                                          <?php endif; ?>
                                           <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('ايقونه اضافه مورد')): ?>
                                    <div class="form-group col-lg-1 col-1">
                  <button style="margin: 22px" type="button" class="btn btn-default" data-toggle="modal" data-target="#NewVend">
                                            <i class="fal fa-user"></i>
                                     </button>

                                                             <!-- Modal New Vendor -->
            <div class="modal fade" id="NewVend" tabindex="-1" role="dialog" aria-hidden="true">
                        <div class="modal-dialog modal-dialog-centered modal-xl" role="document">
                            <div class="modal-content">
                                <div class="modal-header">

                                    <h4 class="modal-body row">


                                        <div class="form-group col-md-2">
                                 <label class="form-label" for="simpleinput">   <?php echo e(trans('admin.Code')); ?></label>
                                 <input type="text" id="CODE"  value="<?php echo e($CodeUser); ?>" disabled class="form-control">

                                                                    </div>
                                                   <div class="form-group col-md-3">
                <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Arabic_Name')); ?>  </label><span class="strick">*</span>
                        <input type="text" id="Name" value="<?php echo e(old('Name')); ?>" class="form-control">
                                                                    </div>

                                               <div class="form-group col-md-3">
                <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.English_Name')); ?>  </label><span class="strick">*</span>
                        <input type="text" id="NameEn" value="<?php echo e(old('NameEn')); ?>" class="form-control">
                                                                    </div>

                                                               <div class="form-group col-md-4">
                <label class="form-label" for="">   <?php echo e(trans('admin.Price_Level')); ?> </label><span class="strick">*</span>
                             <select class="select2 form-control w-100" id="PriceLevel">
                                    <option value="1"> <?php echo e(trans('admin.Level1')); ?> </option>
                                    <option value="2"> <?php echo e(trans('admin.Level2')); ?> </option>
                                    <option value="3"> <?php echo e(trans('admin.Level3')); ?> </option>

                                                                        </select>
                                                                    </div>

                                    </h4>
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                        <span aria-hidden="true"><i class="fal fa-times"></i></span>
                                    </button>
                                </div>

                                <div class="modal-footer">
                  <button type="button" class="btn btn-secondary" data-dismiss="modal"> <?php echo e(trans('admin.No')); ?></button>
                   <button type="button" onclick="NewVendor()"  class="btn btn-primary"> <?php echo e(trans('admin.AddNew')); ?></button>
                                </div>

                            </div>
                        </div>
                    </div>



                                            </div>
                                        <?php endif; ?>


                             <div  id="CHECK" style="display: none">
                                         <div class="row">
                                                  <div class="form-group col-lg-4">
                                            <label class="form-label" for=""><?php echo e(trans('admin.Check_Type')); ?></label>
                                            <select class="select2 form-control w-100"  name="Check_Type" >
                                                <option value=""> <?php echo e(trans('admin.Check_Type')); ?></option>
                                            <?php $__currentLoopData = $ChecksTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($type->id); ?>" >

                                 <?php echo e(app()->getLocale() == 'ar' ?$type->Arabic_Name :$type->English_Name); ?>

                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                        </div>


                                                              <div class="form-group col-lg-4">
                                            <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Due_Date')); ?>  </label>
                      <input type="date"  class="form-control" name="Due_Date">
                                        </div>


                                      <div class="form-group col-lg-4">
                                   <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Check_Number')); ?>  </label>
             <input type="number" step="any"   class="form-control" name="Check_Number">
                                        </div>
                                                </div>

                                        </div>

                                                <?php if($show->Note == 1): ?>
                                        <div class="form-group col-lg-2">
                                            <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Notes')); ?>  </label>
                                            <input type="text" name="Note" value="<?php echo e(old('Note')); ?>" class="form-control">
                                        </div>
                                        <?php else: ?>
                                          <div class="form-group col-lg-2" style="display: none">
                                            <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Notes')); ?>  </label>
                                            <input type="text" name="Note" value="<?php echo e(old('Note')); ?>" class="form-control">
                                        </div>
                                        <?php endif; ?>

                                    </div>
                            </div>
                        </div>
                    </div>
                    </div>
                    <div class="col-xl-12">
                        <div id="panel-1" class="panel">

                            <div class="panel-container show">
                                <div class="panel-content" style="background: #fad2f796;">
                             <div class="row">
                           <?php if($show->SearchCode == 1): ?>
                        <div class="form-group col-lg-3">
                           <label class="form-label" for="simpleinput">  </label>
                           <div class="input-items" style="position:relative;">
                              <input type="text" id="search" class="form-control" placeholder="<?php echo e(trans('admin.Search_For_Products')); ?>">
                                                            <?php if(app()->getLocale() == 'ar' ): ?>
                     <i class="fal fa-barcode-alt" style="position:absolute ;top:5px;left:3px;"></i>
                      <?php else: ?>
                            <i class="fal fa-barcode-alt" style="position:absolute ;top:5px;right: 0;left:auto;"></i>
                      <?php endif; ?>
                           </div>
                        </div>
                             <div class="form-group col-lg-3">
                           <label class="form-label" for="simpleinput">  </label>
                           <div class="input-items" style="position:relative;">
                              <input type="text" id="SearchCode" class="form-control" placeholder="<?php echo e(trans('admin.SearchCode')); ?>">
                                                                 <?php if(app()->getLocale() == 'ar' ): ?>
                     <i class="fal fa-barcode-alt" style="position:absolute ;top:5px;left:3px;"></i>
                      <?php else: ?>
                            <i class="fal fa-barcode-alt" style="position:absolute ;top:5px;right: 0;left:auto;"></i>
                      <?php endif; ?>
                           </div>
                        </div>
                         <?php else: ?>
                              <div class="form-group col-lg-6">
                           <label class="form-label" for="simpleinput">  </label>
                           <div class="input-items" style="position:relative;">
                              <input type="text" id="search" class="form-control" placeholder="<?php echo e(trans('admin.Search_For_Products')); ?>">
                                                             <?php if(app()->getLocale() == 'ar' ): ?>
                     <i class="fal fa-barcode-alt" style="position:absolute ;top:5px;left:3px;"></i>
                      <?php else: ?>
                            <i class="fal fa-barcode-alt" style="position:absolute ;top:5px;right: 0;left:auto;"></i>
                      <?php endif; ?>
                           </div>
                        </div>
                         <?php endif; ?>
                        <?php if($show->Group_Brand == 1): ?>
                        <div class="form-group col-lg-3 mt-3">
                           <select class="select2 form-control w-100" id="Brandd">
                              <option value=""><?php echo e(trans('admin.Brand')); ?></option>
                              <?php $__currentLoopData = $Brands; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $brand): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                              <option value="<?php echo e($brand->id); ?>">
                                <?php echo e(app()->getLocale() == 'ar' ?$brand->Name :$brand->NameEn); ?>

                               </option>
                              <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                           </select>
                        </div>
                        <div class="form-group col-lg-3 mt-3">
                           <select class="select2 form-control w-100" id="Groupp">
                              <option value=""><?php echo e(trans('admin.Group')); ?></option>
                              <?php $__currentLoopData = $ItemsGroups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $group): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                              <option value="<?php echo e($group->id); ?>">
                                <?php echo e(app()->getLocale() == 'ar' ?$group->Name :$group->NameEn); ?>

                               </option>
                              <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                           </select>
                        </div>
                        <?php endif; ?>


                     </div>
                                      <div id="mobile-overflow">
                                    <table
                                    class="table table-bordered table-hover table-striped w-100 mobile-width-more table-color1 mt-2">
                                    <thead>
                                        <tr>
                                            <th><?php echo e(trans('admin.Name')); ?></th>
                                            <th><?php echo e(trans('admin.Unit')); ?></th>
                                            <th><?php echo e(trans('admin.Code')); ?></th>
                                            <th><?php echo e(trans('admin.Qty')); ?></th>
                                            <th><?php echo e(trans('admin.Price')); ?></th>
                                            <?php if($show->Disc == 1): ?>
                                 <th><?php echo e(trans('admin.Discount')); ?></th>
                                  <?php endif; ?>
                                     <?php if($show->Tax == 1): ?>
                                 <th><?php echo e(trans('admin.Tax')); ?></th>
                                  <?php endif; ?>
                                     <?php if($show->Store == 1): ?>
                                 <th style="padding:12px 20px;"><?php echo e(trans('admin.Store')); ?></th>
                                  <?php endif; ?>
                                               <?php if($show->Expire_Date == 1): ?>
                                            <th><?php echo e(trans('admin.Exp_Date')); ?></th>
                                            <?php endif; ?>
                                            <th><?php echo e(trans('admin.Actions')); ?></th>
                                        </tr>
                                    </thead>
                                    <tbody class="Data" id="Data">

                                    </tbody>
                                    </table>
                                    </div>
                                    <!-- datatable start -->
                                      <div id="mobile-overflow">
                                    <table id="dt"
                                        class="table table-bordered table-hover table-striped w-100 mobile-width-more table-color2">
                                        <thead>
                                            <tr>
                                            <th><?php echo e(trans('admin.Name')); ?></th>
                                            <th><?php echo e(trans('admin.Unit')); ?></th>
                                            <th><?php echo e(trans('admin.Code')); ?></th>
                                            <th><?php echo e(trans('admin.Qty')); ?></th>
                                            <th><?php echo e(trans('admin.Price')); ?></th>
                                             <?php if($show->Disc == 1): ?>
                                 <th><?php echo e(trans('admin.Discount')); ?></th>
                                  <?php endif; ?>
                                    <?php if($show->TotalBfTax == 1): ?>
                                 <th><?php echo e(trans('admin.Total_Bf_Taxes')); ?></th>
                                         <?php endif; ?>
                                        <?php if($show->Tax == 1): ?>
                                 <th><?php echo e(trans('admin.Tax')); ?></th>
                                  <?php endif; ?>
                                 <th><?php echo e(trans('admin.Total')); ?></th>

                                        <?php if($show->Store == 1): ?>
                                 <th><?php echo e(trans('admin.Store')); ?></th>
                                  <?php endif; ?>
                                              <?php if($show->Expire_Date == 1): ?>
                                            <th><?php echo e(trans('admin.Exp_Date')); ?></th>
                                            <?php endif; ?>
                                            <th><?php echo e(trans('admin.Actions')); ?></th>
                                            </tr>
                                        </thead>
                                        <tbody id="data-dt">


                                        </tbody>

                                    </table>
                                    </div>
                                      <div class="form-row">
                                        <div class="form-group col-lg-4">
                                 <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Product_Numbers')); ?> </label>
                      <input type="text" id="Product_Numbers" disabled  class="form-control">
                      <input type="hidden" id="Product_NumbersHide" name="Product_Numbers">
                                        </div>
                                        <div class="form-group col-lg-4">
                         <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Total_Qty')); ?> </label>
                                           <input type="text" id="Total_Qty" disabled  class="form-control">
                                            <input type="hidden" id="Total_QtyHide" name="Total_Qty">
                                                <input type="hidden" id="Total_Discount" disabled  class="form-control">
                                            <input type="hidden" id="Total_DiscountHide" name="Total_Discount">
                                              <input type="hidden" id="Total_Bf_Taxes" disabled  class="form-control">
                                            <input type="hidden" id="Total_Bf_TaxesHide" name="Total_BF_Taxes">
                                        </div>
                                              <div class="form-group col-lg-4">
                       <label class="form-label" for="simpleinput">  <?php echo e(trans('admin.Total_Price')); ?></label>
                           <input type="text" id="Total_Price" disabled  class="form-control">
                                            <input type="hidden" id="Total_PriceHide" name="Total_Price">
                                        </div>

                                                <div class="form-group col-lg-3">
                                  <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Total_Discount')); ?>  </label>
                                         <input type="text" id="Total_DiscountTT" disabled  class="form-control">
                                            <input type="hidden" id="Total_DiscountHideTT" name="Total_Discount">
                                        </div>




                                                  <div class="form-group col-lg-3">
                           <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Total_af_Discount')); ?> </label>
                                    <input type="text" class="form-control" id="TAFterD"  disabled>

                                        </div>

                                        <div class="form-group col-lg-3">
                          <label class="form-label" for="simpleinput">   <?php echo e(trans('admin.Total_Taxes')); ?></label>
                                         <input type="text" id="Total_Taxes" disabled  class="form-control">
                                            <input type="hidden" id="Total_TaxesHide" name="Total_Taxes">
                                        </div>


                                    <div class="form-group col-lg-3">
                           <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Total_Bill')); ?> </label>

         <input type="text" id="Total_Bill" disabled  class="form-control">
                                        </div>



                                                                    <div class="form-group col-lg-4">
                           <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Discount_Bill')); ?> </label>
                           <input type="text" class="form-control" id="resdiual" name="DiscountBill" value="0" onkeyup="Statuss()">

                                        </div>


                                                                                     <div class="form-group col-lg-4">
                           <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Total_Net')); ?> </label>

         <input type="text" id="Total_PriceW" disabled  class="form-control">
                                        </div>


                                        <div class="form-group col-lg-4">
                           <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Paid')); ?> </label>
                                    <input type="text" name="Pay" id="paid"   class="form-control">

                                        </div>

                                    </div>
                                    <div class="buttons mt-3" id="Submit" style="display: none">
                          <input type="hidden" id="sp" name="SP">
            <button type="button"  class="btn btn-primary" onclick="SPS()"> <i class="fal fa-folder"></i> <?php echo e(trans('admin.Save')); ?> </button>

          <button type="button"  class="btn btn-primary" onclick="SPP()"><i class="fal fa-save"></i>  <?php echo e(trans('admin.SaveandPrint')); ?> </button>
                                      </div>
                                </div>
                            </div>
                        </div>
                    </div>



                    </div>

                </form>
              <?php if($Def->Discount == 0): ?>
            <input type="hidden" id="DIS" value="0">  <!-- Number -->
        <?php else: ?>
            <input type="hidden" id="DIS" value="1">  <!-- Precent -->
        <?php endif; ?>
     <input type="hidden" id="ED" value="<?php echo e($show->Expire_Date); ?>">
          <input type="hidden" id="DISCSHOW" value="<?php echo e($show->Disc); ?>">
   <input type="hidden" id="TOTBFSHOW" value="<?php echo e($show->TotalBfTax); ?>">
   <input type="hidden" id="TAXSHOW" value="<?php echo e($show->Tax); ?>">
   <input type="hidden" id="STORESHOW" value="<?php echo e($show->Store); ?>">
                </main>

<?php if(app()->getLocale() == 'ar' ): ?>
<input type="hidden" id="LANG" value="ar">
<?php else: ?>
<input type="hidden" id="LANG" value="en">
<?php endif; ?>



<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>

<link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/datagrid/datatables/datatables.bundle.css')); ?>">
<link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/formplugins/summernote/summernote.css')); ?>">
<link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/formplugins/select2/select2.bundle.css')); ?>">
<script src="<?php echo e(asset('Admin/js/datagrid/datatables/datatables.bundle.js')); ?>"></script>
<script src="<?php echo e(asset('Admin/js/datagrid/datatables/datatables.export.js')); ?>"></script>
<script src="<?php echo e(asset('Admin/js/formplugins/summernote/summernote.js')); ?>"></script>
<script src="<?php echo e(asset('Admin/js/formplugins/select2/select2.bundle.js')); ?>"></script>
<script>
   var autoSave = $('#autoSave');
   var interval;
   var timer = function()
   {
       interval = setInterval(function()
       {
           //start slide...
           if (autoSave.prop('checked'))
               saveToLocal();

           clearInterval(interval);
       }, 3000);
   };

   //save
   var saveToLocal = function()
   {
       localStorage.setItem('summernoteData', $('#saveToLocal').summernote("code"));
       console.log("saved");
   }

   //delete
   var removeFromLocal = function()
   {
       localStorage.removeItem("summernoteData");
       $('#saveToLocal').summernote('reset');
   }

   $(document).ready(function()
   {
       //init default
       $('.js-summernote').summernote(
       {
           height: 200,
           tabsize: 2,
           placeholder: "Type here...",
           dialogsFade: true,
           toolbar: [
               ['style', ['style']],
               ['font', ['strikethrough', 'superscript', 'subscript']],
               ['font', ['bold', 'italic', 'underline', 'clear']],
               ['fontsize', ['fontsize']],
               ['fontname', ['fontname']],
               ['color', ['color']],
               ['para', ['ul', 'ol', 'paragraph']],
               ['height', ['height']]
               ['table', ['table']],
               ['insert', ['link', 'picture', 'video']],
               ['view', ['fullscreen', 'codeview', 'help']]
           ],
           callbacks:
           {
               //restore from localStorage
               onInit: function(e)
               {
                   $('.js-summernote').summernote("code", localStorage.getItem("summernoteData"));
               },
               onChange: function(contents, $editable)
               {
                   clearInterval(interval);
                   timer();
               }
           }
       });

       //load emojis
       $.ajax(
       {
           url: 'https://api.github.com/emojis',
           async: false
       }).then(function(data)
       {
           window.emojis = Object.keys(data);
           window.emojiUrls = data;
       });

       //init emoji example
       $(".js-hint2emoji").summernote(
       {
           height: 100,
           toolbar: false,
           placeholder: 'type starting with : and any alphabet',
           hint:
           {
               match: /:([\-+\w]+)$/,
               search: function(keyword, callback)
               {
                   callback($.grep(emojis, function(item)
                   {
                       return item.indexOf(keyword) === 0;
                   }));
               },
               template: function(item)
               {
                   var content = emojiUrls[item];
                   return '<img src="' + content + '" width="20" /> :' + item + ':';
               },
               content: function(item)
               {
                   var url = emojiUrls[item];
                   if (url)
                   {
                       return $('<img />').attr('src', url).css('width', 20)[0];
                   }
                   return '';
               }
           }
       });

       //init mentions example
       $(".js-hint2mention").summernote(
       {
           height: 100,
           toolbar: false,
           placeholder: "type starting with @",
           hint:
           {
               mentions: ['jayden', 'sam', 'alvin', 'david'],
               match: /\B@(\w*)$/,
               search: function(keyword, callback)
               {
                   callback($.grep(this.mentions, function(item)
                   {
                       return item.indexOf(keyword) == 0;
                   }));
               },
               content: function(item)
               {
                   return '@' + item;
               }
           }
       });

   });

</script>
<!-- Search Selecet -->
<script>
   $(document).ready(function () {
       $(function () {
           $(".select2").select2();

           $(".select2-placeholder-multiple").select2({
               placeholder: "Select State",
           });
           $(".js-hide-search").select2({
               minimumResultsForSearch: 1 / 0,
           });
           $(".js-max-length").select2({
               maximumSelectionLength: 2,
               placeholder: "Select maximum 2 items",
           });
           $(".select2-placeholder").select2({
               placeholder: "Select a state",
               allowClear: true,
           });

           $(".js-select2-icons").select2({
               minimumResultsForSearch: 1 / 0,
               templateResult: icon,
               templateSelection: icon,
               escapeMarkup: function (elm) {
                   return elm;
               },
           });

           function icon(elm) {
               elm.element;
               return elm.id ? "<i class='" + $(elm.element).data("icon") + " mr-2'></i>" + elm.text : elm.text;
           }


             $("#vendor").select2({
               placeholder: "select...",
               ajax: {
                   type: "GET",
                   dataType: "json",
                   url: "AllVendors",
                   processResults: function (data) {
                       return {
                           results: $.map(data, function (obj, index) {

                               return { id: index, text: obj };
                           }),
                       };

                       console.log(data);
                   },
                  data: function (params) {


                  var query = {
                           search: params.term,
                       };


             $.ajax({
                             url: 'AllVendorsJS/'+params.term,
                             type:"GET",
                             dataType:"json",
                             beforeSend: function(){
                                 $('#loader').css("visibility", "visible");
                             },

                             success:function(data) {
                                         $('#vendor').empty();
                                 $.each(data, function(key, value){

                        $('#vendor').append('<option value="'+ key +'">' + value + '</option>');

                                 });
                                     var countryId = $('#vendor').val();
                     if(countryId) {
                         $.ajax({
                             url: 'AccountBalanceSOFilter/'+countryId,
                             type:"GET",
                             dataType:"json",
                             beforeSend: function(){
                                 $('#loader').css("visibility", "visible");
                             },

                             success:function(data) {
                                 $.each(data, function(key, value){


                   $('#AccountCredit').val(key);
                   $('#AccountCredit').val(value);
                                 });
                             },
                             complete: function(){
                                 $('#loader').css("visibility", "hidden");
                             }
                         });
                     } else {

                         $('select[name="state"]').empty();
                     }



                             },
                             complete: function(){
                                 $('#loader').css("visibility", "hidden");
                             }
                         });



       }
               },
           });

           $("#vendor").on("select2:select", function (e) {
               console.log("select done", e.params.data);
           });


                            $('#AccountCodeF').select2({
   placeholder: "select...",
   ajax: {
       type: "GET",
       dataType: 'json',
       url: 'AllVendors',
       processResults: function (data) {
         return {
           results: $.map(data, function(obj, index) {
             return { id: index, text: obj };
           })
         };

           	console.log(data);

       },
        data: function (params) {


                  var query = {
                           search: params.term,
                       };


             $.ajax({
                             url: 'AllVendorsJS/'+params.term,
                             type:"GET",
                             dataType:"json",
                             beforeSend: function(){
                                 $('#loader').css("visibility", "visible");
                             },

                             success:function(data) {
                                         $('#AccountCodeF').empty();
                                 $.each(data, function(key, value){

                        $('#AccountCodeF').append('<option value="'+ key +'">' + value + '</option>');

                                 });
                             },
                             complete: function(){
                                 $('#loader').css("visibility", "hidden");
                             }
                         });



       }
   }
   });


   $('#AccountCodeF').on('select2:select', function (e) {
   console.log("select done", e.params.data);
   });




                         $('#AccountCodee').select2({
   placeholder: "select...",
   ajax: {
       type: "GET",
       dataType: 'json',
       url: 'AllVendors',
       processResults: function (data) {
         return {
           results: $.map(data, function(obj, index) {
             return { id: index, text: obj };
           })
         };

           	console.log(data);

       },
        data: function (params) {


                  var query = {
                           search: params.term,
                       };


             $.ajax({
                             url: 'AllVendorsJS/'+params.term,
                             type:"GET",
                             dataType:"json",
                             beforeSend: function(){
                                 $('#loader').css("visibility", "visible");
                             },

                             success:function(data) {
                                         $('#AccountCodee').empty();
                                 $.each(data, function(key, value){

                        $('#AccountCodee').append('<option value="'+ key +'">' + value + '</option>');

                                 });
                             },
                             complete: function(){
                                 $('#loader').css("visibility", "hidden");
                             }
                         });



       }
   }
   });


   $('#AccountCodee').on('select2:select', function (e) {
   console.log("select done", e.params.data);
   });




       });
   });
</script>
<!-- Unit Code and Name -->
<script>
   function  UnitCodePurchh(r){

   var countryId = $('#UnitPurch'+r).val();
   var Pro = $('#Product'+r).val();
   var store = $('#StorePurch'+r).val();
   var code = $('#CodePurch'+r).val();
                     if(countryId) {
                         $.ajax({
                             url: 'UnitPurchasesFilter/'+countryId+'/'+Pro+'/'+store+'/'+code,
                             type:"GET",
                             dataType:"json",
                             beforeSend: function(){
                                 $('#loader').css("visibility", "visible");
                             },

                             success:function(data) {
                                 $.each(data, function(key, value){

                       $('#CodePurch'+r).val(data.code);
                       $('#UnitPurchName'+r).val(data.name);
                       $('#TaxRate'+r).val(data.rate);
                       $('#TaxType'+r).val(data.type);
                       $('#Price'+r).val(data.price);
                       $('#PurchTax'+r).val(data.tax);
                                         $('#UnitPriceOne'+r).val(data.priceOne);
                       $('#UnitPriceTwo'+r).val(data.priceTwo);
                       $('#UnitPriceThree'+r).val(data.priceThree);

                                 });

                                var TaxRate = $("#TaxRate"+r).val();
   var TaxType = $("#TaxType"+r).val();
   var Qty = $("#Qty"+r).val();
   var Price = $("#Price"+r).val();
   var Discount = $("#Discount"+r).val();
   var i = 0;
   var ii=0;
   var iii=0;

               var DIS = $("#DIS").val();

       if(parseFloat(DIS) == 0){

           Discount=$("#Discount"+r).val();

       }else{

           var d= Discount / 100 ;
           var Multi= parseFloat(Price) ;
          var dd= Multi * d;

        Discount=  dd;

       }
           var BFG =  parseFloat(Qty) * parseFloat(Discount) ;
             $("#TDiscPro"+r).val(parseFloat(BFG));

       if(TaxType == 1){
           //Precent

      var BF =  parseFloat(Qty) *  (parseFloat(Price)  -  parseFloat(Discount) );
      var BFF =  parseFloat(Qty) *  parseFloat(Price);

       $("#TotalBFTax"+r).val(parseFloat(BFF));

       i =    parseFloat(TaxRate)  / 100 ;

        ii=  parseFloat(BF) * parseFloat(i) ;

           $("#Tax"+r).val(parseFloat(ii));

       iii =  parseFloat(BF) + parseFloat(ii) ;

           $("#Total"+r).val(parseFloat(iii));

       }else if(TaxType == 2){
          //Number
        var BF =  parseFloat(Qty) *  (parseFloat(Price)  -  parseFloat(Discount) );
        var BFF =  parseFloat(Qty) *  parseFloat(Price);
         $("#TotalBFTax"+r).val(parseFloat(BFF));

            i =    parseFloat(TaxRate)   ;

           $("#Tax"+r).val(parseFloat(i));

       iii =  parseFloat(BF) + parseFloat(i) ;

           $("#Total"+r).val(parseFloat(iii));


       }


        var Total = $("#Total"+r).val();
        var TotalBFTax = $("#TotalBFTax"+r).val();
        var Tax = $("#Tax"+r).val();
        var UnitID = $("#UnitPurch"+r).val();
        var StorePurch = $("#StorePurch"+r).val();

       if(TaxRate == ''  || TaxType == ''  ||  Qty == '' || Price == '' || Discount == '' || Total == '' || TotalBFTax == '' || Tax == '' || UnitID == '' || StorePurch == ''){

        document.getElementById("AddBtnPur"+r).style.display = "none";
     }


       if(TaxRate != ''  && TaxType != ''  &&  Qty != '' && Price != '' && Discount != '' && Total != '' && TotalBFTax != '' && Tax != '' && UnitID != '' && StorePurch != ''){

        document.getElementById("AddBtnPur"+r).style.display = "block";
     }




                             },
                             complete: function(){
                                 $('#loader').css("visibility", "hidden");
                             }
                         });
                     } else {

                         $('select[name="state"]').empty();
                     }

   }
</script>
<!-- Unit Code and Name V -->
<script>
   function  UnitCodePurch(r){

   var countryId = $('#UnitPurch'+r).val();
   var Pro = $('#Product'+r).val();
   var store = $('#StorePurch'+r).val();
       var code = $('#CodePurch'+r).val();
                     if(countryId) {
                         $.ajax({
                             url: 'UnitPurchasesFilter/'+countryId+'/'+Pro+'/'+store+'/'+code,
                             type:"GET",
                             dataType:"json",
                             beforeSend: function(){
                                 $('#loader').css("visibility", "visible");
                             },

                             success:function(data) {
                                 $.each(data, function(key, value){

             $('#CodePurch'+r).val(data.code);
                       $('#UnitPurchName'+r).val(data.name);
                       $('#TaxRate'+r).val(data.rate);
                       $('#TaxType'+r).val(data.type);
                       $('#Price'+r).val(data.price);
                       $('#PurchTax'+r).val(data.tax);
                       $('#UnitPriceOne'+r).val(data.priceOne);
                       $('#UnitPriceTwo'+r).val(data.priceTwo);
                       $('#UnitPriceThree'+r).val(data.priceThree);

                                 });

                                var TaxRate = $("#TaxRate"+r).val();
   var TaxType = $("#TaxType"+r).val();
   var Qty = $("#Qty"+r).val();
   var Price = $("#Price"+r).val();
   var Discount = $("#Discount"+r).val();
   var i = 0;
   var ii=0;
   var iii=0;
        var DIS = $("#DIS").val();

       if(parseFloat(DIS) == 0){

           Discount=$("#Discount"+r).val();

       }else{

           var d= Discount / 100 ;
           var Multi= parseFloat(Price) ;
          var dd= Multi * d;

        Discount=  dd;

       }
           var BFG =  parseFloat(Qty) * parseFloat(Discount) ;
             $("#TDiscPro"+r).val(parseFloat(BFG));


       if(TaxType == 1){
           //Precent

        var BF =  parseFloat(Qty) *  (parseFloat(Price)  -  parseFloat(Discount) );
        var BFF =  parseFloat(Qty) *  parseFloat(Price);

       $("#TotalBFTax"+r).val(parseFloat(BFF));

       i =    parseFloat(TaxRate)  / 100 ;

        ii=  parseFloat(BF) * parseFloat(i) ;

           $("#Tax"+r).val(parseFloat(ii));

       iii =  parseFloat(BF) + parseFloat(ii) ;

           $("#Total"+r).val(parseFloat(iii));

       }else if(TaxType == 2){
          //Number
        var BF =  parseFloat(Qty) *  (parseFloat(Price)  -  parseFloat(Discount) );
        var BFF =  parseFloat(Qty) *  parseFloat(Price);
         $("#TotalBFTax"+r).val(parseFloat(BFF));

            i =    parseFloat(TaxRate)   ;

           $("#Tax"+r).val(parseFloat(i));

       iii =  parseFloat(BF) + parseFloat(i) ;

           $("#Total"+r).val(parseFloat(iii));


       }


        var Total = $("#Total"+r).val();
        var TotalBFTax = $("#TotalBFTax"+r).val();
        var Tax = $("#Tax"+r).val();
        var UnitID = $("#UnitPurch"+r).val();
        var StorePurch = $("#StorePurch"+r).val();

       if(TaxRate == ''  || TaxType == ''  ||  Qty == '' || Price == '' || Discount == '' || Total == '' || TotalBFTax == '' || Tax == '' || UnitID == '' || StorePurch == ''){

        document.getElementById("AddBtnPur"+r).style.display = "none";
     }


       if(TaxRate != ''  && TaxType != ''  &&  Qty != '' && Price != '' && Discount != '' && Total != '' && TotalBFTax != '' && Tax != '' && UnitID != '' && StorePurch != ''){

        document.getElementById("AddBtnPur"+r).style.display = "block";
     }




                             },
                             complete: function(){
                                 $('#loader').css("visibility", "hidden");
                             }
                         });
                     } else {

                         $('select[name="state"]').empty();
                     }

   }
</script>
<!-- Store Name -->
<script>
   function  StoreNamePurch(r){

   var countryId = $('#StorePurch'+r).val();
   var Pro = $('#Product'+r).val();
   var Un = $('#UnitPurch'+r).val();
   var CO = $('#CodePurch'+r).val();
                     if(countryId) {
                         $.ajax({
                             url: 'StoreNamePurchasesFilter/'+countryId,
                             type:"GET",
                             dataType:"json",
                             beforeSend: function(){
                                 $('#loader').css("visibility", "visible");
                             },

                             success:function(data) {
                                 $.each(data, function(key, value){

                       $('#StorePurchName'+r).val(data.name);


                             $.ajax({
                             url: 'StorePricePurchasesFilter',
                             type:"GET",
                             data:{
                                Product:Pro,
                                Store:countryId,
                                Unit:Un,
                                Code:CO,
                             },
                            dataType:"json",
                             beforeSend: function(){
                                 $('#loader').css("visibility", "visible");
                             },

                             success:function(data) {
                                 $.each(data, function(key, value){

                       $('#Price'+r).val(data.price);



                                 });

   var TaxRate = $("#TaxRate"+r).val();
   var TaxType = $("#TaxType"+r).val();
   var Qty = $("#Qty"+r).val();
   var Price = $("#Price"+r).val();
   var Discount = $("#Discount"+r).val();
   var i = 0;
   var ii=0;
   var iii=0;

                                         var DIS = $("#DIS").val();

       if(parseFloat(DIS) == 0){

           Discount=$("#Discount"+r).val();

       }else{

           var d= Discount / 100 ;
           var Multi= parseFloat(Price) ;
          var dd= Multi * d;

        Discount=  dd;

       }
           var BFG =  parseFloat(Qty) * parseFloat(Discount) ;
             $("#TDiscPro"+r).val(parseFloat(BFG));

       if(TaxType == 1){
           //Precent

        var BF =  parseFloat(Qty) *  (parseFloat(Price)  -  parseFloat(Discount) );
        var BFF =  parseFloat(Qty) *  parseFloat(Price);

       $("#TotalBFTax"+r).val(parseFloat(BFF));

       i =    parseFloat(TaxRate)  / 100 ;

        ii=  parseFloat(BF) * parseFloat(i) ;

           $("#Tax"+r).val(parseFloat(ii));

       iii =  parseFloat(BF) + parseFloat(ii) ;

           $("#Total"+r).val(parseFloat(iii));

       }else if(TaxType == 2){
          //Number
           var BF =  parseFloat(Qty) *  (parseFloat(Price)  -  parseFloat(Discount) );
           var BFF =  parseFloat(Qty) *  parseFloat(Price);
         $("#TotalBFTax"+r).val(parseFloat(BFF));

            i =    parseFloat(TaxRate)   ;

           $("#Tax"+r).val(parseFloat(i));

       iii =  parseFloat(BF) + parseFloat(i) ;

           $("#Total"+r).val(parseFloat(iii));


       }


        var Total = $("#Total"+r).val();
        var TotalBFTax = $("#TotalBFTax"+r).val();
        var Tax = $("#Tax"+r).val();
        var UnitID = $("#UnitPurch"+r).val();
        var StorePurch = $("#StorePurch"+r).val();

       if(TaxRate == ''  || TaxType == ''  ||  Qty == '' || Price == '' || Discount == '' || Total == '' || TotalBFTax == '' || Tax == '' || UnitID == '' || StorePurch == ''){

        document.getElementById("AddBtnPur"+r).style.display = "none";
     }


       if(TaxRate != ''  && TaxType != ''  &&  Qty != '' && Price != '' && Discount != '' && Total != '' && TotalBFTax != '' && Tax != '' && UnitID != '' && StorePurch != ''){

        document.getElementById("AddBtnPur"+r).style.display = "block";
     }




                             },
                             complete: function(){
                                 $('#loader').css("visibility", "hidden");
                             }
                         });



                                 });

   var TaxRate = $("#TaxRate"+r).val();
   var TaxType = $("#TaxType"+r).val();
   var Qty = $("#Qty"+r).val();
   var Price = $("#Price"+r).val();
   var Discount = $("#Discount"+r).val();
   var i = 0;
   var ii=0;
   var iii=0;
        var DIS = $("#DIS").val();

       if(parseFloat(DIS) == 0){

           Discount=$("#Discount"+r).val();

       }else{

           var d= Discount / 100 ;
           var Multi= parseFloat(Price) ;
          var dd= Multi * d;

        Discount=  dd;

       }
           var BFG =  parseFloat(Qty) * parseFloat(Discount) ;
             $("#TDiscPro"+r).val(parseFloat(BFG));

       if(TaxType == 1){
           //Precent

   var BF =  parseFloat(Qty) *  (parseFloat(Price)  -  parseFloat(Discount) );
   var BFF =  parseFloat(Qty) *  (parseFloat(Price)  );

       $("#TotalBFTax"+r).val(parseFloat(BFF));

       i =    parseFloat(TaxRate)  / 100 ;

        ii=  parseFloat(BF) * parseFloat(i) ;

           $("#Tax"+r).val(parseFloat(ii));

       iii =  parseFloat(BF) + parseFloat(ii) ;

           $("#Total"+r).val(parseFloat(iii));

       }else if(TaxType == 2){
          //Number
      var BF =  parseFloat(Qty) *  (parseFloat(Price)  -  parseFloat(Discount) );
      var BFF =  parseFloat(Qty) *  (parseFloat(Price)   );
         $("#TotalBFTax"+r).val(parseFloat(BFF));

            i =    parseFloat(TaxRate)   ;

           $("#Tax"+r).val(parseFloat(i));

       iii =  parseFloat(BF) + parseFloat(i) ;

           $("#Total"+r).val(parseFloat(iii));


       }


        var Total = $("#Total"+r).val();
        var TotalBFTax = $("#TotalBFTax"+r).val();
        var Tax = $("#Tax"+r).val();
        var UnitID = $("#UnitPurch"+r).val();
        var StorePurch = $("#StorePurch"+r).val();

       if(TaxRate == ''  || TaxType == ''  ||  Qty == '' || Price == '' || Discount == '' || Total == '' || TotalBFTax == '' || Tax == '' || UnitID == '' || StorePurch == ''){

        document.getElementById("AddBtnPur"+r).style.display = "none";
     }


       if(TaxRate != ''  && TaxType != ''  &&  Qty != '' && Price != '' && Discount != '' && Total != '' && TotalBFTax != '' && Tax != '' && UnitID != '' && StorePurch != ''){

        document.getElementById("AddBtnPur"+r).style.display = "block";
     }




                             },
                             complete: function(){
                                 $('#loader').css("visibility", "hidden");
                             }
                         });
                     } else {

                         $('select[name="state"]').empty();
                     }

   }
</script>
<!-- Account Balance -->
<script>
   $(document).ready(function() {

                  $('#vendor').on('change', function(){
                      var countryId = $(this).val();
                      if(countryId) {
                          $.ajax({
                              url: 'AccountBalanceFilter/'+countryId,
                              type:"GET",
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },

                              success:function(data) {
                                  $.each(data, function(key, value){


                       $('#AccountCredit').val(parseFloat(key).toFixed(2));
                    $('#AccountCredit').val(parseFloat(value).toFixed(2));
                                  });
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });
                      } else {

                          $('select[name="state"]').empty();
                      }

                  });

              });


     $(document).ready(function() {

                      var countryId = $('#vendor').val();
                      if(countryId) {
                          $.ajax({
                              url: 'AccountBalanceFilter/'+countryId,
                              type:"GET",
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },

                              success:function(data) {
                                  $.each(data, function(key, value){



                       $('#AccountCredit').val(parseFloat(key).toFixed(2));
                    $('#AccountCredit').val(parseFloat(value).toFixed(2));
                                  });
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });
                      } else {

                          $('select[name="state"]').empty();
                      }

     });


</script>
<!--  Filter Products -->
<script>
   $(document).ready(function(){

    fetch_customer_data();

    function fetch_customer_data(search = '',store='',vendor='',Brand='',Group='')
    {
     $.ajax({
      url:'PurchacesProductsFilter',
      method:'GET',
      data:{search:search,store:store,vendor:vendor,Brand:Brand,Group:Group},
      dataType:'json',
      success:function(data)
      {
       $('.Data').html(data.table_data);
             $("#Data").show();
      }
     })
    }

   $(document).on('keyup', '#search', function(){
     var search = $(this).val();
     var store = $('#store').val();
     var vendor = $('#vendor').val();
     var Brand = $('#Brandd').val();
     var Group = $('#Groupp').val();
       if(search == ''){
            $("#Data").hide();
          }
     fetch_customer_data(search,store,vendor,Brand,Group);
    });


    $(document).on('change', '#store', function(){
     var store = $(this).val();
     var search = $('#search').val();
        var vendor = $('#vendor').val();
       var Brand = $('#Brandd').val();
     var Group = $('#Groupp').val();
     fetch_customer_data(search,store,vendor,Brand,Group);
    });


        $(document).on('change', '#vendor', function(){
     var vendor = $(this).val();
     var search = $('#search').val();
      var store = $('#store').val();
    var Brand = $('#Brandd').val();
     var Group = $('#Groupp').val();
     fetch_customer_data(search,store,vendor,Brand,Group);
    });

            $(document).on('change', '#Brandd', function(){
     var Brand = $(this).val();
     var search = $('#search').val();
      var store = $('#store').val();
    var vendor = $('#vendor').val();
     var Group = $('#Groupp').val();
     fetch_customer_data(search,store,vendor,Brand,Group);
    });


            $(document).on('change', '#Groupp', function(){
     var Group = $(this).val();
     var search = $('#search').val();
      var store = $('#store').val();
    var Brand = $('#Brandd').val();
     var vendor = $('#vendor').val();
     fetch_customer_data(search,store,vendor,Brand,Group);
    });



   });
</script>
<!-- Filter Search Code -->
<script>
   $(document).ready(function(){

    fetch_customer_data();

    function fetch_customer_data(search = '',store='',vendor='',Brand='',Group='')
    {
     $.ajax({
      url:'PurchacesProductsSearchCodeFilter',
      method:'GET',
      data:{search:search,store:store,vendor:vendor,Brand:Brand,Group:Group},
      dataType:'json',
      success:function(data)
      {
       $('.Data').html(data.table_data);
             $("#Data").show();
      }
     })
    }

   $(document).on('keyup', '#SearchCode', function(){
     var search = $(this).val();
     var store = $('#store').val();
     var vendor = $('#vendor').val();
     var Brand = $('#Brandd').val();
     var Group = $('#Groupp').val();
       if(search == ''){
            $("#Data").hide();
          }
     fetch_customer_data(search,store,vendor,Brand,Group);
    });



   });
</script>
<!-- Add Products -->
<script>
   function Fun(r) {

             var P_Ar_Name = $("#P_Ar_Name"+r).val();
             var P_En_Name= $("#P_En_Name"+r).val();
             var Product = $("#Product"+r).val();
             var UnitID = $("#UnitPurch"+r).val();
             var UnitName = $("#UnitPurchName"+r).val();
             var Qty = $("#Qty"+r).val();
             var Barcode = $("#CodePurch"+r).val();
             var Price = $("#Price"+r).val();
             var Total = $("#Total"+r).val();
             var ExpDate = $("#ExpDate"+r).val();
             var PurchTax = $("#PurchTax"+r).val();
             var TotalTax = $("#Tax"+r).val();
             var Discount = $("#Discount"+r).val();
             var TDiscPro = $("#TDiscPro"+r).val();
             var TotalBFTax = $("#TotalBFTax"+r).val();
             var StorePurch = $("#StorePurch"+r).val();
             var StorePurchName = $("#StorePurchName"+r).val();
             var PurchTax = $("#PurchTax"+r).val();
   var Price_Sale = $("#Price_Sale"+r).val();
   var TaxRate = $("#TaxRate"+r).val();
             var TaxType = $("#TaxType"+r).val();
       var Exp='';
        var ED = $("#ED").val();
        var DISCSHOW = $("#DISCSHOW").val();
        var TOTBFSHOW = $("#TOTBFSHOW").val();
        var TAXSHOW = $("#TAXSHOW").val();
        var STORESHOW = $("#STORESHOW").val();


         if(ED == 1){

         Exp = "<td><input type='hidden' name='Exp_Date[]' value='"+ExpDate+"'>" + ExpDate + "</td>";

       }else{

           Exp = "<input type='hidden' name='Exp_Date[]' value='"+ExpDate+"'>";
       }

          if(DISCSHOW == 1){

       var  DISCSHOWW = "";

       }else{

             var  DISCSHOWW = "none";
       }


          if(TOTBFSHOW == 1){

        var TOTBFSHOWW = "";

       }else{

            var TOTBFSHOWW  = "none";
       }



          if(TAXSHOW == 1){

      var   TAXSHOWW = "";

       }else{

            var   TAXSHOWW = "none";
       }



          if(STORESHOW == 1){

        var STORESHOWW = "";

       }else{

             var STORESHOWW  = "none";
       }




             document.getElementById("AddBtnPur"+r).style.display = "none";
             document.getElementById("Row"+r).style.display = "none";

               var LANG = $("#LANG").val();
   if(LANG == 'ar' ){
          var Nemo = P_Ar_Name ;
          }else{
             var Nemo = P_En_Name ;
          }



             var markup = "<tr><td><input type='hidden' name='P_Ar_Name[]' value='"+P_Ar_Name+"'><input type='hidden' name='P_En_Name[]' value='"+P_En_Name+"'>" + Nemo + "</td><td><input type='hidden' name='Unit[]' value='"+UnitID+"'>" + UnitName + "</td><td><input type='hidden' name='P_Code[]' value='"+Barcode+"'>" + Barcode + "</td><td><input class='Qun form-control' id='QuntD"+r+"' type='number' step='any' name='Qty[]' value='"+Qty+"' onkeyup='TotD("+r+")' onclick='TotD("+r+")' ></td><td><input type='number' id='PriD"+r+"' step='any' name='Price[]' value='"+Price+"' class='form-control' onkeyup='TotD("+r+")' onclick='TotD("+r+")' ></td><td style='display:"+DISCSHOWW+"' ><input class='Disc form-control' id='DiscD"+r+"' type='number' step='any' name='Discount[]' value='"+Discount+"' onkeyup='TotD("+r+")' onclick='TotD("+r+")' ></td><td style='display:"+TOTBFSHOWW+"'><input class='TotalBFTax form-control' id='TotBFTaxD"+r+"' type='number' step='any' disabled  value='"+TotalBFTax+"'><input type='hidden'  id='TotBFTaxDHide"+r+"' name='TotalBFTax[]'  value='"+TotalBFTax+"'></td><td style='display:"+TAXSHOWW+"'><input class='TotalTax form-control' id='TaxD"+r+"' disabled  type='number' step='any' value='"+TotalTax+"'><input type='hidden' id='TotalTaxVVHide"+r+"'  name='TotalTax[]'  value='"+TotalTax+"'></td><td><input class='Tot form-control' type='number' step='any' id='TotD"+r+"' disabled   name='Total[]' value='"+Total+"'><input type='hidden' id='TotDHide"+r+"' name='Total[]'  value='"+Total+"'></td><td style='display:"+STORESHOWW+"'><input  type='hidden' name='StorePurch[]' value='"+StorePurch+"'>" + StorePurchName + "</td>"+Exp+"<td><button id='DelAssem' type='button' class='btn btn-default'><i class='fal fa-trash'></i></button><input type='hidden' name='Product[]' value='"+Product+"'><input type='hidden' name='VOne[]' value=''><input type='hidden' name='VTwo[]' value=''></td><input type='hidden' name='V_Name[]' value=''><input type='hidden' class='TDISCOOO' id='TDiscProEdit"+r+"' name='TDiscPro[]' value='"+TDiscPro+"'><input type='hidden' name='VV_Name[]' value=''><input type='hidden' name='PurchTax[]' value='"+PurchTax+"'><input id='TaxRateD"+r+"' type='hidden'  value='"+TaxRate+"'><input type='hidden' id='TaxTypeD"+r+"'  value='"+TaxType+"'><input type='hidden' name='Price_Sale[]' value='"+Price_Sale+"'><input type='hidden' id='Enough"+r+"' name='AllEnough[]' class='ENO'  value='0'></tr>";



             $("#data-dt").append(markup);
       $("#Data").hide();
      $.fn.rowCount = function() {
     return $('tr', $(this).find('tbody')).length;
   };

   var rowctr = $('#dt').rowCount();

    var sumQ = 0;
   $('.Qun').each(function(){
   sumQ += parseFloat($(this).val());
   });

    var sumT = 0;
   $('.Tot').each(function(){
   sumT += parseFloat($(this).val());
   });

        var sumD = 0;
   $('.Disc').each(function(){
   sumD += parseFloat($(this).val());
   });

               var sumTTD = 0;
   $('.TDISCOOO').each(function(){
   sumTTD += parseFloat($(this).val());
   });


           var sumBF = 0;
   $('.TotalBFTax').each(function(){
   sumBF += parseFloat($(this).val());
   });

           var sumTax = 0;
   $('.TotalTax').each(function(){
   sumTax += parseFloat($(this).val());
   });


   $('#Product_Numbers').val(parseFloat(rowctr));
   $('#Product_NumbersHide').val(parseFloat(rowctr));

   $('#Total_Qty').val(parseFloat(sumQ));
   $('#Total_QtyHide').val(parseFloat(sumQ));

   $('#Total_Price').val(parseFloat(sumT) + parseFloat(sumTTD) -  parseFloat(sumTax));
   $('#Total_PriceHide').val(parseFloat(sumT) + parseFloat(sumTTD) -  parseFloat(sumTax));
   $('#Total_PriceW').val(parseFloat(sumT));
   $('#Total_Bill').val(parseFloat(sumT));


           $('#Total_DiscountTT').val(parseFloat(sumTTD).toFixed(2));
   $('#Total_DiscountHideTT').val(parseFloat(sumTTD).toFixed(2));

       $('#Total_Discount').val(parseFloat(sumD).toFixed(2));
   $('#Total_DiscountHide').val(parseFloat(sumD).toFixed(2));

          $('#Total_Bf_Taxes').val(parseFloat(sumBF)+parseFloat(sumTTD));
   $('#Total_Bf_TaxesHide').val(parseFloat(sumBF)+parseFloat(sumTTD));

          $('#Total_Taxes').val(parseFloat(sumTax).toFixed(2));
   $('#Total_TaxesHide').val(parseFloat(sumTax).toFixed(2));


       if(rowctr == 0){
        document.getElementById("Submit").style.display = "none";

       }else{

        document.getElementById("Submit").style.display = "block";
       }

            var Pay =$('#Payment_Method').val();
        var Stat =$('#Status').val();
        var paid =$('#paid').val();

      if(Pay == 'Later' &&  Stat == 0 ){

             if(paid == 0 || paid < 0){
        document.getElementById("Submit").style.display = "none";
             }else{

           if(rowctr == 0){
        document.getElementById("Submit").style.display = "none";

       }else{

        document.getElementById("Submit").style.display = "block";
       }



             }

       }else{

       if(rowctr == 0){
        document.getElementById("Submit").style.display = "none";

       }else{

        document.getElementById("Submit").style.display = "block";
       }
       }


                             var Pay =$('#Payment_Method').val();
        var Stat =$('#Status').val();
        var paid =$('#paid').val();
        var resdiual =$('#resdiual').val();
        var total =$('#Total_PriceHide').val();
        var totalDisc =$('#Total_DiscountHideTT').val();
        var totalTax =$('#Total_TaxesHide').val();


           var RR= (parseFloat(total) + parseFloat(totalTax) ) - (parseFloat(resdiual) + parseFloat(totalDisc));
           var RRR= parseFloat(total) - (parseFloat(resdiual) + parseFloat(totalDisc));
          $('#TAFterD').val(parseFloat(RRR).toFixed(2));
         $('#paid').val(parseFloat(RR).toFixed(2));


               var Pay =$('#Payment_Method').val();
      if(Pay == 'Later'){


                   $('#paid').val('');

       }



        $('#data-dt').on('click', '#DelAssem', function(e){
                $(this).closest('tr').remove();


      $.fn.rowCount = function() {
     return $('tr', $(this).find('tbody')).length;
   };

   var rowctr = $('#dt').rowCount();

    var sumQ = 0;
   $('.Qun').each(function(){
   sumQ += parseFloat($(this).val());
   });

    var sumT = 0;
   $('.Tot').each(function(){
   sumT += parseFloat($(this).val());
   });

        var sumD = 0;
   $('.Disc').each(function(){
   sumD += parseFloat($(this).val());
   });


           var sumBF = 0;
   $('.TotalBFTax').each(function(){
   sumBF += parseFloat($(this).val());
   });

           var sumTax = 0;
   $('.TotalTax').each(function(){
   sumTax += parseFloat($(this).val());
   });


                 var sumTTD = 0;
   $('.TDISCOOO').each(function(){
   sumTTD += parseFloat($(this).val());
   });

   $('#Product_Numbers').val(parseFloat(rowctr));
   $('#Product_NumbersHide').val(parseFloat(rowctr));

   $('#Total_Qty').val(parseFloat(sumQ));
   $('#Total_QtyHide').val(parseFloat(sumQ));

   $('#Total_Price').val(parseFloat(sumT) + parseFloat(sumTTD) -  parseFloat(sumTax));
   $('#Total_PriceHide').val(parseFloat(sumT) + parseFloat(sumTTD) -  parseFloat(sumTax));
    $('#Total_PriceW').val(parseFloat(sumT));
       $('#Total_Bill').val(parseFloat(sumT));

           $('#Total_DiscountTT').val(parseFloat(sumTTD).toFixed(2));
   $('#Total_DiscountHideTT').val(parseFloat(sumTTD).toFixed(2));

       $('#Total_Discount').val(parseFloat(sumD).toFixed(2));
   $('#Total_DiscountHide').val(parseFloat(sumD).toFixed(2));

          $('#Total_Bf_Taxes').val(parseFloat(sumBF)+parseFloat(sumTTD));
   $('#Total_Bf_TaxesHide').val(parseFloat(sumBF)+parseFloat(sumTTD));

          $('#Total_Taxes').val(parseFloat(sumTax).toFixed(2));
   $('#Total_TaxesHide').val(parseFloat(sumTax).toFixed(2));



       if(rowctr == 0){
        document.getElementById("Submit").style.display = "none";

       }else{

        document.getElementById("Submit").style.display = "block";
       }

                  var Pay =$('#Payment_Method').val();
        var Stat =$('#Status').val();
        var paid =$('#paid').val();

      if(Pay == 'Later' &&  Stat == 0 ){

             if(paid == 0 || paid < 0){
        document.getElementById("Submit").style.display = "none";
             }else{

           if(rowctr == 0){
        document.getElementById("Submit").style.display = "none";

       }else{

        document.getElementById("Submit").style.display = "block";
       }



             }

       }else{

       if(rowctr == 0){
        document.getElementById("Submit").style.display = "none";

       }else{

        document.getElementById("Submit").style.display = "block";
       }
       }



                             var Pay =$('#Payment_Method').val();
        var Stat =$('#Status').val();
        var paid =$('#paid').val();
        var resdiual =$('#resdiual').val();
        var total =$('#Total_PriceHide').val();
          var totalDisc =$('#Total_DiscountHideTT').val();
        var totalTax =$('#Total_TaxesHide').val();


        var RR= (parseFloat(total) + parseFloat(totalTax) ) - (parseFloat(resdiual) + parseFloat(totalDisc));
             var RRR= parseFloat(total) - (parseFloat(resdiual) + parseFloat(totalDisc));
          $('#TAFterD').val(parseFloat(RRR).toFixed(2));
         $('#paid').val(parseFloat(RR).toFixed(2));


               var Pay =$('#Payment_Method').val();
      if(Pay == 'Later'){


                   $('#paid').val('');

       }

          var EN = 0;
    var Total_PriceW =$('#Total_PriceW').val();

  $('.ENO').each(function(){
   EN += parseFloat($(this).val());
   });
 if(EN == 0){

             if(Total_PriceW == 'NaN'){
                   document.getElementById("Submit").style.display = "none";
                    }else{
        document.getElementById("Submit").style.display = "block";
             }

       }else{

        document.getElementById("Submit").style.display = "none";
       }



                    })


     }
</script>
<!-- Add Vira One -->
<script>
   function FunV(r) {

                      $.ajax({
                              url: 'ViraFilterPurchases/'+r,
                              type:"GET",
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },

                              success:function(data) {
                                  $.each(data, function(key, value){

             var P_Ar_Name = $("#P_Ar_Name"+r).val();
             var P_En_Name= $("#P_En_Name"+r).val();
             var Product = $("#Product"+r).val();
             var UnitID = $("#UnitPurch"+r).val();
             var UnitName = $("#UnitPurchName"+r).val();
             var Qty = $("#Qty"+r).val();
             var Barcode = Math.floor(Math.random() * 10000);
             var Price = $("#Price"+r).val();
             var Total = $("#Total"+r).val();
             var ExpDate = $("#ExpDate"+r).val();
             var PurchTax = $("#PurchTax"+r).val();
             var TotalTax = $("#Tax"+r).val();
             var Discount = $("#Discount"+r).val();
            var TDiscPro = $("#TDiscPro"+r).val();
             var TotalBFTax = $("#TotalBFTax"+r).val();
             var StorePurch = $("#StorePurch"+r).val();
             var StorePurchName = $("#StorePurchName"+r).val();
             var TaxRate = $("#TaxRate"+r).val();
             var Price_Sale = $("#Price_Sale"+r).val();
             var TaxType = $("#TaxType"+r).val();
                var PurchTax = $("#PurchTax"+r).val();

           var Exp='';
        var ED = $("#ED").val();

     var DISCSHOW = $("#DISCSHOW").val();
        var TOTBFSHOW = $("#TOTBFSHOW").val();
        var TAXSHOW = $("#TAXSHOW").val();
        var STORESHOW = $("#STORESHOW").val();

         if(DISCSHOW == 1){

       var  DISCSHOWW = "";

       }else{

             var  DISCSHOWW = "none";
       }


          if(TOTBFSHOW == 1){

        var TOTBFSHOWW = "";

       }else{

            var TOTBFSHOWW  = "none";
       }



          if(TAXSHOW == 1){

      var   TAXSHOWW = "";

       }else{

            var   TAXSHOWW = "none";
       }



          if(STORESHOW == 1){

        var STORESHOWW = "";

       }else{

             var STORESHOWW  = "none";
       }


         if(ED == 1){

         Exp = "<td><input type='hidden' name='Exp_Date[]' value='"+ExpDate+"'>" + ExpDate + "</td>";

       }else{

           Exp = "<input type='hidden' name='Exp_Date[]' value='"+ExpDate+"'>";
       }




             document.getElementById("AddBtnPur"+r).style.display = "none";
             document.getElementById("Row"+r).style.display = "none";

                      var LANG = $("#LANG").val();
   if(LANG == 'ar' ){
          var Nemo = P_Ar_Name ;
          var PNNN='سعر البيع';
          }else{
             var Nemo = P_En_Name ;
                   var PNNN='Sales Price';
          }




       var markup = "<tr><td><input type='hidden' name='P_Ar_Name[]' value='"+P_Ar_Name+"'><input type='hidden' name='P_En_Name[]' value='"+P_En_Name+"'>" + Nemo + " ("+value+") </td><td><input type='hidden' name='Unit[]' value='"+UnitID+"'>" + UnitName + "<br><label>"+PNNN+"</label><input type='number' name='Price_Sale[]' value='"+Price_Sale+"'></td><td><input type='text' name='P_Code[]' value='"+Barcode+"' class='form-control'></td><td><input class='Qun  form-control' type='number' id='QuntityV"+key+"' value='"+Qty+"' step='any' name='Qty[]' onkeyup='TotV("+key+")' onclick='TotV("+key+")'></td> <td><input type='number' name='Price[]' value='"+Price+"' onkeyup='TotV("+key+")' onclick='TotV("+key+")'  id='PriceV"+key+"' step='any' class='form-control' ></td><td style='display:"+DISCSHOWW+"'><input type='number' name='Discount[]' value='"+Discount+"' onkeyup='TotV("+key+")' onclick='TotV("+key+")'  id='DiscountV"+key+"' step='any' class='form-control' ></td><td style='display:"+TOTBFSHOWW+"'><input class='TotalBFTax form-control' type='text' id='TotalBFV"+key+"' value='"+TotalBFTax+"'  disabled><input type='hidden' id='TotalBFVHide"+key+"' name='TotalBFTax[]' value='"+TotalBFTax+"'></td><td style='display:"+TAXSHOWW+"'><input class='TotalTax form-control' type='text' id='TotalTax"+key+"' value='"+TotalTax+"'  disabled><input type='hidden' id='TotalTaxVVHide"+key+"'  value='"+TotalTax+"' name='TotalTax[]'></td><td><input class='Tot form-control' type='text' id='TotalV"+key+"' value='"+Total+"'  disabled><input type='hidden' id='TotalVHide"+key+"' name='Total[]' value='"+Total+"'></td><td style='display:"+STORESHOWW+"'><input  type='hidden' name='StorePurch[]' value='"+StorePurch+"'>" + StorePurchName + "</td>"+Exp+"<td><button id='DelAssem' type='button' class='btn btn-default'><i class='fal fa-trash'></i></button><input type='hidden' name='Product[]' value='"+Product+"'><input type='hidden' name='VOne[]' value='"+key+"'><input type='hidden' name='VTwo[]' value=''></td><input type='hidden' name='V_Name[]' value='"+value+"'><input type='hidden' name='VV_Name[]' value=''><input type='hidden'  value='"+TaxRate+"' id='RateV"+key+"'><input type='hidden'  value='"+TaxType+"' id='TypeV"+key+"'><input type='hidden' name='PurchTax[]' value='"+PurchTax+"'><input type='hidden' class='TDISCOOO' id='TDiscProEdit"+key+"' name='TDiscPro[]' value='"+TDiscPro+"'><input type='hidden' id='Enough"+key+"' name='AllEnough[]' class='ENO'  value='0'></tr>";

             $("#data-dt").append(markup);
                  $("#Data").hide();
   $.fn.rowCount = function() {
     return $('tr', $(this).find('tbody')).length;
   };

   var rowctr = $('#dt').rowCount();

    var sumQ = 0;
   $('.Qun').each(function(){
   sumQ += parseFloat($(this).val());
   });

    var sumT = 0;
   $('.Tot').each(function(){
   sumT += parseFloat($(this).val());
   });

        var sumD = 0;
   $('.Disc').each(function(){
   sumD += parseFloat($(this).val());
   });


           var sumBF = 0;
   $('.TotalBFTax').each(function(){
   sumBF += parseFloat($(this).val());
   });

           var sumTax = 0;
   $('.TotalTax').each(function(){
   sumTax += parseFloat($(this).val());
   });


                  var sumTTD = 0;
   $('.TDISCOOO').each(function(){
   sumTTD += parseFloat($(this).val());
   });
   $('#Product_Numbers').val(parseFloat(rowctr));
   $('#Product_NumbersHide').val(parseFloat(rowctr));

   $('#Total_Qty').val(parseFloat(sumQ));
   $('#Total_QtyHide').val(parseFloat(sumQ));

   $('#Total_Price').val(parseFloat(sumT) + parseFloat(sumTTD) -  parseFloat(sumTax));
   $('#Total_PriceHide').val(parseFloat(sumT) + parseFloat(sumTTD) -  parseFloat(sumTax));
   $('#Total_PriceW').val(parseFloat(sumT));
       $('#Total_Bill').val(parseFloat(sumT));

           $('#Total_DiscountTT').val(parseFloat(sumTTD).toFixed(2));
   $('#Total_DiscountHideTT').val(parseFloat(sumTTD).toFixed(2));

       $('#Total_Discount').val(parseFloat(sumD).toFixed(2));
   $('#Total_DiscountHide').val(parseFloat(sumD).toFixed(2));

          $('#Total_Bf_Taxes').val(parseFloat(sumBF)+parseFloat(sumTTD));
   $('#Total_Bf_TaxesHide').val(parseFloat(sumBF)+parseFloat(sumTTD));

          $('#Total_Taxes').val(parseFloat(sumTax).toFixed(2));
   $('#Total_TaxesHide').val(parseFloat(sumTax).toFixed(2));



       if(rowctr == 0){
        document.getElementById("Submit").style.display = "none";

       }else{

        document.getElementById("Submit").style.display = "block";
       }

              var Pay =$('#Payment_Method').val();
        var Stat =$('#Status').val();
        var paid =$('#paid').val();

     if(Pay == 'Later' &&  Stat == 0 ){

             if(paid == 0 || paid < 0){
        document.getElementById("Submit").style.display = "none";
             }else{

           if(rowctr == 0){
        document.getElementById("Submit").style.display = "none";

       }else{

        document.getElementById("Submit").style.display = "block";
       }



             }

       }else{

       if(rowctr == 0){
        document.getElementById("Submit").style.display = "none";

       }else{

        document.getElementById("Submit").style.display = "block";
       }
       }


                                    var Pay =$('#Payment_Method').val();
        var Stat =$('#Status').val();
        var paid =$('#paid').val();
        var resdiual =$('#resdiual').val();
        var total =$('#Total_PriceHide').val();
       var totalDisc =$('#Total_DiscountHideTT').val();

         var totalTax =$('#Total_TaxesHide').val();


           var RR= (parseFloat(total) + parseFloat(totalTax) ) - (parseFloat(resdiual) + parseFloat(totalDisc));
             var RRR= parseFloat(total) - (parseFloat(resdiual) + parseFloat(totalDisc));
          $('#TAFterD').val(parseFloat(RRR).toFixed(2));
         $('#paid').val(parseFloat(RR).toFixed(2));




                                  });
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });

               var Pay =$('#Payment_Method').val();
      if(Pay == 'Later'){


                   $('#paid').val('');

       }


        $('#data-dt').on('click', '#DelAssem', function(e){
                $(this).closest('tr').remove();


      $.fn.rowCount = function() {
     return $('tr', $(this).find('tbody')).length;
   };

   var rowctr = $('#dt').rowCount();

    var sumQ = 0;
   $('.Qun').each(function(){
   sumQ += parseFloat($(this).val());
   });

    var sumT = 0;
   $('.Tot').each(function(){
   sumT += parseFloat($(this).val());
   });

        var sumD = 0;
   $('.Disc').each(function(){
   sumD += parseFloat($(this).val());
   });


           var sumBF = 0;
   $('.TotalBFTax').each(function(){
   sumBF += parseFloat($(this).val());
   });

           var sumTax = 0;
   $('.TotalTax').each(function(){
   sumTax += parseFloat($(this).val());
   });


                 var sumTTD = 0;
   $('.TDISCOOO').each(function(){
   sumTTD += parseFloat($(this).val());
   });
   $('#Product_Numbers').val(parseFloat(rowctr));
   $('#Product_NumbersHide').val(parseFloat(rowctr));

   $('#Total_Qty').val(parseFloat(sumQ));
   $('#Total_QtyHide').val(parseFloat(sumQ));

   $('#Total_Price').val(parseFloat(sumT) + parseFloat(sumTTD) -  parseFloat(sumTax));
   $('#Total_PriceHide').val(parseFloat(sumT) + parseFloat(sumTTD) -  parseFloat(sumTax));
   $('#Total_PriceW').val(parseFloat(sumT));
              $('#Total_Bill').val(parseFloat(sumT));

           $('#Total_DiscountTT').val(parseFloat(sumTTD).toFixed(2));
   $('#Total_DiscountHideTT').val(parseFloat(sumTTD).toFixed(2));

       $('#Total_Discount').val(parseFloat(sumD).toFixed(2));
   $('#Total_DiscountHide').val(parseFloat(sumD).toFixed(2));

          $('#Total_Bf_Taxes').val(parseFloat(sumBF)+parseFloat(sumTTD));
   $('#Total_Bf_TaxesHide').val(parseFloat(sumBF)+parseFloat(sumTTD));

          $('#Total_Taxes').val(parseFloat(sumTax).toFixed(2));
   $('#Total_TaxesHide').val(parseFloat(sumTax).toFixed(2));



       if(rowctr == 0){
        document.getElementById("Submit").style.display = "none";

       }else{

        document.getElementById("Submit").style.display = "block";
       }


          var Pay =$('#Payment_Method').val();
        var Stat =$('#Status').val();
        var paid =$('#paid').val();

     if(Pay == 'Later' &&  Stat == 0 ){

             if(paid == 0 || paid < 0){
        document.getElementById("Submit").style.display = "none";
             }else{

           if(rowctr == 0){
        document.getElementById("Submit").style.display = "none";

       }else{

        document.getElementById("Submit").style.display = "block";
       }



             }

       }else{

       if(rowctr == 0){
        document.getElementById("Submit").style.display = "none";

       }else{

        document.getElementById("Submit").style.display = "block";
       }
       }

                           var Pay =$('#Payment_Method').val();
        var Stat =$('#Status').val();
        var paid =$('#paid').val();
        var resdiual =$('#resdiual').val();
        var total =$('#Total_PriceHide').val();
         var totalDisc =$('#Total_DiscountHideTT').val();

         var totalTax =$('#Total_TaxesHide').val();


          var RR= (parseFloat(total) + parseFloat(totalTax) ) - (parseFloat(resdiual) + parseFloat(totalDisc));
             var RRR= parseFloat(total) - (parseFloat(resdiual) + parseFloat(totalDisc));
          $('#TAFterD').val(parseFloat(RRR).toFixed(2));
         $('#paid').val(parseFloat(RR).toFixed(2));


               var Pay =$('#Payment_Method').val();
      if(Pay == 'Later'){


                   $('#paid').val('');

       }

          var EN = 0;
    var Total_PriceW =$('#Total_PriceW').val();

  $('.ENO').each(function(){
   EN += parseFloat($(this).val());
   });
 if(EN == 0){

             if(Total_PriceW == 'NaN'){
                   document.getElementById("Submit").style.display = "none";
                    }else{
        document.getElementById("Submit").style.display = "block";
             }

       }else{

        document.getElementById("Submit").style.display = "none";
       }



                    })


     }
</script>
<!-- Add Vira Two -->
<script>
   function FunVV(r) {
                      $.ajax({
                              url: 'ViraNamePurchases/'+r,
                              type:"GET",
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },

                              success:function(data) {
                                  $.each(data, function(key,value){


            var P_Ar_Name = $("#P_Ar_Name"+r).val();
             var P_En_Name= $("#P_En_Name"+r).val();
             var Product = $("#Product"+r).val();
             var UnitID = $("#UnitPurch"+r).val();
             var UnitName = $("#UnitPurchName"+r).val();
             var Qty = $("#Qty"+r).val();
             var Barcode = Math.floor(Math.random() * 10000);
             var Price = $("#Price"+r).val();
             var Total = $("#Total"+r).val();
             var ExpDate = $("#ExpDate"+r).val();
             var PurchTax = $("#PurchTax"+r).val();
             var TotalTax = $("#Tax"+r).val();
             var Discount = $("#Discount"+r).val();
                                           var TDiscPro = $("#TDiscPro"+r).val();
             var TotalBFTax = $("#TotalBFTax"+r).val();
             var StorePurch = $("#StorePurch"+r).val();
             var StorePurchName = $("#StorePurchName"+r).val();
             var TaxRate = $("#TaxRate"+r).val();
             var TaxType = $("#TaxType"+r).val();
                   var PurchTax = $("#PurchTax"+r).val();
   var Price_Sale = $("#Price_Sale"+r).val();

            var Exp='';
        var ED = $("#ED").val();
         var DISCSHOW = $("#DISCSHOW").val();
        var TOTBFSHOW = $("#TOTBFSHOW").val();
        var TAXSHOW = $("#TAXSHOW").val();
        var STORESHOW = $("#STORESHOW").val();

         if(DISCSHOW == 1){

       var  DISCSHOWW = "";

       }else{

             var  DISCSHOWW = "none";
       }


          if(TOTBFSHOW == 1){

        var TOTBFSHOWW = "";

       }else{

            var TOTBFSHOWW  = "none";
       }



          if(TAXSHOW == 1){

      var   TAXSHOWW = "";

       }else{

            var   TAXSHOWW = "none";
       }



          if(STORESHOW == 1){

        var STORESHOWW = "";

       }else{

             var STORESHOWW  = "none";
       }



         if(ED == 1){

         Exp = "<td><input type='hidden' name='Exp_Date[]' value='"+ExpDate+"'>" + ExpDate + "</td>";

       }else{

           Exp = "<input type='hidden' name='Exp_Date[]' value='"+ExpDate+"'>";
       }

             document.getElementById("AddBtnPur"+r).style.display = "none";
             document.getElementById("Row"+r).style.display = "none";


                    var LANG = $("#LANG").val();
   if(LANG == 'ar' ){
          var Nemo = P_Ar_Name ;
          var PNNN='سعر البيع';
          }else{
             var Nemo = P_En_Name ;
                   var PNNN='Sales Price';
          }

        var markup = "<tr><td><input type='hidden' name='P_Ar_Name[]' value='"+P_Ar_Name+"'><input type='hidden' name='P_En_Name[]' value='"+P_En_Name+"'>" + Nemo + " ("+value+") </td><td><input type='hidden' name='Unit[]' value='"+UnitID+"'>" + UnitName + "<br><label>"+PNNN+"</label><input type='number' name='Price_Sale[]' value='"+Price_Sale+"'></td><td><input type='text' name='P_Code[]' value='"+Barcode+"' class='form-control'></td><td><input class='Qun  form-control' type='number' id='QuntityVV"+key+"' value='"+Qty+"' step='any' name='Qty[]' onkeyup='TotVV("+key+")' onclick='TotVV("+key+")'></td> <td><input type='number' name='Price[]' value='"+Price+"' onkeyup='TotVV("+key+")' onclick='TotVV("+key+")'  id='PriceVV"+key+"' step='any' class='form-control' ></td><td style='display:"+DISCSHOWW+"'><input type='number' name='Discount[]' value='"+Discount+"' onkeyup='TotVV("+key+")' onclick='TotVV("+key+")'  id='DiscountVV"+key+"' step='any' class='form-control' ></td><td style='display:"+TOTBFSHOWW+"'><input class='TotalBFTax form-control' type='text' id='TotalBFVV"+key+"' value='"+TotalBFTax+"'  disabled><input type='hidden' id='TotalBFVVHide"+key+"' name='TotalBFTax[]' value='"+TotalBFTax+"'></td><td style='display:"+TAXSHOWW+"'><input class='TotalTax form-control' type='text' id='TotalTaxVV"+key+"' value='"+TotalTax+"'  disabled><input type='hidden' id='TotalTaxVVHide"+key+"'  value='"+TotalTax+"' name='TotalTax[]'></td><td><input class='Tot form-control' type='text' id='TotalVV"+key+"' value='"+Total+"'  disabled><input type='hidden' id='TotalVVHide"+key+"' name='Total[]' value='"+Total+"'></td><td style='display:"+STORESHOWW+"'><input  type='hidden' name='StorePurch[]' value='"+StorePurch+"'>" + StorePurchName + "</td>"+Exp+"<td><button id='DelAssem' type='button' class='btn btn-default'><i class='fal fa-trash'></i></button><input type='hidden' name='Product[]' value='"+Product+"'><input type='hidden' name='VOne[]' value=''><input type='hidden' name='VTwo[]' value=''></td><input type='hidden' name='V_Name[]' value='"+value+"'><input type='hidden' name='VV_Name[]' value='"+value+"'><input type='hidden'  value='"+TaxRate+"' id='RateVV"+key+"'><input type='hidden'  value='"+TaxType+"' id='TypeVV"+key+"'><input type='hidden' name='PurchTax[]' value='"+PurchTax+"'><input type='hidden' class='TDISCOOO' id='TDiscProEdit"+key+"' name='TDiscPro[]' value='"+TDiscPro+"'><input type='hidden' id='Enough"+key+"' name='AllEnough[]' class='ENO'  value='0'></tr>";

             $("#data-dt").append(markup);

                      $("#Data").hide();
                                  });

                             $.fn.rowCount = function() {
     return $('tr', $(this).find('tbody')).length;
   };

   var rowctr = $('#dt').rowCount();

    var sumQ = 0;
   $('.Qun').each(function(){
   sumQ += parseFloat($(this).val());
   });

    var sumT = 0;
   $('.Tot').each(function(){
   sumT += parseFloat($(this).val());
   });

        var sumD = 0;
   $('.Disc').each(function(){
   sumD += parseFloat($(this).val());
   });


           var sumBF = 0;
   $('.TotalBFTax').each(function(){
   sumBF += parseFloat($(this).val());
   });

           var sumTax = 0;
   $('.TotalTax').each(function(){
   sumTax += parseFloat($(this).val());
   });


                var sumTTD = 0;
   $('.TDISCOOO').each(function(){
   sumTTD += parseFloat($(this).val());
   });
   $('#Product_Numbers').val(parseFloat(rowctr));
   $('#Product_NumbersHide').val(parseFloat(rowctr));

   $('#Total_Qty').val(parseFloat(sumQ));
   $('#Total_QtyHide').val(parseFloat(sumQ));

   $('#Total_Price').val(parseFloat(sumT) + parseFloat(sumTTD) -  parseFloat(sumTax));
   $('#Total_PriceHide').val(parseFloat(sumT) + parseFloat(sumTTD) -  parseFloat(sumTax));
   $('#Total_PriceW').val(parseFloat(sumT));
       $('#Total_Bill').val(parseFloat(sumT));
           $('#Total_DiscountTT').val(parseFloat(sumTTD).toFixed(2));
   $('#Total_DiscountHideTT').val(parseFloat(sumTTD).toFixed(2));

       $('#Total_Discount').val(parseFloat(sumD).toFixed(2));
   $('#Total_DiscountHide').val(parseFloat(sumD).toFixed(2));

          $('#Total_Bf_Taxes').val(parseFloat(sumBF)+parseFloat(sumTTD));
   $('#Total_Bf_TaxesHide').val(parseFloat(sumBF)+parseFloat(sumTTD));

          $('#Total_Taxes').val(parseFloat(sumTax).toFixed(2));
   $('#Total_TaxesHide').val(parseFloat(sumTax).toFixed(2));



       if(rowctr == 0){
        document.getElementById("Submit").style.display = "none";

       }else{

        document.getElementById("Submit").style.display = "block";
       }

                  var Pay =$('#Payment_Method').val();
        var Stat =$('#Status').val();
        var paid =$('#paid').val();

    if(Pay == 'Later' &&  Stat == 0 ){

             if(paid == 0 || paid < 0){
        document.getElementById("Submit").style.display = "none";
             }else{

           if(rowctr == 0){
        document.getElementById("Submit").style.display = "none";

       }else{

        document.getElementById("Submit").style.display = "block";
       }



             }

       }else{

       if(rowctr == 0){
        document.getElementById("Submit").style.display = "none";

       }else{

        document.getElementById("Submit").style.display = "block";
       }
       }


                             var Pay =$('#Payment_Method').val();
        var Stat =$('#Status').val();
        var paid =$('#paid').val();
        var resdiual =$('#resdiual').val();
        var total =$('#Total_PriceHide').val();
    var totalDisc =$('#Total_DiscountHideTT').val();

          var totalTax =$('#Total_TaxesHide').val();


            var RR= (parseFloat(total) + parseFloat(totalTax) ) - (parseFloat(resdiual) + parseFloat(totalDisc));
           var RRR= parseFloat(total) - (parseFloat(resdiual) + parseFloat(totalDisc));
          $('#TAFterD').val(parseFloat(RRR).toFixed(2));
         $('#paid').val(parseFloat(RR).toFixed(2));



                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });


               var Pay =$('#Payment_Method').val();
      if(Pay == 'Later'){


                   $('#paid').val('');

       }




        $('#data-dt').on('click', '#DelAssem', function(e){
                $(this).closest('tr').remove();

   $.fn.rowCount = function() {
     return $('tr', $(this).find('tbody')).length;
   };

   var rowctr = $('#dt').rowCount();

    var sumQ = 0;
   $('.Qun').each(function(){
   sumQ += parseFloat($(this).val());
   });

    var sumT = 0;
   $('.Tot').each(function(){
   sumT += parseFloat($(this).val());
   });

        var sumD = 0;
   $('.Disc').each(function(){
   sumD += parseFloat($(this).val());
   });


           var sumBF = 0;
   $('.TotalBFTax').each(function(){
   sumBF += parseFloat($(this).val());
   });

           var sumTax = 0;
   $('.TotalTax').each(function(){
   sumTax += parseFloat($(this).val());
   });


                 var sumTTD = 0;
   $('.TDISCOOO').each(function(){
   sumTTD += parseFloat($(this).val());
   });
   $('#Product_Numbers').val(parseFloat(rowctr));
   $('#Product_NumbersHide').val(parseFloat(rowctr));

   $('#Total_Qty').val(parseFloat(sumQ));
   $('#Total_QtyHide').val(parseFloat(sumQ));

   $('#Total_Price').val(parseFloat(sumT) + parseFloat(sumTTD) -  parseFloat(sumTax));
   $('#Total_PriceHide').val(parseFloat(sumT) + parseFloat(sumTTD) -  parseFloat(sumTax));
   $('#Total_PriceW').val(parseFloat(sumT));
       $('#Total_Bill').val(parseFloat(sumT));

           $('#Total_DiscountTT').val(parseFloat(sumTTD).toFixed(2));
   $('#Total_DiscountHideTT').val(parseFloat(sumTTD).toFixed(2));

       $('#Total_Discount').val(parseFloat(sumD).toFixed(2));
   $('#Total_DiscountHide').val(parseFloat(sumD).toFixed(2));

          $('#Total_Bf_Taxes').val(parseFloat(sumBF)+parseFloat(sumTTD));
   $('#Total_Bf_TaxesHide').val(parseFloat(sumBF)+parseFloat(sumTTD));

          $('#Total_Taxes').val(parseFloat(sumTax).toFixed(2));
   $('#Total_TaxesHide').val(parseFloat(sumTax).toFixed(2));



       if(rowctr == 0){
        document.getElementById("Submit").style.display = "none";

       }else{

        document.getElementById("Submit").style.display = "block";
       }


          var Pay =$('#Payment_Method').val();
        var Stat =$('#Status').val();
        var paid =$('#paid').val();

   if(Pay == 'Later' &&  Stat == 0 ){

             if(paid == 0 || paid < 0){
        document.getElementById("Submit").style.display = "none";
             }else{

           if(rowctr == 0){
        document.getElementById("Submit").style.display = "none";

       }else{

        document.getElementById("Submit").style.display = "block";
       }



             }

       }else{

       if(rowctr == 0){
        document.getElementById("Submit").style.display = "none";

       }else{

        document.getElementById("Submit").style.display = "block";
       }
       }


                               var Pay =$('#Payment_Method').val();
        var Stat =$('#Status').val();
        var paid =$('#paid').val();
        var resdiual =$('#resdiual').val();
        var total =$('#Total_PriceHide').val();
         var totalDisc =$('#Total_DiscountHideTT').val();
         var totalTax =$('#Total_TaxesHide').val();


            var RR= (parseFloat(total) + parseFloat(totalTax) ) - (parseFloat(resdiual) + parseFloat(totalDisc));
            var RRR= parseFloat(total) - (parseFloat(resdiual) + parseFloat(totalDisc));
          $('#TAFterD').val(parseFloat(RRR).toFixed(2));
         $('#paid').val(parseFloat(RR).toFixed(2));


               var Pay =$('#Payment_Method').val();
      if(Pay == 'Later'){


                   $('#paid').val('');

       }

            var EN = 0;
    var Total_PriceW =$('#Total_PriceW').val();

  $('.ENO').each(function(){
   EN += parseFloat($(this).val());
   });
 if(EN == 0){

             if(Total_PriceW == 'NaN'){
                   document.getElementById("Submit").style.display = "none";
                    }else{
        document.getElementById("Submit").style.display = "block";
             }

       }else{

        document.getElementById("Submit").style.display = "none";
       }



                    })


     }
</script>
<!-- Total Products -->
<script>
   function PurchTotal(r) {
   var TaxRate = $("#TaxRate"+r).val();
   var TaxType = $("#TaxType"+r).val();
   var Qty = $("#Qty"+r).val();
   var Price = $("#Price"+r).val();
   var Discount = $("#Discount"+r).val();
   var i = 0;
   var ii=0;
   var iii=0;



          var DIS = $("#DIS").val();

       if(parseFloat(DIS) == 0){

           Discount=$("#Discount"+r).val();

       }else{

           var d= Discount / 100 ;
           var Multi= parseFloat(Price) ;
          var dd= Multi * d;

        Discount=  dd;

       }
           var BFG =  parseFloat(Qty) * parseFloat(Discount) ;
             $("#TDiscPro"+r).val(parseFloat(BFG));
       if(TaxType == 1){
           //Precent

      var BF =  parseFloat(Qty) *  (parseFloat(Price)  -  parseFloat(Discount) );

            var BFF =  parseFloat(Qty) *  parseFloat(Price);

         $("#TotalBFTax"+r).val(parseFloat(BFF).toFixed(2));

       i =    parseFloat(TaxRate)  / 100 ;

        ii=  parseFloat(BF) * parseFloat(i) ;

           $("#Tax"+r).val(parseFloat(ii).toFixed(2));

       iii =  parseFloat(BF) + parseFloat(ii) ;

           $("#Total"+r).val(parseFloat(iii).toFixed(2));

       }else if(TaxType == 2){
          //Number
       var BF =  parseFloat(Qty) *  ( parseFloat(Price)  -  parseFloat(Discount) );
       var BFF =  parseFloat(Qty) *  parseFloat(Price)  ;

         $("#TotalBFTax"+r).val(parseFloat(BFF).toFixed(2));

            i =    parseFloat(TaxRate)   ;

           $("#Tax"+r).val(parseFloat(i).toFixed(2));

       iii =  parseFloat(BF) + parseFloat(i) ;

           $("#Total"+r).val(parseFloat(iii).toFixed(2));


       }


        var Total = $("#Total"+r).val();
        var TotalBFTax = $("#TotalBFTax"+r).val();
        var Tax = $("#Tax"+r).val();
        var UnitID = $("#UnitPurch"+r).val();
        var StorePurch = $("#StorePurch"+r).val();

       if(TaxRate == ''  || TaxType == ''  ||  Qty == '' || Price == '' || Discount == '' || Total == '' || TotalBFTax == '' || Tax == '' || UnitID == '' || StorePurch == ''){

        document.getElementById("AddBtnPur"+r).style.display = "none";
     }


       if(TaxRate != ''  && TaxType != ''  &&  Qty != ''  && Qty != 0 && Price != '' && Discount != '' && Total != '' && TotalBFTax != '' && Tax != '' && UnitID != '' && StorePurch != ''){

              if(Qty < 0 || Price < 0 || Discount < 0 || Total < 0){
               document.getElementById("AddBtnPur"+r).style.display = "none";

           }else{
             document.getElementById("AddBtnPur"+r).style.display = "inline-block";
           }
     }





   }

     function TotD(r) {
   var TaxRate = $("#TaxRateD"+r).val();
   var TaxType = $("#TaxTypeD"+r).val();
   var Qty = $("#QuntD"+r).val();
   var Price = $("#PriD"+r).val();
   var Discount = $("#DiscD"+r).val();
   var i = 0;
   var ii=0;
   var iii=0;

               var DIS = $("#DIS").val();

       if(parseFloat(DIS) == 0){

           Discount=$("#DiscD"+r).val();
               var BFG =  parseFloat(Qty) * parseFloat(Discount) ;
             $("#TDiscProEdit"+r).val(parseFloat(BFG));
       }else{

           var d= Discount / 100 ;
           var Multi= parseFloat(Price) ;
          var dd= Multi * d;

        Discount=  dd;
               var BFG =  parseFloat(Qty) * parseFloat(Discount) ;
             $("#TDiscProEdit"+r).val(parseFloat(BFG));
       }

       if(TaxType == 1){
           //Precent

     var BF =  parseFloat(Qty) *  (parseFloat(Price)  -  parseFloat(Discount) );
     var BFF =  parseFloat(Qty) *  parseFloat(Price);

       $("#TotBFTaxD"+r).val(parseFloat(BFF).toFixed(2));
       $("#TotBFTaxDHide"+r).val(parseFloat(BFF).toFixed(2));

       i =    parseFloat(TaxRate)  / 100 ;

        ii=  parseFloat(BF) * parseFloat(i) ;

           $("#TaxD"+r).val(parseFloat(ii).toFixed(2));
           $("#TaxDHide"+r).val(parseFloat(ii).toFixed(2));

       iii =  parseFloat(BF) + parseFloat(ii) ;

           $("#TotD"+r).val(parseFloat(iii).toFixed(2));
           $("#TotDHide"+r).val(parseFloat(iii).toFixed(2));

       }else if(TaxType == 2){
          //Number
      var BF =  parseFloat(Qty) *  (parseFloat(Price)  -  parseFloat(Discount) );
   var BFF =  parseFloat(Qty) *  parseFloat(Price) ;
         $("#TotBFTaxD"+r).val(parseFloat(BFF).toFixed(2));
         $("#TotBFTaxDHide"+r).val(parseFloat(BFF).toFixed(2));

            i =    parseFloat(TaxRate)   ;

           $("#TaxD"+r).val(parseFloat(i).toFixed(2));
           $("#TaxDHide"+r).val(parseFloat(i).toFixed(2));

       iii =  parseFloat(BF) + parseFloat(i) ;

           $("#TotD"+r).val(parseFloat(iii).toFixed(2));
           $("#TotDHide"+r).val(parseFloat(iii).toFixed(2));


       }

   var rowctr = $('#dt').rowCount();

    var sumQ = 0;
   $('.Qun').each(function(){
   sumQ += parseFloat($(this).val());
   });

    var sumT = 0;
   $('.Tot').each(function(){
   sumT += parseFloat($(this).val());
   });

        var sumD = 0;
   $('.Disc').each(function(){
   sumD += parseFloat($(this).val());
   });


           var sumBF = 0;
   $('.TotalBFTax').each(function(){
   sumBF += parseFloat($(this).val());
   });

           var sumTax = 0;
   $('.TotalTax').each(function(){
   sumTax += parseFloat($(this).val());
   });


                var sumTTD = 0;
   $('.TDISCOOO').each(function(){
   sumTTD += parseFloat($(this).val());
   });

   $('#Product_Numbers').val(parseFloat(rowctr));
   $('#Product_NumbersHide').val(parseFloat(rowctr));

   $('#Total_Qty').val(parseFloat(sumQ));
   $('#Total_QtyHide').val(parseFloat(sumQ));

    $('#Total_Price').val(parseFloat(sumT) + parseFloat(sumTTD) -  parseFloat(sumTax));
   $('#Total_PriceHide').val(parseFloat(sumT) + parseFloat(sumTTD) -  parseFloat(sumTax));
   $('#Total_PriceW').val(parseFloat(sumT));
       $('#Total_Bill').val(parseFloat(sumT));
           $('#Total_DiscountTT').val(parseFloat(sumTTD).toFixed(2));
   $('#Total_DiscountHideTT').val(parseFloat(sumTTD).toFixed(2));

       $('#Total_Discount').val(parseFloat(sumD).toFixed(2));
   $('#Total_DiscountHide').val(parseFloat(sumD).toFixed(2));

          $('#Total_Bf_Taxes').val(parseFloat(sumBF)+parseFloat(sumTTD));
   $('#Total_Bf_TaxesHide').val(parseFloat(sumBF)+parseFloat(sumTTD));

          $('#Total_Taxes').val(parseFloat(sumTax).toFixed(2));
   $('#Total_TaxesHide').val(parseFloat(sumTax).toFixed(2));

                            var Pay =$('#Payment_Method').val();
        var Stat =$('#Status').val();
        var paid =$('#paid').val();
        var resdiual =$('#resdiual').val();
        var total =$('#Total_PriceHide').val();
        var totalDisc =$('#Total_DiscountHideTT').val();

          var totalTax =$('#Total_TaxesHide').val();


         var RR= (parseFloat(total) + parseFloat(totalTax) ) - (parseFloat(resdiual) + parseFloat(totalDisc));
             var RRR= parseFloat(total) - (parseFloat(resdiual) + parseFloat(totalDisc));
          $('#TAFterD').val(parseFloat(RRR).toFixed(2));
         $('#paid').val(parseFloat(RR).toFixed(2));


            var EN = 0;
               var Pay =$('#Payment_Method').val();
               var Total_PriceW =$('#Total_PriceW').val();
      if(Pay == 'Later'){


                   $('#paid').val('');

       }


                  if(parseFloat(Qty) < 0 || parseFloat(Price) < 0 || parseFloat(Discount) < 0)
           {
               document.getElementById("QuntD"+r).style.border="1px solid darkred";
             $("#Enough"+r).val(1);

              }else{
                       document.getElementById("QuntD"+r).style.border="none";
             $("#Enough"+r).val(0);
                        }

  $('.ENO').each(function(){
   EN += parseFloat($(this).val());
   });
 if(EN == 0){

             if(Total_PriceW == 'NaN'){
                   document.getElementById("Submit").style.display = "none";
                    }else{
        document.getElementById("Submit").style.display = "block";
             }

       }else{

        document.getElementById("Submit").style.display = "none";
       }


   }

</script>
<!-- Total Products Serial -->
<script>
   function PurchTotalSerial(r) {
   var TaxRate = $("#TaxRate"+r).val();
   var TaxType = $("#TaxType"+r).val();
   var Qty = 1;
   var Price = $("#Price"+r).val();
   var Discount = $("#Discount"+r).val();
   var i = 0;
   var ii=0;
   var iii=0;

              var DIS = $("#DIS").val();

       if(parseFloat(DIS) == 0){

           Discount=$("#Discount"+r).val();

       }else{

           var d= Discount / 100 ;
           var Multi= parseFloat(Price) ;
          var dd= Multi * d;

        Discount=  dd;

       }
           var BFG =  parseFloat(Qty) * parseFloat(Discount) ;
             $("#TDiscPro"+r).val(parseFloat(BFG));

       if(TaxType == 1){
           //Precent

       var BF =  parseFloat(Qty) *  (parseFloat(Price)  -  parseFloat(Discount) );
       var BFF =  parseFloat(Qty) *  parseFloat(Price);

       $("#TotalBFTax"+r).val(parseFloat(BFF).toFixed(2));

       i =    parseFloat(TaxRate)  / 100 ;

        ii=  parseFloat(BF) * parseFloat(i) ;

           $("#Tax"+r).val(parseFloat(ii).toFixed(2));

       iii =  parseFloat(BF) + parseFloat(ii) ;

           $("#Total"+r).val(parseFloat(iii).toFixed(2));

       }else if(TaxType == 2){
          //Number
         var BF =  parseFloat(Qty) *  (parseFloat(Price)  -  parseFloat(Discount) );
         var BFF =  parseFloat(Qty) *  parseFloat(Price);
         $("#TotalBFTax"+r).val(parseFloat(BFF).toFixed(2));

            i =    parseFloat(TaxRate)   ;

           $("#Tax"+r).val(parseFloat(i).toFixed(2));

       iii =  parseFloat(BF) + parseFloat(i) ;

           $("#Total"+r).val(parseFloat(iii).toFixed(2));


       }


        var Total = $("#Total"+r).val();
        var TotalBFTax = $("#TotalBFTax"+r).val();
        var Tax = $("#Tax"+r).val();
        var UnitID = $("#UnitPurch"+r).val();
        var StorePurch = $("#StorePurch"+r).val();

       if(TaxRate == ''  || TaxType == ''  ||  Qty == '' || Price == '' || Discount == '' || Total == '' || TotalBFTax == '' || Tax == '' || UnitID == '' || StorePurch == ''){

        document.getElementById("AddBtnPur"+r).style.display = "none";
     }


       if(TaxRate != ''  && TaxType != ''  &&  Qty != ''  && Qty != 0 && Price != '' && Discount != '' && Total != '' && TotalBFTax != '' && Tax != '' && UnitID != '' && StorePurch != ''){

          if(Qty < 0 || Price < 0 || Discount < 0 || Total < 0){
               document.getElementById("AddBtnPur"+r).style.display = "none";

           }else{
             document.getElementById("AddBtnPur"+r).style.display = "inline-block";
           }
     }





   }
</script>
<!-- Total Products V -->
<script>
   function PurchTotalV(r) {
   var TaxRate = $("#TaxRate"+r).val();
   var TaxType = $("#TaxType"+r).val();
   var Qty = $("#Qty"+r).val();
   var Price = $("#Price"+r).val();
   var Discount = $("#Discount"+r).val();
   var i = 0;
   var ii=0;
   var iii=0;

              var DIS = $("#DIS").val();

       if(parseFloat(DIS) == 0){

           Discount=$("#Discount"+r).val();

       }else{

           var d= Discount / 100 ;
           var Multi= parseFloat(Price) ;
          var dd= Multi * d;

        Discount=  dd;

       }
           var BFG =  parseFloat(Qty) * parseFloat(Discount) ;
             $("#TDiscPro"+r).val(parseFloat(BFG));
       if(TaxType == 1){
           //Precent

        var BF =  parseFloat(Qty) *  (parseFloat(Price)  -  parseFloat(Discount) );
        var BFF =  parseFloat(Qty) *  parseFloat(Price);

       $("#TotalBFTax"+r).val(parseFloat(BFF).toFixed(2));

       i =    parseFloat(TaxRate)  / 100 ;

        ii=  parseFloat(BF) * parseFloat(i) ;

           $("#Tax"+r).val(parseFloat(ii).toFixed(2));

       iii =  parseFloat(BF) + parseFloat(ii) ;

           $("#Total"+r).val(parseFloat(iii).toFixed(2));

       }else if(TaxType == 2){
          //Number
          var BF =  parseFloat(Qty) *  (parseFloat(Price)  -  parseFloat(Discount) );
          var BFF =  parseFloat(Qty) *  parseFloat(Price);
         $("#TotalBFTax"+r).val(parseFloat(BFF).toFixed(2));

            i =    parseFloat(TaxRate)   ;

           $("#Tax"+r).val(parseFloat(i).toFixed(2));

       iii =  parseFloat(BF) + parseFloat(i) ;

           $("#Total"+r).val(parseFloat(iii).toFixed(2));


       }


        var Total = $("#Total"+r).val();
        var TotalBFTax = $("#TotalBFTax"+r).val();
        var Tax = $("#Tax"+r).val();
        var UnitID = $("#UnitPurch"+r).val();
        var StorePurch = $("#StorePurch"+r).val();

       if(TaxRate == ''  || TaxType == ''  ||  Qty == '' || Price == '' || Discount == '' || Total == '' || TotalBFTax == '' || Tax == '' || UnitID == '' || StorePurch == ''){

        document.getElementById("AddBtnPur"+r).style.display = "none";
     }


       if(TaxRate != ''  && TaxType != ''  &&  Qty != ''  && Qty != 0 && Price != '' && Discount != '' && Total != '' && TotalBFTax != '' && Tax != '' && UnitID != '' && StorePurch != ''){

              if(Qty < 0 || Price < 0 || Discount < 0 || Total < 0){
               document.getElementById("AddBtnPur"+r).style.display = "none";

           }else{
             document.getElementById("AddBtnPur"+r).style.display = "inline-block";
           }
     }





   }

    function TotV(r) {
   var TaxRate = $("#RateV"+r).val();
   var TaxType = $("#TypeV"+r).val();
   var Qty = $("#QuntityV"+r).val();
   var Price = $("#PriceV"+r).val();
   var Discount = $("#DiscountV"+r).val();
   var i = 0;
   var ii=0;
   var iii=0;

                     var DIS = $("#DIS").val();

       if(parseFloat(DIS) == 0){

           Discount=$("#DiscountV"+r).val();
               var BFG =  parseFloat(Qty) * parseFloat(Discount) ;
             $("#TDiscProEdit"+r).val(parseFloat(BFG));
       }else{

           var d= Discount / 100 ;
           var Multi= parseFloat(Price) ;
          var dd= Multi * d;

        Discount=  dd;
               var BFG =  parseFloat(Qty) * parseFloat(Discount) ;
             $("#TDiscProEdit"+r).val(parseFloat(BFG));
       }
       if(TaxType == 1){
           //Precent

       var BF =  parseFloat(Qty) *  (parseFloat(Price)  -  parseFloat(Discount) );
       var BFF =  parseFloat(Qty) *  parseFloat(Price);

       $("#TotalBFV"+r).val(parseFloat(BFF).toFixed(2));
       $("#TotalBFVHide"+r).val(parseFloat(BFF).toFixed(2));

       i =    parseFloat(TaxRate)  / 100 ;

        ii=  parseFloat(BF) * parseFloat(i) ;

           $("#TotalTax"+r).val(parseFloat(ii).toFixed(2));
           $("#TotalTaxHide"+r).val(parseFloat(ii).toFixed(2));

       iii =  parseFloat(BF) + parseFloat(ii) ;

           $("#TotalV"+r).val(parseFloat(iii).toFixed(2));
           $("#TotalVHide"+r).val(parseFloat(iii).toFixed(2));

       }else if(TaxType == 2){
          //Number
       var BF =  parseFloat(Qty) *  (parseFloat(Price)  -  parseFloat(Discount) );
       var BFF =  parseFloat(Qty) *  parseFloat(Price) ;
         $("#TotalBFV"+r).val(parseFloat(BFF).toFixed(2));
         $("#TotalBFVHide"+r).val(parseFloat(BFF).toFixed(2));

            i =    parseFloat(TaxRate)   ;

           $("#TotalTax"+r).val(parseFloat(i).toFixed(2));
           $("#TotalTaxHide"+r).val(parseFloat(i).toFixed(2));

       iii =  parseFloat(BF) + parseFloat(i) ;

           $("#TotalV"+r).val(parseFloat(iii).toFixed(2));
           $("#TotalVHide"+r).val(parseFloat(iii).toFixed(2));


       }


       $.fn.rowCount = function() {
     return $('tr', $(this).find('tbody')).length;
   };

   var rowctr = $('#dt').rowCount();

    var sumQ = 0;
   $('.Qun').each(function(){
   sumQ += parseFloat($(this).val());
   });

    var sumT = 0;
   $('.Tot').each(function(){
   sumT += parseFloat($(this).val());
   });

        var sumD = 0;
   $('.Disc').each(function(){
   sumD += parseFloat($(this).val());
   });


           var sumBF = 0;
   $('.TotalBFTax').each(function(){
   sumBF += parseFloat($(this).val());
   });

           var sumTax = 0;
   $('.TotalTax').each(function(){
   sumTax += parseFloat($(this).val());
   });


                 var sumTTD = 0;
   $('.TDISCOOO').each(function(){
   sumTTD += parseFloat($(this).val());
   });
   $('#Product_Numbers').val(parseFloat(rowctr));
   $('#Product_NumbersHide').val(parseFloat(rowctr));

   $('#Total_Qty').val(parseFloat(sumQ));
   $('#Total_QtyHide').val(parseFloat(sumQ));

   $('#Total_Price').val(parseFloat(sumT) + parseFloat(sumTTD) -  parseFloat(sumTax));
   $('#Total_PriceHide').val(parseFloat(sumT) + parseFloat(sumTTD) -  parseFloat(sumTax));
   $('#Total_PriceW').val(parseFloat(sumT));
       $('#Total_Bill').val(parseFloat(sumT));
           $('#Total_DiscountTT').val(parseFloat(sumTTD).toFixed(2));
   $('#Total_DiscountHideTT').val(parseFloat(sumTTD).toFixed(2));

       $('#Total_Discount').val(parseFloat(sumD).toFixed(2));
   $('#Total_DiscountHide').val(parseFloat(sumD).toFixed(2));

          $('#Total_Bf_Taxes').val(parseFloat(sumBF)+parseFloat(sumTTD));
   $('#Total_Bf_TaxesHide').val(parseFloat(sumBF)+parseFloat(sumTTD));

          $('#Total_Taxes').val(parseFloat(sumTax).toFixed(2));
   $('#Total_TaxesHide').val(parseFloat(sumTax).toFixed(2));



       if(rowctr == 0){
        document.getElementById("Submit").style.display = "none";

       }else{

        document.getElementById("Submit").style.display = "block";
       }


      var Pay =$('#Payment_Method').val();
        var Stat =$('#Status').val();
        var paid =$('#paid').val();
   if(Pay == 'Later' &&  Stat == 0 ){

             if(paid == 0 || paid < 0){
        document.getElementById("Submit").style.display = "none";
             }else{

           if(rowctr == 0){
        document.getElementById("Submit").style.display = "none";

       }else{

        document.getElementById("Submit").style.display = "block";
       }



             }

       }else{

       if(rowctr == 0){
        document.getElementById("Submit").style.display = "none";

       }else{

        document.getElementById("Submit").style.display = "block";
       }
       }

     var Pay =$('#Payment_Method').val();
        var Stat =$('#Status').val();
        var paid =$('#paid').val();
        var resdiual =$('#resdiual').val();
        var total =$('#Total_PriceHide').val();
         var totalDisc =$('#Total_DiscountHideTT').val();
         var totalTax =$('#Total_TaxesHide').val();


          var RR= (parseFloat(total) + parseFloat(totalTax) ) - (parseFloat(resdiual) + parseFloat(totalDisc));
            var RRR= parseFloat(total) - (parseFloat(resdiual) + parseFloat(totalDisc));
          $('#TAFterD').val(parseFloat(RRR).toFixed(2));
         $('#paid').val(parseFloat(RR).toFixed(2));


               var Pay =$('#Payment_Method').val();
      if(Pay == 'Later'){


                   $('#paid').val('');

       }
                   var EN = 0;
           var Total_PriceW =$('#Total_PriceW').val();
                      if(parseFloat(Qty) < 0 || parseFloat(Price) < 0 || parseFloat(Discount) < 0)
           {
               document.getElementById("QuntityV"+r).style.border="1px solid darkred";
             $("#Enough"+r).val(1);

              }else{
                       document.getElementById("QuntityV"+r).style.border="none";
             $("#Enough"+r).val(0);
                        }


  $('.ENO').each(function(){
   EN += parseFloat($(this).val());
   });
 if(EN == 0){

             if(Total_PriceW == 'NaN'){
                   document.getElementById("Submit").style.display = "none";
                    }else{
        document.getElementById("Submit").style.display = "block";
             }

       }else{

        document.getElementById("Submit").style.display = "none";
       }

   }


</script>
<!-- Total Products VV -->
<script>
   function PurchTotalVV(r) {
   var TaxRate = $("#TaxRate"+r).val();
   var TaxType = $("#TaxType"+r).val();
   var Qty = $("#Qty"+r).val();
   var Price = $("#Price"+r).val();
   var Discount = $("#Discount"+r).val();
   var i = 0;
   var ii=0;
   var iii=0;

              var DIS = $("#DIS").val();

       if(parseFloat(DIS) == 0){

           Discount=$("#Discount"+r).val();

       }else{

           var d= Discount / 100 ;
           var Multi= parseFloat(Price) ;
          var dd= Multi * d;

        Discount=  dd;

       }
           var BFG =  parseFloat(Qty) * parseFloat(Discount) ;
             $("#TDiscPro"+r).val(parseFloat(BFG));
       if(TaxType == 1){
           //Precent

         var BF =  parseFloat(Qty) *  (parseFloat(Price)  -  parseFloat(Discount) );
         var BFF =  parseFloat(Qty) *  parseFloat(Price);

       $("#TotalBFTax"+r).val(parseFloat(BFF).toFixed(2));

       i =    parseFloat(TaxRate)  / 100 ;

        ii=  parseFloat(BF) * parseFloat(i) ;

           $("#Tax"+r).val(parseFloat(ii).toFixed(2));

       iii =  parseFloat(BF) + parseFloat(ii) ;

           $("#Total"+r).val(parseFloat(iii).toFixed(2));

       }else if(TaxType == 2){
          //Number
          var BF =  parseFloat(Qty) *  (parseFloat(Price)  -  parseFloat(Discount) );
          var BFF =  parseFloat(Qty) *  parseFloat(Price);
         $("#TotalBFTax"+r).val(parseFloat(BFF).toFixed(2));

            i =    parseFloat(TaxRate)   ;

           $("#Tax"+r).val(parseFloat(i).toFixed(2));

       iii =  parseFloat(BF) + parseFloat(i) ;

           $("#Total"+r).val(parseFloat(iii).toFixed(2));


       }


        var Total = $("#Total"+r).val();
        var TotalBFTax = $("#TotalBFTax"+r).val();
        var Tax = $("#Tax"+r).val();
        var UnitID = $("#UnitPurch"+r).val();
        var StorePurch = $("#StorePurch"+r).val();

       if(TaxRate == ''  || TaxType == ''  ||  Qty == '' || Price == '' || Discount == '' || Total == '' || TotalBFTax == '' || Tax == '' || UnitID == '' || StorePurch == ''){

        document.getElementById("AddBtnPur"+r).style.display = "none";
     }


       if(TaxRate != ''  && TaxType != ''  &&  Qty != ''  && Qty != 0 && Price != '' && Discount != '' && Total != '' && TotalBFTax != '' && Tax != '' && UnitID != '' && StorePurch != ''){

           if(Qty < 0 || Price < 0 || Discount < 0 || Total < 0){
               document.getElementById("AddBtnPur"+r).style.display = "none";

           }else{
             document.getElementById("AddBtnPur"+r).style.display = "inline-block";
           }
     }





   }

    function TotVV(r) {
   var TaxRate = $("#RateVV"+r).val();
   var TaxType = $("#TypeVV"+r).val();
   var Qty = $("#QuntityVV"+r).val();
   var Price = $("#PriceVV"+r).val();
   var Discount = $("#DiscountVV"+r).val();
   var i = 0;
   var ii=0;
   var iii=0;
              var DIS = $("#DIS").val();

       if(parseFloat(DIS) == 0){

           Discount=$("#DiscountVV"+r).val();
               var BFG =  parseFloat(Qty) * parseFloat(Discount) ;
             $("#TDiscProEdit"+r).val(parseFloat(BFG));
       }else{

           var d= Discount / 100 ;
           var Multi= parseFloat(Price) ;
          var dd= Multi * d;

        Discount=  dd;
               var BFG =  parseFloat(Qty) * parseFloat(Discount) ;
             $("#TDiscProEdit"+r).val(parseFloat(BFG));
       }

       if(TaxType == 1){
           //Precent

         var BF =  parseFloat(Qty) *  (parseFloat(Price)  -  parseFloat(Discount) );
         var BFF =  parseFloat(Qty) *  parseFloat(Price);

       $("#TotalBFVV"+r).val(parseFloat(BFF).toFixed(2));
       $("#TotalBFVVHide"+r).val(parseFloat(BFF).toFixed(2));

       i =    parseFloat(TaxRate)  / 100 ;

        ii=  parseFloat(BF) * parseFloat(i) ;

           $("#TotalTaxVV"+r).val(parseFloat(ii).toFixed(2));
           $("#TotalTaxVVHide"+r).val(parseFloat(ii).toFixed(2));

       iii =  parseFloat(BF) + parseFloat(ii) ;

           $("#TotalVV"+r).val(parseFloat(iii).toFixed(2));
           $("#TotalVVHide"+r).val(parseFloat(iii).toFixed(2));

       }else if(TaxType == 2){
          //Number
          var BF =  parseFloat(Qty) *  (parseFloat(Price)  -  parseFloat(Discount) );
          var BFF =  parseFloat(Qty) *  parseFloat(Price);
         $("#TotalBFVV"+r).val(parseFloat(BFF).toFixed(2));
         $("#TotalBFVVHide"+r).val(parseFloat(BFF).toFixed(2));

            i =    parseFloat(TaxRate)   ;

           $("#TotalTaxVV"+r).val(parseFloat(i).toFixed(2));
           $("#TotalTaxVVHide"+r).val(parseFloat(i).toFixed(2));

       iii =  parseFloat(BF) + parseFloat(i) ;

           $("#TotalVV"+r).val(parseFloat(iii).toFixed(2));
           $("#TotalVVHide"+r).val(parseFloat(iii).toFixed(2));


       }


       $.fn.rowCount = function() {
     return $('tr', $(this).find('tbody')).length;
   };

   var rowctr = $('#dt').rowCount();

    var sumQ = 0;
   $('.Qun').each(function(){
   sumQ += parseFloat($(this).val());
   });

    var sumT = 0;
   $('.Tot').each(function(){
   sumT += parseFloat($(this).val());
   });

        var sumD = 0;
   $('.Disc').each(function(){
   sumD += parseFloat($(this).val());
   });


           var sumBF = 0;
   $('.TotalBFTax').each(function(){
   sumBF += parseFloat($(this).val());
   });

           var sumTax = 0;
   $('.TotalTax').each(function(){
   sumTax += parseFloat($(this).val());
   });


                 var sumTTD = 0;
   $('.TDISCOOO').each(function(){
   sumTTD += parseFloat($(this).val());
   });
   $('#Product_Numbers').val(parseFloat(rowctr));
   $('#Product_NumbersHide').val(parseFloat(rowctr));

   $('#Total_Qty').val(parseFloat(sumQ));
   $('#Total_QtyHide').val(parseFloat(sumQ));

   $('#Total_Price').val(parseFloat(sumT) + parseFloat(sumTTD) -  parseFloat(sumTax));
   $('#Total_PriceHide').val(parseFloat(sumT) + parseFloat(sumTTD) -  parseFloat(sumTax));
    $('#Total_PriceW').val(parseFloat(sumT));
       $('#Total_Bill').val(parseFloat(sumT));
           $('#Total_DiscountTT').val(parseFloat(sumTTD).toFixed(2));
   $('#Total_DiscountHideTT').val(parseFloat(sumTTD).toFixed(2));

       $('#Total_Discount').val(parseFloat(sumD).toFixed(2));
   $('#Total_DiscountHide').val(parseFloat(sumD).toFixed(2));

          $('#Total_Bf_Taxes').val(parseFloat(sumBF)+parseFloat(sumTTD));
   $('#Total_Bf_TaxesHide').val(parseFloat(sumBF)+parseFloat(sumTTD));

          $('#Total_Taxes').val(parseFloat(sumTax).toFixed(2));
   $('#Total_TaxesHide').val(parseFloat(sumTax).toFixed(2));


       if(rowctr == 0){
        document.getElementById("Submit").style.display = "none";

       }else{

        document.getElementById("Submit").style.display = "block";
       }


      var Pay =$('#Payment_Method').val();
        var Stat =$('#Status').val();
        var paid =$('#paid').val();

    if(Pay == 'Later' &&  Stat == 0 ){

             if(paid == 0 || paid < 0){
        document.getElementById("Submit").style.display = "none";
             }else{

           if(rowctr == 0){
        document.getElementById("Submit").style.display = "none";

       }else{

        document.getElementById("Submit").style.display = "block";
       }



             }

       }else{

       if(rowctr == 0){
        document.getElementById("Submit").style.display = "none";

       }else{

        document.getElementById("Submit").style.display = "block";
       }
       }

     var Pay =$('#Payment_Method').val();
        var Stat =$('#Status').val();
        var paid =$('#paid').val();
        var resdiual =$('#resdiual').val();
        var total =$('#Total_PriceHide').val();
         var totalDisc =$('#Total_DiscountHideTT').val();
         var totalTax =$('#Total_TaxesHide').val();


          var RR= (parseFloat(total) + parseFloat(totalTax) ) - (parseFloat(resdiual) + parseFloat(totalDisc));
          var RRR= parseFloat(total) - (parseFloat(resdiual) + parseFloat(totalDisc));
          $('#TAFterD').val(parseFloat(RRR).toFixed(2));
         $('#paid').val(parseFloat(RR).toFixed(2));

             var EN = 0;
               var Pay =$('#Payment_Method').val();
      if(Pay == 'Later'){


                   $('#paid').val('');

       }

             var Total_PriceW =$('#Total_PriceW').val();
                           if(parseFloat(Qty) < 0 || parseFloat(Price) < 0 || parseFloat(Discount) < 0)
           {
               document.getElementById("QuntityVV"+r).style.border="1px solid darkred";
             $("#Enough"+r).val(1);

              }else{
                       document.getElementById("QuntityVV"+r).style.border="none";
             $("#Enough"+r).val(0);
                        }


  $('.ENO').each(function(){
   EN += parseFloat($(this).val());
   });
 if(EN == 0){

             if(Total_PriceW == 'NaN'){
                   document.getElementById("Submit").style.display = "none";
                    }else{
        document.getElementById("Submit").style.display = "block";
             }

       }else{

        document.getElementById("Submit").style.display = "none";
       }


   }
</script>
<!-- Enter Next Input -->
<script>
   $(function () {
       $(document).on('keyup', '.inputs', function (e) {
           if (e.which == 13) {
               var $this = $(this);
               var $td = $this.closest('.col-md-12'); // Current TD
               var $row = $td.closest('.row'); // Current TR
               var $rows = $row.parent(); // Current TABLE or TBODY - parent of all rows
               var column = $td.index(); // Current column of TD

               // Search on a row basis in current column, then try next column
               // repeat until we run out of cells
               while ($td.length) {
                   // get next row
                   $row = $row.next('.row');
                   // If we were on last row
                   if ($row.length == 0) {
                       // Go back to first row
                       $row = $rows.children().first();
                       // And use next column
                       column++;
                   }
                   // get the position in the row column - if it exists
                   $td = $row.children().eq(column);
                   var $input = $td.find('.inputs');
                   if ($input.length) {
                       $input.focus();
                       break;
                   }
               }
           }
       });
   });
</script>
<!-- Scanner  problem -->
<script>
   $(".form-control").keypress(function(event){
   if (event.which == '10' || event.which == '13') {
   event.preventDefault();
   }
   });


</script>
<!-- Status -->
<script>
   function Statuss(){

       var Pay =$('#Payment_Method').val();
       var Stat =$('#Status').val();
       var paid =$('#paid').val();
       var total =$('#Total_PriceHide').val();
   $.fn.rowCount = function() {
    return $('tr', $(this).find('tbody')).length;
   };

   var rowctr = $('#dt').rowCount();



      if(Pay == 'Later'  &&  Stat == 0 ){

            if(paid == 0 || paid < 0){
       document.getElementById("Submit").style.display = "none";
            }else{

          if(rowctr == 0){
       document.getElementById("Submit").style.display = "none";

      }else{


          if(paid != 0  ||  paid == ''){

               document.getElementById("Submit").style.display = "block";
          }else{

                    document.getElementById("Submit").style.display = "none";
          }


      }



            }

      }else{

      if(rowctr == 0){
       document.getElementById("Submit").style.display = "none";

      }else{

           if(paid != 0  ||  paid == ''){

               document.getElementById("Submit").style.display = "block";
          }else{

                    document.getElementById("Submit").style.display = "none";
          }

      }
      }


      if(Pay == 'Check'){

          document.getElementById("CHECK").style.display = "block";

      }else{

        document.getElementById("CHECK").style.display = "none";
      }

                         var Pay =$('#Payment_Method').val();
       var Stat =$('#Status').val();
       var paid =$('#paid').val();
       var resdiual =$('#resdiual').val();
       var total =$('#Total_PriceHide').val();
       var totalDisc =$('#Total_DiscountHideTT').val();
         var totalTax =$('#Total_TaxesHide').val();


      var RR= (parseFloat(total) + parseFloat(totalTax) ) - (parseFloat(resdiual) + parseFloat(totalDisc));
          var RRR= parseFloat(total) - (parseFloat(resdiual) + parseFloat(totalDisc));
         $('#TAFterD').val(parseFloat(RRR).toFixed(2));
        $('#paid').val(parseFloat(RR));
        $('#Total_PriceW').val(parseFloat(RR));

       if(Pay == 'Later'){

          document.getElementById("LaterDate").style.display = "block";

      }else{

        document.getElementById("LaterDate").style.display = "none";
      }



   }


   $(document).ready(function() {
         var Pay =$('#Payment_Method').val();
       var Stat =$('#Status').val();
       var paid =$('#paid').val();
                   var total =$('#Total_PriceHide').val();


   $.fn.rowCount = function() {
    return $('tr', $(this).find('tbody')).length;
   };

   var rowctr = $('#dt').rowCount();



      if(Pay == 'Later'  &&  Stat == 0 ){

            if(paid == 0 || paid < 0){
       document.getElementById("Submit").style.display = "none";
            }else{

          if(rowctr == 0){
       document.getElementById("Submit").style.display = "none";

      }else{


          if(paid != 0  ||  paid == ''){

               document.getElementById("Submit").style.display = "block";
          }else{

                    document.getElementById("Submit").style.display = "none";
          }


      }



            }

      }else{

      if(rowctr == 0){
       document.getElementById("Submit").style.display = "none";

      }else{

           if(paid != 0  ||  paid == ''){

               document.getElementById("Submit").style.display = "block";
          }else{

                    document.getElementById("Submit").style.display = "none";
          }

      }
      }






      if(Pay == 'Check'){

          document.getElementById("CHECK").style.display = "block";

      }else{

        document.getElementById("CHECK").style.display = "none";
      }

                             var Pay =$('#Payment_Method').val();
       var Stat =$('#Status').val();
       var paid =$('#paid').val();
       var resdiual =$('#resdiual').val();
       var total =$('#Total_PriceHide').val();
       var totalDisc =$('#Total_DiscountHideTT').val();
   var totalTax =$('#Total_TaxesHide').val();

       var RR= (parseFloat(total) + parseFloat(totalTax) ) - (parseFloat(resdiual) + parseFloat(totalDisc));
         var RRR= parseFloat(total) - (parseFloat(resdiual) + parseFloat(totalDisc));
         $('#TAFterD').val(parseFloat(RRR).toFixed(2));
        $('#paid').val(parseFloat(RR));
        $('#Total_PriceW').val(parseFloat(RR));

             if(Pay == 'Later'){

          document.getElementById("LaterDate").style.display = "block";

      }else{

        document.getElementById("LaterDate").style.display = "none";
      }


   });
</script>
<!-- Submit Script -->
<script>
   function SPP(){
      $('#sp').val(1);
      var x= $('#sp').val();

             var store= $('#store').val();
      var SSAFE= $('#SSAFE').val();
      var vendor= $('#vendor').val();

          var Pay =$('#Payment_Method').val();
 var paid =$('#paid').val();

           var SafeBalance= $('#SafeBalance').val();
           var Amount= $('#Total_PriceW').val();

       if(Pay == 'Later'){

     if(parseFloat(paid) > parseFloat(SafeBalance)){
  alert('المبلغ غير كافي في الخزينه');
         }else{

   if(store != '' && SSAFE != '' && vendor != null){
        if(x == 1){
         document.getElementById("Submit").style.display = "none";
   document.getElementById('form').submit();
   }
   }else{

           alert('تأكد من اختيار مخزن و خزنه وحساب');

       }


       }
          }else{
            if(parseFloat(Amount) > parseFloat(SafeBalance)){
  alert('المبلغ غير كافي في الخزينه');
         }else{

   if(store != '' && SSAFE != '' && vendor != null){
        if(x == 1){
         document.getElementById("Submit").style.display = "none";
   document.getElementById('form').submit();
   }
   }else{

           alert('تأكد من اختيار مخزن و خزنه وحساب');

       }


       }
   }



   }


   function SPS(){
      $('#sp').val(0);
      var x= $('#sp').val();

    var paid =$('#paid').val();

         var Pay =$('#Payment_Method').val();

                     var store= $('#store').val();
      var SSAFE= $('#SSAFE').val();
      var vendor= $('#vendor').val();

                var SafeBalance= $('#SafeBalance').val();
           var Amount= $('#Total_PriceW').val();


          if(Pay == 'Later'){
            if(parseFloat(paid) > parseFloat(SafeBalance)){
  alert('المبلغ غير كافي في الخزينه');
         }else{
   if(store != '' && SSAFE != '' && vendor != null){
       if(x == 0){
         document.getElementById("Submit").style.display = "none";
   document.getElementById('form').submit();
   }
   }else{

           alert('تأكد من اختيار مخزن و خزنه وحساب');

       }

         }
          }else{
            if(parseFloat(Amount) > parseFloat(SafeBalance)){
  alert('المبلغ غير كافي في الخزينه');
         }else{
   if(store != '' && SSAFE != '' && vendor != null){
       if(x == 0){
         document.getElementById("Submit").style.display = "none";
   document.getElementById('form').submit();
   }
   }else{

           alert('تأكد من اختيار مخزن و خزنه وحساب');

       }

         }
          }
   }



</script>
<!-- Change Price Unit -->
<script>
   function ChangePriceUnit(r){

   var POne = $('#UnitPriceOne'+r).val();
   var PTwo = $('#UnitPriceTwo'+r).val();
   var PThree = $('#UnitPriceThree'+r).val();
   var ID = $('#UnitPriceID'+r).val();

       if(PTwo == ''){

           PTwo  = 0 ;
       }else{

         PTwo = $('#UnitPriceTwo'+r).val();
       }

          if(PThree == ''){

           PThree  = 0 ;
       }else{

         PThree = $('#UnitPriceThree'+r).val();
       }

         if(POne) {
                        $.ajax({
                            url: 'ChangePriceUnit/'+POne+'/'+PTwo+'/'+PThree+'/'+ID,
                            type:"GET",
                            dataType:"json",
                            beforeSend: function(){
                                $('#loader').css("visibility", "visible");
                            },

                            success:function(data) {
                                $.each(data, function(key, value){

                    alert('تم التحديث بنجاح');

                                });
                            },
                            complete: function(){
                                $('#loader').css("visibility", "hidden");
                            }
                        });
                    }else{

                       alert('حقل السعر الاول مطلوب');

                    }



   }
</script>
<!-- Add New Product -->
<script>
   function NewProducts(){


   var Name = $('#P_Ar_Name').val();
   var EnName = $('#P_En_Name').val();
   var P_Type = $('#P_Type').val();
   var Brand = $('#Brand').val();
   var Group = $('#Group').val();
   var unit = $('#unit').val();
   var Rate = $('#Rate').val();
   var Barcode = $('#Barcode').val();
   var Price = $('#Price').val();
   var Price_Two = $('#Price_Two').val();
   var Price_Three = $('#Price_Three').val();

         if(Name) {
                        $.ajax({
                            url: 'AddNewProduct/'+Name+'/'+EnName+'/'+P_Type+'/'+Brand+'/'+Group+'/'+unit+'/'+Rate+'/'+Barcode+'/'+Price+'/'+Price_Two+'/'+Price_Three,
                            type:"GET",
                            dataType:"json",
                            beforeSend: function(){
                                $('#loader').css("visibility", "visible");
                            },

                            success:function(data) {
                                $.each(data, function(key, value){

                    alert('تم الاضافه بنجاح');

                   $('#P_Ar_Name').val('');
   $('#P_En_Name').val('');
   $('#Rate').val(1);
   $('#Barcode').val('');
   $('#Price').val(0);
   $('#Price_Two').val(0);
   $('#Price_Three').val(0);


                                });
                            },
                            complete: function(){
                                $('#loader').css("visibility", "hidden");
                            }
                        });
                    }else{

                       alert('حقل الاسم مطلوب');

                    }



   }
</script>
<!-- Add New Vendor -->
<script>
   function NewVendor(){

   var co = $('#CODE').val();
   var Name = $('#Name').val();
   var NameEn = $('#NameEn').val();
   var PriceLevel = $('#PriceLevel').val();
   var LANG = $('#LANG').val();

       if(NameEn == ''){

               NameEn = Name ;
          }

         if(Name) {
                        $.ajax({
                            url: 'AddNewVendor/'+co+'/'+Name+'/'+PriceLevel,
                            type:"GET",
                            data:{
                              NameEn:NameEn
                            },
                            dataType:"json",
                            beforeSend: function(){
                                $('#loader').css("visibility", "visible");
                            },

                            success:function(data) {
                                $.each(data, function(key, value){

                    if(LANG == 'ar'){
                            alert('تم الاضافه بنجاح');
                       }else{
                           alert('Added Successfully');
                       }

                   $('#Name').val('');
                                });
                            },
                            complete: function(){
                                $('#loader').css("visibility", "hidden");
                            }
                        });
                    }else{

                      if(LANG == 'ar'){
                       alert('حقل الاسم مطلوب');
                      }else{

                       alert('Name Input is Required');
                      }

                    }



   }
</script>
<!-- Add Serial -->
<script>
   function FunSerial(r) {

            var P_Ar_Name = $("#P_Ar_Name"+r).val();
             var P_En_Name= $("#P_En_Name"+r).val();
             var Product = $("#Product"+r).val();
             var UnitID = $("#UnitPurch"+r).val();
             var UnitName = $("#UnitPurchName"+r).val();
             var Qty = $("#Qty"+r).val();
             var Barcode = $("#CodePurch"+r).val();
             var Price = $("#Price"+r).val();
             var Total = $("#Total"+r).val();
             var ExpDate = $("#ExpDate"+r).val();
             var PurchTax = $("#PurchTax"+r).val();
             var TotalTax = $("#Tax"+r).val();
             var Discount = $("#Discount"+r).val();
            var TDiscPro = $("#TDiscPro"+r).val();
             var TotalBFTax = $("#TotalBFTax"+r).val();
             var StorePurch = $("#StorePurch"+r).val();
             var StorePurchName = $("#StorePurchName"+r).val();
             var PurchTax = $("#PurchTax"+r).val();
   var Price_Sale = $("#Price_Sale"+r).val();
            var i=0;

       var Exp='';
        var ED = $("#ED").val();
     var DISCSHOW = $("#DISCSHOW").val();
        var TOTBFSHOW = $("#TOTBFSHOW").val();
        var TAXSHOW = $("#TAXSHOW").val();
        var STORESHOW = $("#STORESHOW").val();

         if(DISCSHOW == 1){

       var  DISCSHOWW = "";

       }else{

             var  DISCSHOWW = "none";
       }


          if(TOTBFSHOW == 1){

        var TOTBFSHOWW = "";

       }else{

            var TOTBFSHOWW  = "none";
       }



          if(TAXSHOW == 1){

      var   TAXSHOWW = "";

       }else{

            var   TAXSHOWW = "none";
       }



          if(STORESHOW == 1){

        var STORESHOWW = "";

       }else{

             var STORESHOWW  = "none";
       }


         if(ED == 1){

         Exp = "<td>" + ExpDate + "</td>";

       }else{

           Exp = "";
       }

       if(parseFloat(Qty) < 0){
          Qty = 1 ;
          }

                    document.getElementById("AddBtnPur"+r).style.display = "none";
             document.getElementById("Row"+r).style.display = "none";

                                                                 var LANG = $("#LANG").val();
   if(LANG == 'ar' ){
          var Nemo = P_Ar_Name ;
          }else{
             var Nemo = P_En_Name ;
          }
             var markup = "<tr><td>" + Nemo + " ("+Qty+")</td><td>" + UnitName + "</td><td><div class='row'>";

            for(i ; i < Qty ; i++){
       markup += "<div class='col-md-12'><input type='text' onkeyup='SERIAL("+Qty+")' onclick='SERIAL("+Qty+","+Product+")' onkeypress='SERIAL("+Qty+","+Product+")' id='SRCODE"+i+Product+"' name='P_Code[]'  class='form-control inputs'><input type='hidden' name='P_Ar_Name[]' value='"+P_Ar_Name+"'><input type='hidden' name='P_En_Name[]' value='"+P_En_Name+"'><input type='hidden' name='Unit[]' value='"+UnitID+"'><input class='Qun' type='hidden' name='Qty[]' value='1'><input type='hidden' name='Price[]' value='"+Price+"'><input class='Disc' type='hidden' name='Discount[]' value='"+Discount+"'><input class='TotalBFTax' type='hidden' name='TotalBFTax[]' value='"+TotalBFTax+"'><input class='TotalTax' type='hidden' name='TotalTax[]' value='"+TotalTax+"'><input class='Tot' type='hidden' name='Total[]' value='"+Total+"'><input  type='hidden' name='StorePurch[]' value='"+StorePurch+"'><input type='hidden' name='Exp_Date[]' value='"+ExpDate+"'><input type='hidden' name='Product[]' value='"+Product+"'><input type='hidden' name='VOne[]' value=''><input type='hidden' name='VTwo[]' value=''><input type='hidden' name='V_Name[]' value=''><input type='hidden' name='VV_Name[]' value=''><input type='hidden' name='PurchTax[]' value='"+PurchTax+"'><input type='hidden' name='Price_Sale[]' value='"+Price_Sale+"'><input type='hidden' class='TDISCOOO' id='TDiscProEdit"+r+"' name='TDiscPro[]' value='"+TDiscPro+"'><input type='hidden' id='Enough"+r+"' name='AllEnough[]' class='ENO'  value='0'></div>";
               }

            markup += "</div></td><td>1</td><td>" + Price + "</td><td style='display:"+DISCSHOWW+"'>" + Discount + "</td><td style='display:"+TOTBFSHOWW+"'>" + TotalBFTax + "</td><td style='display:"+TAXSHOWW+"'>" + TotalTax + "</td><td>" + Total + "</td><td style='display:"+STORESHOWW+"'>" + StorePurchName + "</td>"+Exp+"<td><button id='DelAssem' type='button' class='btn btn-default'><i class='fal fa-trash'></i></button></td></tr>";



             $("#data-dt").append(markup);

    $("#Data").hide();

          $.fn.rowCount = function() {
     return $('tr', $(this).find('tbody')).length;
   };

   var rowctr = $('#dt').rowCount();

    var sumQ = 0;
   $('.Qun').each(function(){
   sumQ += parseFloat($(this).val());
   });

    var sumT = 0;
   $('.Tot').each(function(){
   sumT += parseFloat($(this).val());
   });

        var sumD = 0;
   $('.Disc').each(function(){
   sumD += parseFloat($(this).val());
   });


           var sumBF = 0;
   $('.TotalBFTax').each(function(){
   sumBF += parseFloat($(this).val());
   });

           var sumTax = 0;
   $('.TotalTax').each(function(){
   sumTax += parseFloat($(this).val());
   });


               var sumTTD = 0;
   $('.TDISCOOO').each(function(){
   sumTTD += parseFloat($(this).val());
   });
   $('#Product_Numbers').val(parseFloat(rowctr));
   $('#Product_NumbersHide').val(parseFloat(rowctr));

   $('#Total_Qty').val(parseFloat(sumQ));
   $('#Total_QtyHide').val(parseFloat(sumQ));

   $('#Total_Price').val(parseFloat(sumT) + parseFloat(sumTTD) -  parseFloat(sumTax));
   $('#Total_PriceHide').val(parseFloat(sumT) + parseFloat(sumTTD) -  parseFloat(sumTax));
   $('#Total_PriceW').val(parseFloat(sumT));
       $('#Total_Bill').val(parseFloat(sumT));

           $('#Total_DiscountTT').val(parseFloat(sumTTD).toFixed(2));
   $('#Total_DiscountHideTT').val(parseFloat(sumTTD).toFixed(2));

       $('#Total_Discount').val(parseFloat(sumD).toFixed(2));
   $('#Total_DiscountHide').val(parseFloat(sumD).toFixed(2));

          $('#Total_Bf_Taxes').val(parseFloat(sumBF)+parseFloat(sumTTD));
   $('#Total_Bf_TaxesHide').val(parseFloat(sumBF)+parseFloat(sumTTD));

          $('#Total_Taxes').val(parseFloat(sumTax).toFixed(2));
   $('#Total_TaxesHide').val(parseFloat(sumTax).toFixed(2));



       if(rowctr == 0){
        document.getElementById("Submit").style.display = "none";

       }else{

        document.getElementById("Submit").style.display = "block";
       }

             var Pay =$('#Payment_Method').val();
        var Stat =$('#Status').val();
        var paid =$('#paid').val();

      if(Pay == 'Later' &&  Stat == 0 ){

             if(paid == 0 || paid < 0){
        document.getElementById("Submit").style.display = "none";
             }else{

           if(rowctr == 0){
        document.getElementById("Submit").style.display = "none";

       }else{

        document.getElementById("Submit").style.display = "block";
       }



             }

       }else{

       if(rowctr == 0){
        document.getElementById("Submit").style.display = "none";

       }else{

        document.getElementById("Submit").style.display = "block";
       }
       }


                               var Pay =$('#Payment_Method').val();
        var Stat =$('#Status').val();
        var paid =$('#paid').val();
        var resdiual =$('#resdiual').val();
        var total =$('#Total_PriceHide').val();
         var totalDisc =$('#Total_DiscountHideTT').val();

          var totalTax =$('#Total_TaxesHide').val();


       var RR= (parseFloat(total) + parseFloat(totalTax) ) - (parseFloat(resdiual) + parseFloat(totalDisc));
           var RRR= parseFloat(total) - (parseFloat(resdiual) + parseFloat(totalDisc));
          $('#TAFterD').val(parseFloat(RRR).toFixed(2));
         $('#paid').val(parseFloat(RR).toFixed(2));


               var Pay =$('#Payment_Method').val();
      if(Pay == 'Later'){


                   $('#paid').val('');

       }


        $('#data-dt').on('click', '#DelAssem', function(e){
                $(this).closest('tr').remove();

      $.fn.rowCount = function() {
     return $('tr', $(this).find('tbody')).length;
   };

   var rowctr = $('#dt').rowCount();

    var sumQ = 0;
   $('.Qun').each(function(){
   sumQ += parseFloat($(this).val());
   });

    var sumT = 0;
   $('.Tot').each(function(){
   sumT += parseFloat($(this).val());
   });

        var sumD = 0;
   $('.Disc').each(function(){
   sumD += parseFloat($(this).val());
   });


           var sumBF = 0;
   $('.TotalBFTax').each(function(){
   sumBF += parseFloat($(this).val());
   });

           var sumTax = 0;
   $('.TotalTax').each(function(){
   sumTax += parseFloat($(this).val());
   });


                 var sumTTD = 0;
   $('.TDISCOOO').each(function(){
   sumTTD += parseFloat($(this).val());
   });
   $('#Product_Numbers').val(parseFloat(rowctr));
   $('#Product_NumbersHide').val(parseFloat(rowctr));

   $('#Total_Qty').val(parseFloat(sumQ));
   $('#Total_QtyHide').val(parseFloat(sumQ));

   $('#Total_Price').val(parseFloat(sumT) + parseFloat(sumTTD) -  parseFloat(sumTax));
   $('#Total_PriceHide').val(parseFloat(sumT) + parseFloat(sumTTD) -  parseFloat(sumTax));
   $('#Total_PriceW').val(parseFloat(sumT));
       $('#Total_Bill').val(parseFloat(sumT));

           $('#Total_DiscountTT').val(parseFloat(sumTTD).toFixed(2));
   $('#Total_DiscountHideTT').val(parseFloat(sumTTD).toFixed(2));

       $('#Total_Discount').val(parseFloat(sumD).toFixed(2));
   $('#Total_DiscountHide').val(parseFloat(sumD).toFixed(2));

          $('#Total_Bf_Taxes').val(parseFloat(sumBF)+parseFloat(sumTTD));
   $('#Total_Bf_TaxesHide').val(parseFloat(sumBF)+parseFloat(sumTTD));

          $('#Total_Taxes').val(parseFloat(sumTax).toFixed(2));
   $('#Total_TaxesHide').val(parseFloat(sumTax).toFixed(2));



       if(rowctr == 0){
        document.getElementById("Submit").style.display = "none";

       }else{

        document.getElementById("Submit").style.display = "block";
       }

                  var Pay =$('#Payment_Method').val();
        var Stat =$('#Status').val();
        var paid =$('#paid').val();

     if(Pay == 'Later' &&  Stat == 0 ){

             if(paid == 0 || paid < 0){
        document.getElementById("Submit").style.display = "none";
             }else{

           if(rowctr == 0){
        document.getElementById("Submit").style.display = "none";

       }else{

        document.getElementById("Submit").style.display = "block";
       }



             }

       }else{

       if(rowctr == 0){
        document.getElementById("Submit").style.display = "none";

       }else{

        document.getElementById("Submit").style.display = "block";
       }
       }


                               var Pay =$('#Payment_Method').val();
        var Stat =$('#Status').val();
        var paid =$('#paid').val();
        var resdiual =$('#resdiual').val();
        var total =$('#Total_PriceHide').val();
        var totalDisc =$('#Total_DiscountHideTT').val();

          var totalTax =$('#Total_TaxesHide').val();


       var RR= (parseFloat(total) + parseFloat(totalTax) ) - (parseFloat(resdiual) + parseFloat(totalDisc));
           var RRR= parseFloat(total) - (parseFloat(resdiual) + parseFloat(totalDisc));
          $('#TAFterD').val(parseFloat(RRR).toFixed(2));
         $('#paid').val(parseFloat(RR).toFixed(2));


               var Pay =$('#Payment_Method').val();
      if(Pay == 'Later'){


                   $('#paid').val('');

       }


 var EN = 0;
    var Total_PriceW =$('#Total_PriceW').val();

  $('.ENO').each(function(){
   EN += parseFloat($(this).val());
   });
 if(EN == 0){

             if(Total_PriceW == 'NaN'){
                   document.getElementById("Submit").style.display = "none";
                    }else{
        document.getElementById("Submit").style.display = "block";
             }

       }else{

        document.getElementById("Submit").style.display = "none";
       }

                    })


     }
</script>
<!-- Duplicate Serial -->
<script>
   function SERIAL(r,p){

            var i=0;
            var arr=[];

   for(i ; i < r ; i++){
      var val= $('#SRCODE'+i+p).val();
          arr.push(val);
   }

       var encounteredIndices = {};
       for(var i = 0; i < arr.length; i++)
    if (encounteredIndices[arr[i]]){
      console.log(i);
        document.getElementById('SRCODE'+i+p).style.border="3px solid darkred";
           document.getElementById("Submit").style.display = "none";
    }else{
       encounteredIndices[arr[i]] = 1;
         document.getElementById('SRCODE'+i+p).style.border="none";
           document.getElementById("Submit").style.display = "block";
    }

   }
</script>
<!-- Recipy Voucher Add Ajax -->
<script>
   $(document).ready(function(){

   fetch_customer_data();

   function fetch_customer_data(Creditor = '',AccountCode='',Shift='',Safe='',Coin='',Draw='',STORE='')
   {
    $.ajax({
     url:'AddReciptVoucherAjax',
     method:'GET',
     data:{Creditor:Creditor,AccountCode:AccountCode,Shift:Shift,Safe:Safe,Coin:Coin,Draw:Draw,STORE:STORE},
     dataType:'json',
     success:function(data)
     {

         alert('تم الاضافه بنجاح');

         $('#Creditor').val('');
   $('#AccountCodeF').empty();

     }
    })
   }

   $(document).on('click', '#ReciptVoucher', function(){

    var Creditor = $('#Creditor').val();
    var AccountCode = $('#AccountCodeF').val();
    var Shift = null;
    var Safe = $('#SafeF').val();
    var Coin = $('#CoinF').val();
    var Draw = 1;
    var STORE = $('#STOREF').val();

      if(Creditor != '' && AccountCode != ''){
       fetch_customer_data(Creditor,AccountCode,Shift,Safe,Coin,Draw,STORE);
      }

   });




   });

</script>
<!-- Payment Voucher Add Ajax -->
<script>
   $(document).ready(function(){

   fetch_customer_data();

   function fetch_customer_data(Debitor = '',AccountCodee='',Shift='',Safe='',Coin='',Draw='',STORE='')
   {
    $.ajax({
     url:'AddPaymentVoucherAjax',
     method:'GET',
     data:{Debitor:Debitor,AccountCodee:AccountCodee,Shift:Shift,Safe:Safe,Coin:Coin,Draw:Draw,STORE:STORE},
     dataType:'json',
     success:function(data)
     {

         alert('تم الاضافه بنجاح');

         $('#Debitor').val('');
   $('#AccountCodee').empty();

     }
    })
   }

   $(document).on('click', '#PaymentVoucher', function(){

    var Debitor = $('#Debitor').val();
    var AccountCodee = $('#AccountCodee').val();
    var Shift = null;
    var Safe = $('#SafeP').val();
    var Coin = $('#CoinP').val();
    var Draw = 1;
    var STORE = $('#STOREP').val();

      if(Debitor != '' && AccountCodee != ''){
       fetch_customer_data(Debitor,AccountCodee,Shift,Safe,Coin,Draw,STORE);
      }

   });





   });

</script>
<!-- Change Tax -->
<script>
   function  TAXPurch(r){

   var countryId = $('#OLDTAX'+r).val();
   var Pro = $('#Product'+r).val();
   var Un = $('#UnitPurch'+r).val();
   var CO = $('#CodePurch'+r).val();
                   if(countryId) {
                       $.ajax({
                           url: 'TaxNamePurchasesFilter/'+countryId,
                           type:"GET",
                           dataType:"json",
                           beforeSend: function(){
                               $('#loader').css("visibility", "visible");
                           },

                           success:function(data) {


                     $('#TaxRate'+r).val(data.rate);
                     $('#TaxType'+r).val(data.type);
                     $('#PurchTax'+r).val(data.tax);

   var TaxRate = $("#TaxRate"+r).val();
   var TaxType = $("#TaxType"+r).val();
   var Qty = $("#Qty"+r).val();
   var Price = $("#Price"+r).val();
   var Discount = $("#Discount"+r).val();
   var i = 0;
   var ii=0;
   var iii=0;
      var DIS = $("#DIS").val();

     if(parseFloat(DIS) == 0){

         Discount=$("#Discount"+r).val();

     }else{

         var d= Discount / 100 ;
         var Multi= parseFloat(Price) ;
        var dd= Multi * d;

      Discount=  dd;

     }
         var BFG =  parseFloat(Qty) * parseFloat(Discount) ;
           $("#TDiscPro"+r).val(parseFloat(BFG));

     if(TaxType == 1){
         //Precent

      var BF =  parseFloat(Qty) *  (parseFloat(Price)  -  parseFloat(Discount) );
      var BFF =  parseFloat(Qty) *  parseFloat(Price)  ;

     $("#TotalBFTax"+r).val(parseFloat(BFF).toFixed(2));

     i =    parseFloat(TaxRate)  / 100 ;

      ii=  parseFloat(BF) * parseFloat(i) ;

         $("#Tax"+r).val(parseFloat(ii).toFixed(2));

     iii =  parseFloat(BF) + parseFloat(ii) ;

         $("#Total"+r).val(parseFloat(iii).toFixed(2));

     }else if(TaxType == 2){
        //Number
        var BF =  parseFloat(Qty) *  (parseFloat(Price)  -  parseFloat(Discount) );
        var BFF =  parseFloat(Qty) *  parseFloat(Price) ;
       $("#TotalBFTax"+r).val(parseFloat(BFF).toFixed(2));

          i =    parseFloat(TaxRate)   ;

         $("#Tax"+r).val(parseFloat(i).toFixed(2));

     iii =  parseFloat(BF) + parseFloat(i) ;

         $("#Total"+r).val(parseFloat(iii).toFixed(2));


     }


      var Total = $("#Total"+r).val();
      var TotalBFTax = $("#TotalBFTax"+r).val();
      var Tax = $("#Tax"+r).val();
      var UnitID = $("#UnitPurch"+r).val();
      var StorePurch = $("#StorePurch"+r).val();

     if(TaxRate == ''  || TaxType == ''  ||  Qty == '' || Price == '' || Discount == '' || Total == '' || TotalBFTax == '' || Tax == '' || UnitID == '' || StorePurch == ''){

      document.getElementById("AddBtnPur"+r).style.display = "none";
   }


     if(TaxRate != ''  && TaxType != ''  &&  Qty != ''  && Qty != 0 && Price != '' && Discount != '' && Total != '' && TotalBFTax != '' && Tax != '' && UnitID != '' && StorePurch != ''){

         if( Price == 0 ){

      document.getElementById("AddBtnPur"+r).style.display = "none";
   }else{

      document.getElementById("AddBtnPur"+r).style.display = "inline-block";
   }
   }







                           },
                           complete: function(){
                               $('#loader').css("visibility", "hidden");
                           }
                       });
                   } else {

                       $('select[name="state"]').empty();
                   }

   }
</script>
<script>
   $(document).ready(function(){
         $( "#Barcode" ).val(Math.floor(Math.random() * 1000000));
   });
</script>
<!-- Safe Balnces  -->
<script>
   $(document).ready(function() {

                  $('#SSAFE').on('change', function(){
                      var countryId = $(this).val();
                      if(countryId) {
                          $.ajax({
                              url: 'SafeBalanceFilter/'+countryId,
                              type:"GET",
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },

                              success:function(data) {
                                  $.each(data, function(key, value){


                    $('#SafeBalance').val(key);
                    $('#SafeBalance').val(value);
                                  });
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });
                      } else {

                          $('select[name="state"]').empty();
                      }

                  });

              });

     $(document).ready(function() {

                      var countryId = $("#SSAFE").val();
                      if(countryId) {
                          $.ajax({
                              url: 'SafeBalanceFilter/'+countryId,
                              type:"GET",
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },

                              success:function(data) {
                                  $.each(data, function(key, value){


                    $('#SafeBalance').val(key);
                    $('#SafeBalance').val(value);
                                  });
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });
                      } else {

                          $('select[name="state"]').empty();
                      }


              });
</script>
<script>
       $(document).ready(function(){
             $( "#Barcode" ).val(Math.floor(Math.random() * 1000000));
   });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.index', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp2\htdocs\erp\resources\views/admin/Purchases/PurchasesOrder.blade.php ENDPATH**/ ?>