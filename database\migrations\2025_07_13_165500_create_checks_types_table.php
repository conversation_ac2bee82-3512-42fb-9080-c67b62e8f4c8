<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateChecksTypesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('checks_types', function (Blueprint $table) {
            $table->id();
            
            // Check type information
            $table->string('Arabic_Name')->nullable();  // Arabic check type name
            $table->string('English_Name')->nullable(); // English check type name
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('checks_types');
    }
}
