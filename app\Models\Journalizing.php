<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Stancl\Tenancy\Database\Concerns\CentralConnection;

class Journalizing extends Model
{
    use HasFactory, CentralConnection;
       protected $table = 'journalizings';
      protected $fillable = [
        'Code',
        'Type',
        'TypeEn',
        'Code_Type',
        'Date',
        'Draw',
        'Coin',
        'Cost_Center',
        'Total_Debaitor',
        'Total_Creditor',
        'Note',
          'Branch',
           'Status',

    ];

             public function Branch()
    {
        return $this->belongsTo(Branches::class,'Branch');
    }

        public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }
        public function Cost_Center()
    {
        return $this->belongsTo(CostCenter::class,'Cost_Center');
    }

            public function JournalizingDetails()
    {
        return $this->hasOne(JournalizingDetails::class);
    }






}
