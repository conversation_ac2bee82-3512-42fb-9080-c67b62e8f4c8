<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateArticlesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('articles', function (Blueprint $table) {
            $table->id();
            
            // Article information
            $table->string('Title')->nullable();
            $table->string('TitleEn')->nullable();
            $table->text('Content')->nullable();
            $table->text('ContentEn')->nullable();
            $table->string('Image')->nullable();
            $table->string('Status')->default('1');
            $table->text('Meta_Description')->nullable();
            $table->text('Meta_Keywords')->nullable();
            $table->string('Slug')->nullable();
            $table->integer('Views')->default(0);
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('articles');
    }
}
