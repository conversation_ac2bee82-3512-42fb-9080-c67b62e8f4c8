<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSupPagesPartTwoEComDesignsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('sup_pages_part_two_e_com_designs', function (Blueprint $table) {
            $table->id();
            
            // Shop product design
            $table->string('Shop_Product_BG_Color')->default('#ffffff')->nullable();
            $table->string('Shop_Product_Group_BG_Color')->default('#f8f9fa')->nullable();
            $table->string('Shop_Product_Group_Txt_Color')->default('#000000')->nullable();
            $table->string('Shop_Product_Group_Hover_BG_Color')->default('#007bff')->nullable();
            $table->string('Shop_Product_Group_Hover_Txt_Color')->default('#ffffff')->nullable();
            $table->string('Shop_Product_Icon_BG_Color')->default('#007bff')->nullable();
            $table->string('Shop_Product_Icon_Txt_Color')->default('#ffffff')->nullable();
            $table->string('Shop_Product_Icon_Hover_BG_Color')->default('#0056b3')->nullable();
            $table->string('Shop_Product_Icon_Hover_Txt_Color')->default('#ffffff')->nullable();
            $table->string('Shop_Product_Txt_Color')->default('#000000')->nullable();
            $table->string('Shop_Product_Price_Color')->default('#007bff')->nullable();
            $table->string('Shop_Product_Rate_Color')->default('#ffc107')->nullable();
            
            // Shop filter design
            $table->string('Shop_Filter_Title_Color')->default('#000000')->nullable();
            $table->string('Shop_Filter_Txt_Color')->default('#666666')->nullable();
            $table->string('Shop_Filter_Search_BG_Color')->default('#f8f9fa')->nullable();
            $table->string('Shop_Filter_Search_Icon_BG_Color')->default('#007bff')->nullable();
            $table->string('Shop_Filter_Search_Icon_Txt_Color')->default('#ffffff')->nullable();
            $table->string('Shop_Filter_Search_Icon_BG_Hover_Color')->default('#0056b3')->nullable();
            $table->string('Shop_Filter_Search_Icon_Txt_Hover_Color')->default('#ffffff')->nullable();
            
            // Shop category design
            $table->string('Shop_Category_BG_Color')->default('#ffffff')->nullable();
            $table->string('Shop_Category_Txt_Color')->default('#000000')->nullable();
            $table->string('Shop_Category_Hover_BG_Color')->default('#f8f9fa')->nullable();
            $table->string('Shop_Category_Hover_Txt_Color')->default('#007bff')->nullable();
            $table->string('Shop_Category_Active_BG_Color')->default('#007bff')->nullable();
            $table->string('Shop_Category_Active_Txt_Color')->default('#ffffff')->nullable();
            
            // Shop brand design
            $table->string('Shop_Brand_BG_Color')->default('#ffffff')->nullable();
            $table->string('Shop_Brand_Txt_Color')->default('#000000')->nullable();
            $table->string('Shop_Brand_Hover_BG_Color')->default('#f8f9fa')->nullable();
            $table->string('Shop_Brand_Hover_Txt_Color')->default('#007bff')->nullable();
            $table->string('Shop_Brand_Active_BG_Color')->default('#007bff')->nullable();
            $table->string('Shop_Brand_Active_Txt_Color')->default('#ffffff')->nullable();
            
            // Shop price filter design
            $table->string('Shop_Price_Filter_BG_Color')->default('#ffffff')->nullable();
            $table->string('Shop_Price_Filter_Txt_Color')->default('#000000')->nullable();
            $table->string('Shop_Price_Filter_Button_BG_Color')->default('#007bff')->nullable();
            $table->string('Shop_Price_Filter_Button_Txt_Color')->default('#ffffff')->nullable();
            $table->string('Shop_Price_Filter_Button_Hover_BG_Color')->default('#0056b3')->nullable();
            $table->string('Shop_Price_Filter_Button_Hover_Txt_Color')->default('#ffffff')->nullable();
            
            // Shop sorting design
            $table->string('Shop_Sort_BG_Color')->default('#f8f9fa')->nullable();
            $table->string('Shop_Sort_Txt_Color')->default('#000000')->nullable();
            $table->string('Shop_Sort_Select_BG_Color')->default('#ffffff')->nullable();
            $table->string('Shop_Sort_Select_Txt_Color')->default('#000000')->nullable();
            $table->string('Shop_Sort_Select_Border_Color')->default('#ddd')->nullable();
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('sup_pages_part_two_e_com_designs');
    }
}
