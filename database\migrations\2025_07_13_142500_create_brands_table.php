<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateBrandsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('brands', function (Blueprint $table) {
            $table->id();
            
            // Basic brand information
            $table->string('Name')->nullable();
            $table->string('NameEn')->nullable();
            $table->string('Image')->nullable();
            $table->text('Note')->nullable();
            
            // Display settings
            $table->string('Sales_Show')->nullable();
            $table->string('Store_Show')->nullable();
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('brands');
    }
}
