<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Stancl\Tenancy\Database\Concerns\CentralConnection;

class Notifications extends Model
{
    use HasFactory, CentralConnection;
     protected $table = 'notifications';
      protected $fillable = [
        'Date',
        'Status',
        'Noti_Ar_Name',
        'Noti_En_Name',
        'Type',
        'TypeEn',
        'Type_Code',
        'Emp',
        'Client',
        'Product',
        'Store',
        'Safe',



    ];

        public function Emp()
    {
        return $this->belongsTo(Employess::class,'Emp');
    }

    public function Client()
    {
        return $this->belongsTo(AcccountingManual::class,'Client');
    }

    public function Product()
    {
        return $this->belongsTo(Products::class,'Product');
    }

         public function Safe()
    {
        return $this->belongsTo(AcccountingManual::class,'Safe');
    }

         public function Store()
    {
        return $this->belongsTo(Stores::class,'Store');
    }

}
