<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Stancl\Tenancy\Database\Concerns\CentralConnection;

class InterviewsTypes extends Model
{
    use HasFactory, CentralConnection;
                  protected $table = 'interviews_types';
      protected $fillable = [
        'Arabic_Name',
        'English_Name',

    ];


                       public function Interviews()
    {
        return $this->hasOne(Interviews::class);
    }



}
