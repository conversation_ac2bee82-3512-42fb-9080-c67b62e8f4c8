@extends('admin.index')
@section('content')
@php
use App\Models\CrmDefaultData;
$def=CrmDefaultData::orderBy('id','desc')->first();
@endphp

@if(is_null($def))
    @php
        $def = new App\Models\CrmDefaultData();
        $def->Nationality = null;
    @endphp
@endif
  <title>{{trans('admin.Vendors')}}</title>

 <main id="js-page-content" role="main" class="page-content">
                    <ol class="breadcrumb page-breadcrumb">
                        <li class="breadcrumb-item"><a href="javascript:void(0);">{{trans('admin.Purchases')}}</a></li>
                        <li class="breadcrumb-item active">{{trans('admin.Vendors')}}</li>
                        <li class="position-absolute pos-top pos-right d-none d-sm-block"><span
                                class="js-get-date"></span></li>
                    </ol>

                    <div class="row">
                        <div class="col-xl-12">
                            <div id="panel-1" class="panel">
                                <div class="panel-hdr">
                                    <h2>
                                        <span class="fw-300"><i> {{trans('admin.Vendors')}}</i></span>
                                    </h2>

                                    <div class="panel-toolbar">
                                 <a href="{{'ExportAllVendors'}}" class="btn btn-success btn-sm">
                                 Export Excel
                                            </a>
                                  @can('اضافه مورد')
                     <button type="button" class="btn btn-default" data-toggle="modal" data-target="#default-example-modal-xl">{{trans('admin.AddNew')}}</button>
                                @endcan

                            <button class="btn btn-primary btn-sm mx-3" data-toggle="dropdown">Table Style</button>
                 <div class="dropdown-menu dropdown-menu-animated dropdown-menu-right position-absolute pos-top">
                                            <button class="dropdown-item active" data-action="toggle"
                                                data-class="table-bordered" data-target="#dt-basic-example"> Bordered
                                                Table </button>
                                            <button class="dropdown-item" data-action="toggle" data-class="table-sm"
                                                data-target="#dt-basic-example"> Smaller Table </button>
                                            <button class="dropdown-item" data-action="toggle" data-class="table-dark"
                                                data-target="#dt-basic-example"> Table Dark </button>
                                            <button class="dropdown-item active" data-action="toggle"
                                                data-class="table-hover" data-target="#dt-basic-example"> Table Hover
                                            </button>
                                            <button class="dropdown-item active" data-action="toggle"
                                                data-class="table-stripe" data-target="#dt-basic-example"> Table
                                                Stripped </button>
                                            <div class="dropdown-divider m-0"></div>
                                            <div class="dropdown-multilevel dropdown-multilevel-left">
                                                <div class="dropdown-item">
                                                    tbody color
                                                </div>
                                                <div class="dropdown-menu no-transition-delay">
                                                    <div class="js-tbody-colors dropdown-multilevel dropdown-multilevel-left d-flex flex-wrap"
                                                        style="width: 15.9rem; padding: 0.5rem">
                                                        <a href="javascript:void(0);" data-bg="bg-primary-100"
                                                            class="btn d-inline-block bg-primary-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-200"
                                                            class="btn d-inline-block bg-primary-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-300"
                                                            class="btn d-inline-block bg-primary-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-400"
                                                            class="btn d-inline-block bg-primary-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-500"
                                                            class="btn d-inline-block bg-primary-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-600"
                                                            class="btn d-inline-block bg-primary-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-700"
                                                            class="btn d-inline-block bg-primary-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-800"
                                                            class="btn d-inline-block bg-primary-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-900"
                                                            class="btn d-inline-block bg-primary-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-100"
                                                            class="btn d-inline-block bg-success-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-200"
                                                            class="btn d-inline-block bg-success-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-300"
                                                            class="btn d-inline-block bg-success-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-400"
                                                            class="btn d-inline-block bg-success-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-500"
                                                            class="btn d-inline-block bg-success-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-600"
                                                            class="btn d-inline-block bg-success-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-700"
                                                            class="btn d-inline-block bg-success-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-800"
                                                            class="btn d-inline-block bg-success-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-900"
                                                            class="btn d-inline-block bg-success-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-100"
                                                            class="btn d-inline-block bg-info-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-200"
                                                            class="btn d-inline-block bg-info-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-300"
                                                            class="btn d-inline-block bg-info-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-400"
                                                            class="btn d-inline-block bg-info-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-500"
                                                            class="btn d-inline-block bg-info-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-600"
                                                            class="btn d-inline-block bg-info-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-700"
                                                            class="btn d-inline-block bg-info-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-800"
                                                            class="btn d-inline-block bg-info-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-900"
                                                            class="btn d-inline-block bg-info-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-100"
                                                            class="btn d-inline-block bg-danger-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-200"
                                                            class="btn d-inline-block bg-danger-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-300"
                                                            class="btn d-inline-block bg-danger-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-400"
                                                            class="btn d-inline-block bg-danger-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-500"
                                                            class="btn d-inline-block bg-danger-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-600"
                                                            class="btn d-inline-block bg-danger-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-700"
                                                            class="btn d-inline-block bg-danger-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-800"
                                                            class="btn d-inline-block bg-danger-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-900"
                                                            class="btn d-inline-block bg-danger-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-100"
                                                            class="btn d-inline-block bg-warning-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-200"
                                                            class="btn d-inline-block bg-warning-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-300"
                                                            class="btn d-inline-block bg-warning-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-400"
                                                            class="btn d-inline-block bg-warning-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-500"
                                                            class="btn d-inline-block bg-warning-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-600"
                                                            class="btn d-inline-block bg-warning-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-700"
                                                            class="btn d-inline-block bg-warning-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-800"
                                                            class="btn d-inline-block bg-warning-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-900"
                                                            class="btn d-inline-block bg-warning-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-100"
                                                            class="btn d-inline-block bg-fusion-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-200"
                                                            class="btn d-inline-block bg-fusion-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-300"
                                                            class="btn d-inline-block bg-fusion-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-400"
                                                            class="btn d-inline-block bg-fusion-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-500"
                                                            class="btn d-inline-block bg-fusion-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-600"
                                                            class="btn d-inline-block bg-fusion-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-700"
                                                            class="btn d-inline-block bg-fusion-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-800"
                                                            class="btn d-inline-block bg-fusion-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-900"
                                                            class="btn d-inline-block bg-fusion-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg=""
                                                            class="btn d-inline-block bg-white border width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="dropdown-multilevel dropdown-multilevel-left">
                                                <div class="dropdown-item">
                                                    thead color
                                                </div>
                                                <div class="dropdown-menu no-transition-delay">
                                                    <div class="js-thead-colors dropdown-multilevel dropdown-multilevel-left d-flex flex-wrap"
                                                        style="width: 15.9rem; padding: 0.5rem">
                                                        <a href="javascript:void(0);" data-bg="bg-primary-100"
                                                            class="btn d-inline-block bg-primary-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-200"
                                                            class="btn d-inline-block bg-primary-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-300"
                                                            class="btn d-inline-block bg-primary-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-400"
                                                            class="btn d-inline-block bg-primary-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-500"
                                                            class="btn d-inline-block bg-primary-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-600"
                                                            class="btn d-inline-block bg-primary-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-700"
                                                            class="btn d-inline-block bg-primary-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-800"
                                                            class="btn d-inline-block bg-primary-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-900"
                                                            class="btn d-inline-block bg-primary-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-100"
                                                            class="btn d-inline-block bg-success-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-200"
                                                            class="btn d-inline-block bg-success-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-300"
                                                            class="btn d-inline-block bg-success-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-400"
                                                            class="btn d-inline-block bg-success-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-500"
                                                            class="btn d-inline-block bg-success-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-600"
                                                            class="btn d-inline-block bg-success-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-700"
                                                            class="btn d-inline-block bg-success-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-800"
                                                            class="btn d-inline-block bg-success-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-900"
                                                            class="btn d-inline-block bg-success-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-100"
                                                            class="btn d-inline-block bg-info-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-200"
                                                            class="btn d-inline-block bg-info-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-300"
                                                            class="btn d-inline-block bg-info-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-400"
                                                            class="btn d-inline-block bg-info-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-500"
                                                            class="btn d-inline-block bg-info-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-600"
                                                            class="btn d-inline-block bg-info-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-700"
                                                            class="btn d-inline-block bg-info-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-800"
                                                            class="btn d-inline-block bg-info-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-900"
                                                            class="btn d-inline-block bg-info-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-100"
                                                            class="btn d-inline-block bg-danger-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-200"
                                                            class="btn d-inline-block bg-danger-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-300"
                                                            class="btn d-inline-block bg-danger-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-400"
                                                            class="btn d-inline-block bg-danger-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-500"
                                                            class="btn d-inline-block bg-danger-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-600"
                                                            class="btn d-inline-block bg-danger-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-700"
                                                            class="btn d-inline-block bg-danger-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-800"
                                                            class="btn d-inline-block bg-danger-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-900"
                                                            class="btn d-inline-block bg-danger-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-100"
                                                            class="btn d-inline-block bg-warning-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-200"
                                                            class="btn d-inline-block bg-warning-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-300"
                                                            class="btn d-inline-block bg-warning-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-400"
                                                            class="btn d-inline-block bg-warning-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-500"
                                                            class="btn d-inline-block bg-warning-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-600"
                                                            class="btn d-inline-block bg-warning-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-700"
                                                            class="btn d-inline-block bg-warning-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-800"
                                                            class="btn d-inline-block bg-warning-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-900"
                                                            class="btn d-inline-block bg-warning-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-100"
                                                            class="btn d-inline-block bg-fusion-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-200"
                                                            class="btn d-inline-block bg-fusion-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-300"
                                                            class="btn d-inline-block bg-fusion-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-400"
                                                            class="btn d-inline-block bg-fusion-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-500"
                                                            class="btn d-inline-block bg-fusion-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-600"
                                                            class="btn d-inline-block bg-fusion-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-700"
                                                            class="btn d-inline-block bg-fusion-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-800"
                                                            class="btn d-inline-block bg-fusion-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-900"
                                                            class="btn d-inline-block bg-fusion-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg=""
                                                            class="btn d-inline-block bg-white border width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="panel-container show">
                                      <span id="ex"> @include('admin.layouts.messages')</span>
                                    <div class="panel-content">
                                        <!-- datatable start -->
                                        <div id="mobile-overflow">
                                        <table id="dt-basic-example"
                                            class="table table-bordered table-hover table-striped mobile-width">
                                            <thead class="bg-highlight">
                                                <tr>
                                                    <th>{{trans('admin.Code')}}</th>
                                                    <th>{{trans('admin.Arabic_Name')}}</th>
                                                    <th>{{trans('admin.English_Name')}}</th>
                                                    <th>{{trans('admin.Phone')}}</th>

                                                    <th>{{trans('admin.Price_Level')}}</th>

                                                    <th>{{trans('admin.Account_Code')}}</th>
                                                    <th>{{trans('admin.Responsible')}}</th>

                                                     <th>{{trans('admin.Data')}}</th>

                                                    <th>{{trans('admin.Actions')}}</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach($Vendors as $item)
                                                <tr>
                                                    <td>{{$item->Code}}</td>
                                                    <td>{{$item->Name}}</td>
                                                    <td>{{$item->NameEn}}</td>
                                                    <td>{{$item->Phone}}</td>

                                                    <td>{{$item->Price_Level}}</td>


                                                    <td>{{$item->Account()->first()->Code}}</td>
                                                    <td>@if(!empty($item->Responsible()->first()->Name))


                                                   {{app()->getLocale() == 'ar' ?$item->Responsible()->first()->Name :$item->Responsible()->first()->NameEn}}

                                                        @endif</td>
                                                            <td>
                                                        <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#show-data{{$item->id}}">
                                                           {{trans('admin.Data')}}
                                                        </button>
                                                    </td>


                                                    <td class="text-center">


                                          @can('حذف مورد')
             <button type="button" class="btn btn-default" data-toggle="modal" data-target="#Delete{{$item->id}}"><i class="fal fa-trash-alt"></i></button>
                                           @endcan
                                                  @can('تعديل مورد')
            <button type="button" class="btn btn-default" data-toggle="modal" data-target="#Edit{{$item->id}}"><i class="fal fa-edit"></i></button>
                                @endcan
                                                    </td>
                                                </tr>
                                                @endforeach
                                            </tbody>
                                            <tfoot>
                                                <tr>
                                                <th>{{trans('admin.Code')}}</th>
                                                    <th>{{trans('admin.Arabic_Name')}}</th>
                                                    <th>{{trans('admin.English_Name')}}</th>
                                                    <th>{{trans('admin.Phone')}}</th>

                                                    <th>{{trans('admin.Price_Level')}}</th>

                                                    <th>{{trans('admin.Account_Code')}}</th>
                                                    <th>{{trans('admin.Responsible')}}</th>

                                                     <th>{{trans('admin.Data')}}</th>

                                                    <th>{{trans('admin.Actions')}}</th>
                                                </tr>
                                            </tfoot>
                                        </table>
                                        </div>
                                        <!-- datatable end -->

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>


                    <!-- Modal Add-->
             <div class="modal fade" id="default-example-modal-xl" tabindex="-1" role="dialog" aria-hidden="true">
                        <div class="modal-dialog modal-xl" role="document">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">{{trans('admin.AddNew')}}  </h5>
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                        <span aria-hidden="true"><i class="fal fa-times"></i></span>
                                    </button>
                                </div>
                                <div class="modal-body">
                          <form action="{{url('AddVendors')}}" method="post" enctype="multipart/form-data">
                                        {!! csrf_field() !!}
                                        <div class="form-row">

                                                <div class="form-group col-lg-3">
                                                <label class="form-label" for="simpleinput"> {{trans('admin.Code')}}</label>
                        <input type="text" value="{{$Code}}"  class="form-control" disabled>
                            <input type="hidden" name="Code" value="{{$Code}}">
                                            </div>

                                            <div class="form-group col-lg-3">
                                                <label class="form-label" for="simpleinput"> {{trans('admin.Arabic_Name')}}</label>
                        <input type="text" name="Name" value="{{old('Name')}}"  class="form-control" required>
                                            </div>

                                            <div class="form-group col-lg-3">
                                                <label class="form-label" for="simpleinput"> {{trans('admin.English_Name')}}</label>
                        <input type="text" name="NameEn" value="{{old('NameEn')}}"  class="form-control" >
                                            </div>


                                              <div class="form-group col-lg-3">
                                <label class="form-label" for="simpleinput"> {{trans('admin.Phone')}}</label>
                     <input type="number" name="Phone" value="{{old('Phone')}}"  class="form-control">
                                            </div>

                                                      <div class="form-group col-lg-3">
                                <label class="form-label" for="simpleinput"> {{trans('admin.Phone2')}}</label>
                     <input type="number" name="Phone2" value="{{old('Phone2')}}"  class="form-control">
                                            </div>


                                                      <div class="form-group col-lg-4">
                                <label class="form-label" for="simpleinput"> {{trans('admin.Commercial_Register')}}</label>
                     <input type="number" name="Commercial_Register" value="{{old('Commercial_Register')}}"  class="form-control">
                                            </div>


                                                                       <div class="form-group col-lg-4">
                                <label class="form-label" for="simpleinput"> {{trans('admin.Tax_Card')}}</label>
                     <input type="number" name="Tax_Card" value="{{old('Tax_Card')}}"  class="form-control">
                                            </div>

                                                <div class="form-group col-md-4">
                                       <label class="form-label" for="">  {{trans('admin.Responsible')}}</label>
                                       <select class="select2 form-control w-100" name="Responsible">
                                          <option value="">{{trans('admin.Responsible')}}</option>
                                          @foreach($Employess as $emp)
                                          <option value="{{$emp->id}}">

                                     {{app()->getLocale() == 'ar' ?$emp->Name :$emp->NameEn}}

                                          </option>
                                          @endforeach
                                       </select>
                                    </div>

                                                         <div class="form-group col-md-4">
                                       <label class="form-label" for="">  {{trans('admin.Group')}}</label>
                                       <select class="select2 form-control w-100" name="Pro_Group" >
                                          <option value="">{{trans('admin.Group')}}</option>
                                          @foreach($Groups as $grp)
                                          <option value="{{$grp->id}}">

                                  {{app()->getLocale() == 'ar' ?$grp->Name :$grp->NameEn}}
                                          </option>
                                          @endforeach
                                       </select>
                                    </div>

                                                                   <div class="form-group col-md-4">
                                       <label class="form-label" for="">  {{trans('admin.Brand')}}</label>
                                       <select class="select2 form-control w-100" name="Brand" >
                                          <option value="">{{trans('admin.Brand')}}</option>
                                          @foreach($Brands as $bra)
                                          <option value="{{$bra->id}}">

                                            {{app()->getLocale() == 'ar' ?$bra->Name :$bra->NameEn}}
                                          </option>
                                          @endforeach
                                       </select>
                                    </div>





                                                                       <div class="form-group col-lg-4">
                                <label class="form-label" for="simpleinput"> {{trans('admin.Price_Level')}}</label>
                  <select class="select2 form-control" name="Price_Level" required>
                                      <option value="">{{trans('admin.Price_Level')}}</option>
                                      <option value="1" @if($def->Price_Level == 1) selected @endif>{{trans('admin.Level_1')}}</option>
                                      <option value="2"  @if($def->Price_Level == 2) selected @endif>{{trans('admin.Level_2')}}</option>
                                      <option value="3"  @if($def->Price_Level == 3) selected @endif>{{trans('admin.Level_3')}}</option>

                                       </select>
                                            </div>

                                                            <div class="form-group col-lg-4">
         <label class="form-label" for="">  {{trans('admin.Governrate')}}</label>
                                       <select class="select2 form-control w-100" id="Governrate" name="Governrate" >
                                          <option value="">{{trans('admin.Governrate')}}</option>
                                          @foreach($Governrates as $gov)
                                          <option value="{{$gov->id}}">

                              {{app()->getLocale() == 'ar' ?$gov->Arabic_Name :$gov->English_Name}}
                                          </option>
                                          @endforeach
                                       </select>


                                                                            </div>


                                                       <div class="form-group col-lg-4">
 <label class="form-label" for="">  {{trans('admin.City')}}</label>
                                       <select class="select2 form-control w-100" id="City" name="City" >

                                       </select>

                                                                            </div>


                                                        <div class="form-group col-lg-4">
 <label class="form-label" for="">  {{trans('admin.Place')}}</label>
                                       <select class="select2 form-control w-100" id="Place" name="Place" >

                                       </select>

                                                                            </div>


                                                <div class="form-group col-md-4">
                                       <label class="form-label" for=""> {{trans('admin.Tax_Registration_Number')}}   </label>
                                       <input type="text" name="Tax_Registration_Number" value="{{old('Tax_Registration_Number')}}" class="form-control">
                                    </div>


                                                     <div class="form-group col-md-4">
                                       <label class="form-label" for=""> {{trans('admin.Tax_activity_code')}}   </label>
                                       <input type="text" name="Tax_activity_code"  class="form-control">
                                    </div>
 <div class="form-group col-md-4">
                                       <label class="form-label" for="">    {{trans('admin.work_nature')}}    </label>
                              <select class="select2 form-control" name="work_nature" required>
                    <option value="P" >{{trans('admin.Person')}}</option>
                  <option value="B" >{{trans('admin.Egyptian_trading_company')}}</option>
                  <option value="F" >{{trans('admin.foreign_trading_company')}}</option>
                              </select>
                                    </div>
 <div class="form-group col-md-4">
                              <label class="form-label" for="">  {{trans('admin.Nationality')}} </label>
                              <select class="select2 form-control w-100"  name="Nationality" required>
                                 <option value="">{{trans('admin.Nationality')}}</option>
                                 @foreach($Nationality as $nation)
                                 <option value="{{$nation->id}}"  @if(optional($def)->Nationality == $nation->id) selected @endif>

                            {{app()->getLocale() == 'ar' ?$nation->Arabic_Name :$nation->English_Name}}
                                 </option>
                                 @endforeach
                              </select>
                           </div>



                                                                    <div class="form-group col-md-4">
                                       <label class="form-label" for=""> {{trans('admin.Buliding_Num')}}   </label>
                                       <input type="text" name="Buliding_Num"  class="form-control">
                                    </div>
                                                                    <div class="form-group col-md-4">
                                       <label class="form-label" for=""> {{trans('admin.Street')}}   </label>
                                       <input type="text" name="Street"  class="form-control">
                                    </div>

                                                                    <div class="form-group col-md-4">
                                       <label class="form-label" for=""> {{trans('admin.Postal_Code')}}   </label>
                                       <input type="text" name="Postal_Code"  class="form-control">
                                    </div>
                                                                    <div class="form-group col-md-4">
                                       <label class="form-label" for=""> {{trans('admin.tax_magistrate')}}   </label>
                                       <input type="text" name="tax_magistrate"  class="form-control">
                                    </div>


                                               <div class="form-group col-md-4">
                                       <label class="form-label" for=""> {{trans('admin.Floor')}}   </label>
                                       <input type="text" name="Floor"  class="form-control">
                                    </div>


                                                                    <div class="form-group col-md-4">
                                       <label class="form-label" for=""> {{trans('admin.Room')}}   </label>
                                       <input type="text" name="Room"  class="form-control">
                                    </div>


                                                                    <div class="form-group col-md-4">
                                       <label class="form-label" for=""> {{trans('admin.Landmark')}}   </label>
                                       <input type="text" name="Landmark"  class="form-control">
                                    </div>


                                                                    <div class="form-group col-md-4">
                                       <label class="form-label" for=""> {{trans('admin.Add_Info')}}   </label>
                                       <input type="text" name="Add_Info"  class="form-control">
                                    </div>

                                            <div class="form-group col-md-4">
                                       <label class="form-label" for=""> {{trans('admin.SearchCode')}}   </label>
                                       <input type="text" name="SearchCode"  class="form-control">
                                    </div>


                                        </div>
                                         <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">{{trans('admin.Close')}}</button>
                        <button type="submit" class="btn btn-primary"> {{trans('admin.Add')}}</button>
                                </div>
                                    </form>
                                </div>

                            </div>
                        </div>
                    </div>

      @foreach($Vendors as $item)

                    <!-- Modal Edit-->
                    <div class="modal fade" id="Edit{{$item->id}}" tabindex="-1" role="dialog" aria-hidden="true">
                        <div class="modal-dialog modal-xl" role="document">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">{{trans('admin.Edit')}}  </h5>
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                        <span aria-hidden="true"><i class="fal fa-times"></i></span>
                                    </button>
                                </div>

                                    <div class="modal-body">
                          <form action="{{url('EditVendors/'.$item->id)}}" method="post" enctype="multipart/form-data">
                                        {!! csrf_field() !!}
                            <div class="form-row">

                                            <input type="hidden" name="Account" value="{{$item->Account}}">
                                            <input type="hidden" name="User" value="{{$item->User}}">

                                                <div class="form-group col-lg-3">
                                                <label class="form-label" for="simpleinput"> {{trans('admin.Code')}}</label>
                        <input type="text" value="{{$item->Code}}"  class="form-control" disabled>
                            <input type="hidden" name="Code" value="{{$item->Code}}">
                                            </div>

                                            <div class="form-group col-lg-3">
                                                <label class="form-label" for="simpleinput"> {{trans('admin.Arabic_Name')}}</label>
                        <input type="text" name="Name" value="{{$item->Name}}"  class="form-control" required>
                                            </div>

                                <div class="form-group col-lg-3">
                                                <label class="form-label" for="simpleinput"> {{trans('admin.English_Name')}}</label>
                        <input type="text" name="NameEn" value="{{$item->NameEn}}"  class="form-control" required>
                                            </div>


                                              <div class="form-group col-lg-3">
                                <label class="form-label" for="simpleinput"> {{trans('admin.Phone')}}</label>
                     <input type="number" name="Phone" value="{{$item->Phone}}"  class="form-control">
                                            </div>

                                                      <div class="form-group col-lg-3">
                                <label class="form-label" for="simpleinput"> {{trans('admin.Phone2')}}</label>
                     <input type="number" name="Phone2" value="{{$item->Phone2}}"  class="form-control">
                                            </div>


                                                      <div class="form-group col-lg-4">
                                <label class="form-label" for="simpleinput"> {{trans('admin.Commercial_Register')}}</label>
                     <input type="number" name="Commercial_Register" value="{{$item->Commercial_Register}}"  class="form-control">
                                            </div>


                                                                       <div class="form-group col-lg-4">
                                <label class="form-label" for="simpleinput"> {{trans('admin.Tax_Card')}}</label>
                     <input type="number" name="Tax_Card" value="{{$item->Tax_Card}}"  class="form-control">
                                            </div>

                                                                       <div class="form-group col-lg-4">
                                <label class="form-label" for="simpleinput"> {{trans('admin.Price_Level')}}</label>
                  <select class="select2 form-control" name="Price_Level" required>
                                      <option value="">{{trans('admin.Price_Level')}}</option>
     <option value="1" @if($item->Price_Level == 1) selected @endif >{{trans('admin.Level_1')}}</option>
     <option value="2" @if($item->Price_Level == 2) selected @endif>{{trans('admin.Level_2')}}</option>
     <option value="3" @if($item->Price_Level == 3) selected @endif>{{trans('admin.Level_3')}}</option>

                                       </select>
                                            </div>

                                    <div class="form-group col-md-4">
                                       <label class="form-label" for="">  {{trans('admin.Responsible')}}</label>
                                       <select class="select2 form-control w-100" name="Responsible">
                                          <option value="">{{trans('admin.Responsible')}}</option>
                                          @foreach($Employess as $emp)
                                          <option value="{{$emp->id}}" @if($item->Responsible == $emp->id) selected @endif>
                                             {{app()->getLocale() == 'ar' ?$emp->Name :$emp->NameEn}}
                                          </option>
                                          @endforeach
                                       </select>
                                    </div>

                                                           <div class="form-group col-md-4">
                                       <label class="form-label" for="">  {{trans('admin.Group')}}</label>
                                       <select class="select2 form-control w-100" name="Pro_Group" >
                                          <option value="">{{trans('admin.Group')}}</option>
                                          @foreach($Groups as $grp)
                                          <option value="{{$grp->id}}" @if($item->Pro_Group == $grp->id) selected @endif>
                                       {{app()->getLocale() == 'ar' ?$grp->Name :$grp->NameEn}}
                                          </option>
                                          @endforeach
                                       </select>
                                    </div>



                                                                   <div class="form-group col-md-4">
                                       <label class="form-label" for="">  {{trans('admin.Brand')}}</label>
                                       <select class="select2 form-control w-100" name="Brand">
                                          <option value="">{{trans('admin.Brand')}}</option>
                                          @foreach($Brands as $bra)
                                          <option value="{{$bra->id}}" @if($item->Brand == $bra->id) selected @endif>
                                     {{app()->getLocale() == 'ar' ?$bra->Name :$bra->NameEn}}
                                          </option>
                                          @endforeach
                                       </select>
                                    </div>




                                                               <div class="form-group col-lg-4">
        <label class="form-label" for="">  {{trans('admin.Governrate')}}</label>
         <select class="select2 form-control w-100 Governrate" id="Governrate{{$item->id}}" name="Governrate" onchange="G({{$item->id}})" >
                                          <option value="">{{trans('admin.Governrate')}}</option>
                                          @foreach($Governrates as $gov)
                                          <option value="{{$gov->id}}" @if($gov->id == $item->Governrate) selected @endif>
                                        {{app()->getLocale() == 'ar' ?$gov->Arabic_Name :$gov->English_Name}}
                                          </option>
                                          @endforeach
                                       </select>


                                                                            </div>


                                                       <div class="form-group col-lg-4">
    <label class="form-label" for="">  {{trans('admin.City')}}</label>
    <select class="select2 form-control w-100 City" id="City{{$item->id}}" name="City" onchange="C({{$item->id}})" >
           @if(!empty($item->City))
        <option value="{{$item->City}}" selected>

              {{app()->getLocale() == 'ar' ?$item->City()->first()->Arabic_Name :$item->City()->first()->English_Name}}
        </option>
        @endif
                                       </select>

                                                                            </div>


                                                        <div class="form-group col-lg-4">
    <label class="form-label" for="">  {{trans('admin.Place')}}</label>
                             <select class="select2 form-control w-100 Place" id="Place{{$item->id}}" name="Place" >

                                        @if(!empty($item->Place))
        <option value="{{$item->Place}}" selected>

                                          {{app()->getLocale() == 'ar' ?$item->Place()->first()->Arabic_Name :$item->Place()->first()->English_Name}}
                                 </option>
        @endif

                                       </select>

                                                                            </div>

                            <div class="form-group col-md-4">
                                       <label class="form-label" for=""> {{trans('admin.Tax_Registration_Number')}}   </label>
                                       <input type="text" name="Tax_Registration_Number" value="{{$item->Tax_Registration_Number}}" class="form-control">
                                    </div>


                                                     <div class="form-group col-md-4">
                                       <label class="form-label" for=""> {{trans('admin.Tax_activity_code')}}   </label>
                                       <input type="text" name="Tax_activity_code" value="{{$item->Tax_activity_code}}" class="form-control">
                                    </div>
 <div class="form-group col-md-4">
                                       <label class="form-label" for="">    {{trans('admin.work_nature')}}    </label>
                              <select class="select2 form-control" name="work_nature" required>
                    <option value="P" @if($item->work_nature == 'P') selected @endif>{{trans('admin.Person')}}</option>
                  <option value="B" @if($item->work_nature  == 'B') selected @endif>{{trans('admin.Egyptian_trading_company')}}</option>
                  <option value="F" @if($item->work_nature  == 'F') selected @endif>{{trans('admin.foreign_trading_company')}}</option>
                              </select>
                                    </div>
 <div class="form-group col-md-4">
                              <label class="form-label" for="">  {{trans('admin.Nationality')}} </label>
                              <select class="select2 form-control w-100"  name="Nationality" required>
                                 <option value="">{{trans('admin.Nationality')}}</option>
                                 @foreach($Nationality as $nation)
                                 <option value="{{$nation->id}}"  @if($nation->id  == $item->Nationality) selected @endif>
                              {{app()->getLocale() == 'ar' ?$nation->Arabic_Name :$nation->English_Name}}
                                 </option>
                                 @endforeach
                              </select>
                           </div>


                                                                    <div class="form-group col-md-4">
                                       <label class="form-label" for=""> {{trans('admin.Buliding_Num')}}   </label>
                                       <input type="text" name="Buliding_Num" value="{{$item->Buliding_Num}}" class="form-control">
                                    </div>
                                                                    <div class="form-group col-md-4">
                                       <label class="form-label" for=""> {{trans('admin.Street')}}   </label>
                                       <input type="text" name="Street" value="{{$item->Street}}" class="form-control">
                                    </div>

                                                                    <div class="form-group col-md-4">
                                       <label class="form-label" for=""> {{trans('admin.Postal_Code')}}   </label>
                                       <input type="text" name="Postal_Code" value="{{$item->Postal_Code}}" class="form-control">
                                    </div>
                                                                    <div class="form-group col-md-4">
                                       <label class="form-label" for=""> {{trans('admin.tax_magistrate')}}   </label>
                                       <input type="text" name="tax_magistrate" value="{{$item->tax_magistrate}}" class="form-control">
                                    </div>


                                                                    <div class="form-group col-md-4">
                                       <label class="form-label" for=""> {{trans('admin.Floor')}}   </label>
                                       <input type="text" name="Floor" value="{{$item->Floor}}" class="form-control">
                                    </div>


                                                                    <div class="form-group col-md-4">
                                       <label class="form-label" for=""> {{trans('admin.Room')}}   </label>
                                       <input type="text" name="Room" value="{{$item->Room}}" class="form-control">
                                    </div>


                                                                    <div class="form-group col-md-4">
                                       <label class="form-label" for=""> {{trans('admin.Landmark')}}   </label>
                                       <input type="text" name="Landmark" value="{{$item->Landmark}}" class="form-control">
                                    </div>


                                                                    <div class="form-group col-md-4">
                                       <label class="form-label" for=""> {{trans('admin.Add_Info')}}   </label>
                                       <input type="text" name="Add_Info" value="{{$item->Add_Info}}" class="form-control">
                                    </div>

                                                                    <div class="form-group col-md-4">
                                       <label class="form-label" for=""> {{trans('admin.SearchCode')}}   </label>
                                       <input type="text" name="SearchCode" value="{{$item->SearchCode}}" class="form-control">
                                    </div>




                                        </div>
                                         <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">{{trans('admin.Close')}}</button>
                        <button type="submit" class="btn btn-primary"> {{trans('admin.SaveChanges')}}</button>
                                </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Modal Delete -->
                    <div class="modal fade" id="Delete{{$item->id}}" tabindex="-1" role="dialog" aria-hidden="true">
                        <div class="modal-dialog modal-dialog-centered" role="document">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h4 class="modal-title">
                                    {{trans('admin.RUSWDT')}} <strong>
                                 {{app()->getLocale() == 'ar' ?$item->Name :$item->NameEn}}
                                        </strong>
                                    </h4>
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                        <span aria-hidden="true"><i class="fal fa-times"></i></span>
                                    </button>
                                </div>
                                <div class="modal-footer">
                 <button type="button" class="btn btn-secondary" data-dismiss="modal"> {{trans('admin.No')}}</button>
                   <a href="{{url('DeleteVendors/'.$item->id)}}"  class="btn btn-primary"> {{trans('admin.Yes')}}</a>
                                </div>
                            </div>
                        </div>
                    </div>


    <!-- Modal show data -->
    <div class="modal fade" id="show-data{{$item->id}}" tabindex="-1" role="dialog" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered modal-xl" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h4 class="modal-title">
                              {{trans('admin.Data')}}
                            </h4>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true"><i class="fal fa-times"></i></span>
                            </button>
                        </div>
                        <div class="modal-body">
       <div style="overflow:auto;">
        <table id="" class="table table-bordered table-hover table-striped " style="width:120%;">
                                            <thead class="bg-highlight">
                                                <tr>

                                                       <th>{{trans('admin.Phone2')}}</th>
                                                    <th>{{trans('admin.Commercial_Register')}}</th>
                                                    <th>{{trans('admin.Tax_Card')}}</th>
     <th>{{trans('admin.Governrate')}}</th>
                                                    <th>{{trans('admin.City')}}</th>
                                                    <th>{{trans('admin.Place')}}</th>
    <th>{{trans('admin.Tax_Registration_Number')}}</th>
                                                    <th>{{trans('admin.Tax_activity_code')}}</th>
                                                    <th>{{trans('admin.work_nature')}}</th>
                                                    <th>{{trans('admin.Nationality')}}</th>
                                                    <th>{{trans('admin.Buliding_Num')}}</th>
                                                    <th>{{trans('admin.Street')}}</th>
                                                    <th>{{trans('admin.Postal_Code')}}</th>
                                                    <th>{{trans('admin.tax_magistrate')}}</th>
                                                    <th>{{trans('admin.User')}}</th>
                                                    <th>{{trans('admin.Group')}}</th>
                                                    <th>{{trans('admin.Brand')}}</th>
                                                    <th>{{trans('admin.SearchCode')}}</th>

                                                </tr>
                                            </thead>
                                            <tbody>

                                                <tr>

                                                        <td>{{$item->Phone2}}</td>
                                                    <td>{{$item->Commercial_Register}}</td>
                                                    <td>{{$item->Tax_Card}}</td>
      <td>@if(!empty($item->Governrate))



             {{app()->getLocale() == 'ar' ?$item->Governrate()->first()->Arabic_Name :$item->Governrate()->first()->English_Name}}
          @endif</td>
                                                    <td>@if(!empty($item->City))

                                                          {{app()->getLocale() == 'ar' ?$item->City()->first()->Arabic_Name :$item->City()->first()->English_Name}}
                                                        @endif</td>
                                                    <td>@if(!empty($item->Place))

                                                         {{app()->getLocale() == 'ar' ?$item->Place()->first()->Arabic_Name :$item->Place()->first()->English_Name}}
                                                        @endif</td>
    <td>{{$item->Tax_Registration_Number}}</td>
                                                    <td>{{$item->Tax_activity_code}}</td>
                                                    <td>
                                                                        @if($item->work_nature == 'P')

                                                {{trans('admin.Person')}}
                                        @elseif($item->work_nature == 'B')
                                                 {{trans('admin.Egyptian_trading_company')}}
                                        @elseif($item->work_nature == 'F')
                                     {{trans('admin.foreign_trading_company')}}

                                        @endif

                                                    </td>
                                                    <td>@if(!empty($item->Nationality))

                                                          {{app()->getLocale() == 'ar' ?$item->Nationality()->first()->Arabic_Name :$item->Nationality()->first()->English_Name}}
                                                        @endif</td>
                                                    <td>{{$item->Buliding_Num}}</td>
                                                    <td>{{$item->Street}}</td>
                                                    <td>{{$item->Postal_Code}}</td>
                                                    <td>{{$item->tax_magistrate}}</td>
                                                    <td>

                               {{app()->getLocale() == 'ar' ?$item->User()->first()->name :$item->User()->first()->nameEn}}

                                                    </td>
                                                       <td>@if(!empty($item->Pro_Group))


                                                               {{app()->getLocale() == 'ar' ?$item->Pro_Group()->first()->Name :$item->Pro_Group()->first()->NameEn}}
                                                           @endif</td>
                                                       <td>@if(!empty($item->Brand))

                                                               {{app()->getLocale() == 'ar' ?$item->Brand()->first()->Name :$item->Brand()->first()->NameEn}}
                                                           @endif</td>


                                                    <td>

                                                               {{$item->SearchCode }}
                                                           </td>

                                                </tr>


                                            </tbody>


                                        </table>

                                        </div>

                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">{{trans('admin.Close')}}</button>

                        </div>
                    </div>
                </div>
            </div>

     @endforeach

                </main>
@endsection

@push('js')
  <link rel="stylesheet" media="screen, print" href="{{asset('Admin/css/datagrid/datatables/datatables.bundle.css')}}">
    <script src="{{asset('Admin/js/datagrid/datatables/datatables.export.js')}}"></script>
    <script src="{{asset('Admin/js/datagrid/datatables/datatables.bundle.js')}}"></script>
    <link rel="stylesheet" media="screen, print" href="{{asset('Admin/css/formplugins/select2/select2.bundle.css')}}">
    <script src="{{asset('Admin/js/formplugins/select2/select2.bundle.js')}}"></script>
    <script>
        //_fnFeatureHtmlLength();
        $(document).ready(function () {
            // Setup - add a text input to each footer cell
            $('#dt-basic-example thead tr').clone(true).appendTo('#dt-basic-example thead');
            $('#dt-basic-example thead tr:eq(1) th').each(function (i) {
                var title = $(this).text();
                $(this).html('<input type="text" class="form-control form-control-sm" placeholder="Search ' + '" />');

                $('input', this).on('keyup change', function () {
                    if (table.column(i).search() !== this.value) {
                        table
                            .column(i)
                            .search(this.value)
                            .draw();
                    }
                });
            });
            var table = $('#dt-basic-example').DataTable(
                {
                    // responsive: true,
                    orderCellsTop: true,
                    fixedHeader: true,
                    lengthChange: true,

                    dom: "<'row mb-3'<'col-sm-12 col-md-3 d-flex align-items-center justify-content-start'f><'col-sm-12 col-md-9 d-flex align-items-center justify-content-end'B>>" +
                        "<'row'<'col-sm-12'tr>>" +
                        "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",

                    buttons: [
                        {
                            extend: 'pageLength',
                            className: 'btn-outline-default'
                        },
                        {
                            extend: 'colvis',
                            text: 'Column Visibility',
                            titleAttr: 'Col visibility',
                            className: 'btn-outline-default'
                        },
                        {
                            extend: 'pdfHtml5',
                            text: 'PDF',
                            titleAttr: 'Generate PDF',
                            className: 'btn-outline-danger btn-sm mr-1'
                        },
                        {
                            extend: 'excelHtml5',
                            text: 'Excel',
                            titleAttr: 'Generate Excel',
                            className: 'btn-outline-success btn-sm mr-1'
                        },
                        {
                            extend: 'csvHtml5',
                            text: 'CSV',
                            titleAttr: 'Generate CSV',
                            className: 'btn-outline-primary btn-sm mr-1'
                        },
                        {
                            extend: 'copyHtml5',
                            text: 'Copy',
                            titleAttr: 'Copy to clipboard',
                            className: 'btn-outline-primary btn-sm mr-1'
                        },
                        {
                            extend: 'print',
                            text: 'Print',
                            titleAttr: 'Print Table',
                            className: 'btn-outline-primary btn-sm'
                        }
                    ],
                });
            $('.js-thead-colors a').on('click', function () {
                var theadColor = $(this).attr("data-bg");
                console.log(theadColor);
                $('#dt-basic-example thead').removeClassPrefix('bg-').addClass(theadColor);
            });

            $('.js-tbody-colors a').on('click', function () {
                var theadColor = $(this).attr("data-bg");
                console.log(theadColor);
                $('#dt-basic-example').removeClassPrefix('bg-').addClass(theadColor);
            });

        });

    </script>
<!-- Search Selecet -->
 <script>
        $(document).ready(function()
        {
            $(function()
            {
                $('.select2').select2();

                $(".select2-placeholder-multiple").select2(
                {
                    placeholder: "Select State"
                });
                $(".js-hide-search").select2(
                {
                    minimumResultsForSearch: 1 / 0
                });
                $(".js-max-length").select2(
                {
                    maximumSelectionLength: 2,
                    placeholder: "Select maximum 2 items"
                });
                $(".select2-placeholder").select2(
                {
                    placeholder: "Select a state",
                    allowClear: true
                });

                $(".js-select2-icons").select2(
                {
                    minimumResultsForSearch: 1 / 0,
                    templateResult: icon,
                    templateSelection: icon,
                    escapeMarkup: function(elm)
                    {
                        return elm
                    }
                });

                function icon(elm)
                {
                    elm.element;
                    return elm.id ? "<i class='" + $(elm.element).data("icon") + " mr-2'></i>" + elm.text : elm.text
                }


  $('#AccountCode').select2({
    placeholder: "select...",
    ajax: {
        type: "GET",
        dataType: 'json',
        url: 'AllSubAccounts',
        processResults: function (data) {
          return {
            results: $.map(data, function(obj, index) {
              return { id: index, text: obj };
            })
          };

            	console.log(data);

        },
        data: function (params) {
          var query = {
            search: params.term
          };
          if (params.term == "*") query.items = [];
          return { json: JSON.stringify( query ) }
        }
    }
  });


$('#AccountCode').on('select2:select', function (e) {
	console.log("select done", e.params.data);
});


            });
        });



    </script>

    <!-- Filter Governrate and City !-->
<script>
   $(document).ready(function() {

       $('#Governrate').on('change', function(){
           var countryId = $(this).val();
           if(countryId) {
               $.ajax({
                   url: 'GovernrateFilter/'+countryId,
                   type:"GET",
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },

                   success:function(data) {

                       $('#City').empty();

                       $.each(data, function(key, value){

             $('#City').append('<option value="'+ key +'">' + value + '</option>');

                       });

                      var CIITY = $('#City').val();
                         $.ajax({
                   url: 'CityFilter/'+CIITY,
                   type:"GET",
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },

                   success:function(data) {

                       $('#Place').empty();

                       $.each(data, function(key, value){

             $('#Place').append('<option value="'+ key +'">' + value + '</option>');

                       });


                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });



                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });
           } else {

               $('select[name="states"]').empty();
           }

       });

   });

   $(document).ready(function() {


           var countryId = $('#Governrate').val();
           if(countryId) {
               $.ajax({
                   url: 'GovernrateFilter/'+countryId,
                   type:"GET",
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },

                   success:function(data) {

                       $('#City').empty();

                       $.each(data, function(key, value){

             $('#City').append('<option value="'+ key +'">' + value + '</option>');

                       });

                     var CIITY = $('#City').val();
                         $.ajax({
                   url: 'CityFilter/'+CIITY,
                   type:"GET",
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },

                   success:function(data) {

                       $('#Place').empty();

                       $.each(data, function(key, value){

             $('#Place').append('<option value="'+ key +'">' + value + '</option>');

                       });


                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });




                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });
           } else {

               $('select[name="states"]').empty();
           }


   });


 $('#City').on('change', function(){
      var CIITY = $('#City').val();
                         $.ajax({
                   url: 'CityFilter/'+CIITY,
                   type:"GET",
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },

                   success:function(data) {

                       $('#Place').empty();

                       $.each(data, function(key, value){

             $('#Place').append('<option value="'+ key +'">' + value + '</option>');

                       });


                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });


 });



    function G(r){
           var countryId = $('#Governrate'+r).val();
           if(countryId) {
               $.ajax({
                   url: 'GovernrateFilter/'+countryId,
                   type:"GET",
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },

                   success:function(data) {

                       $('#City'+r).empty();

                       $.each(data, function(key, value){

             $('#City'+r).append('<option value="'+ key +'">' + value + '</option>');

                       });

                      var CIITY = $('#City'+r).val();
                         $.ajax({
                   url: 'CityFilter/'+CIITY,
                   type:"GET",
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },

                   success:function(data) {

                       $('#Place'+r).empty();

                       $.each(data, function(key, value){

             $('#Place'+r).append('<option value="'+ key +'">' + value + '</option>');

                       });


                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });



                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });
           } else {

               $('select[name="states"]').empty();
           }

       }






function C(r){
      var CIITY = $('#City'+r).val();
                         $.ajax({
                   url: 'CityFilter/'+CIITY,
                   type:"GET",
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },

                   success:function(data) {

                       $('#Place'+r).empty();

                       $.each(data, function(key, value){

             $('#Place'+r).append('<option value="'+ key +'">' + value + '</option>');

                       });


                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });


 }


</script>
@endpush

