<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateEmployessesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('employesses', function (Blueprint $table) {
            $table->id();

            // Basic employee information
            $table->string('Code')->nullable();
            $table->string('Name')->nullable();
            $table->string('NameEn')->nullable();
            $table->string('Emp_Type')->nullable();
            $table->decimal('Salary', 15, 2)->nullable();
            $table->string('Image')->nullable();
            $table->text('Note')->nullable();

            // Work schedule
            $table->time('Attendence')->nullable();
            $table->time('Departure')->nullable();
            $table->integer('Hours_Numbers')->nullable();
            $table->integer('Days_Numbers')->nullable();
            $table->decimal('Day_Price', 15, 2)->nullable();

            // Commission and percentages
            $table->decimal('Precentage_of_Sales', 5, 2)->nullable();
            $table->decimal('Precentage_of_Profits', 5, 2)->nullable();
            $table->decimal('Precentage_of_Execution', 5, 2)->nullable();

            // Personal information
            $table->string('Bank_Account')->nullable();
            $table->text('Qualifications')->nullable();
            $table->text('Address')->nullable();
            $table->string('Social_Status')->nullable();
            $table->string('ID_Number')->nullable();
            $table->date('Contract_Start')->nullable();
            $table->date('Contract_End')->nullable();
            $table->string('Phone')->nullable();
            $table->string('Phone2')->nullable();
            $table->string('Email')->nullable();
            $table->string('Password')->nullable();

            // Job and department references
            $table->string('Job')->nullable();
            $table->string('Department')->nullable();
            $table->string('Account')->nullable();
            $table->string('Covenant')->nullable();
            $table->string('Commission')->nullable();
            $table->string('Account_Emp')->nullable();
            $table->string('User')->nullable();

            // Sales and billing
            $table->string('Price_Level')->nullable();
            $table->string('Bill_Num')->default('0');
            $table->string('NumbersOfBill')->nullable();
            $table->string('EmpSort')->default('1');

            // Documents and files
            $table->text('CV')->nullable();
            $table->text('ID_Image')->nullable();
            $table->text('Criminal_status')->nullable();
            $table->text('Contract')->nullable();
            $table->text('health_certificate')->nullable();
            $table->text('Search_Card')->nullable();
            $table->text('Recruitment_certificate')->nullable();
            $table->text('employee_profile')->nullable();
            $table->string('duration_criminal_investigation')->nullable();

            // Additional personal info
            $table->date('Birthdate')->nullable();
            $table->string('Attitude_recruiting')->nullable();
            $table->string('Job_Number')->nullable();
            $table->date('date_resignation')->nullable();
            $table->string('Living')->nullable();
            $table->string('Branch')->nullable();
            $table->string('Level')->nullable();
            $table->string('Religion')->nullable();
            $table->decimal('Insurance_salary', 15, 2)->nullable();
            $table->string('Insurance_companies')->nullable();
            $table->text('Previous_experience')->nullable();
            $table->string('Nationality')->nullable();

            // Targets
            $table->decimal('MonthlyTarget', 15, 2)->nullable();
            $table->decimal('QuarterTarget', 15, 2)->nullable();
            $table->decimal('SemiTarget', 15, 2)->nullable();
            $table->decimal('YearlyTarget', 15, 2)->nullable();

            // Expiry dates
            $table->date('IDExpireDate')->nullable();
            $table->date('LicensExpireDate')->nullable();
            $table->date('PassportExpireDate')->nullable();

            // Additional fields
            $table->string('Merit')->nullable();
            $table->string('Pro_Group')->nullable();
            $table->string('SearchCode')->nullable();
            $table->string('Active')->default('1');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('employesses');
    }
}
