<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCentralSalesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('sales', function (Blueprint $table) {
            $table->id();
            
            // Basic sales information
            $table->string('Code')->nullable();
            $table->string('Store_Code')->nullable();
            $table->date('Date')->nullable();
            $table->string('Draw')->nullable();
            $table->string('Payment_Method')->nullable();
            $table->string('Status')->default('0');
            $table->string('Refernce_Number')->nullable();
            $table->text('Note')->nullable();
            
            // Quantities and totals
            $table->integer('Product_Numbers')->default(0);
            $table->decimal('Total_Qty', 15, 2)->default(0);
            $table->decimal('Total_Discount', 15, 2)->default(0);
            $table->decimal('Total_BF_Taxes', 15, 2)->default(0);
            $table->decimal('Total_Taxes', 15, 2)->default(0);
            $table->decimal('Total_Price', 15, 2)->default(0);
            $table->decimal('Pay', 15, 2)->default(0);
            
            // References to other entities
            $table->string('Safe')->nullable();
            $table->string('Client')->nullable();
            $table->string('Executor')->nullable();
            $table->string('Delegate')->nullable();
            $table->string('Store')->nullable();
            $table->string('Coin')->nullable();
            $table->string('Cost_Center')->nullable();
            $table->string('User')->nullable();
            $table->string('Ship')->nullable();
            $table->string('Branch')->nullable();
            
            // Payment and installment fields
            $table->string('presenter')->nullable();
            $table->decimal('annual_interest', 8, 2)->nullable();
            $table->decimal('monthly_installment', 15, 2)->nullable();
            $table->integer('Years_Number')->nullable();
            $table->decimal('total', 15, 2)->nullable();
            $table->string('Quote')->nullable();
            $table->string('SalesOrder')->nullable();
            $table->integer('installment_Num')->nullable();
            $table->date('Date_First_installment')->nullable();
            
            // Check information
            $table->string('Check_Type')->nullable();
            $table->date('Due_Date')->nullable();
            $table->string('Check_Number')->nullable();
            
            // Payment methods
            $table->decimal('Cash', 15, 2)->default(0);
            $table->decimal('Visa', 15, 2)->default(0);
            
            // Status and control fields
            $table->string('Sale_Type')->nullable();
            $table->string('Hold')->default('0');
            $table->string('Shift_Code')->nullable();
            $table->string('emp')->nullable();
            $table->decimal('Later_Due', 15, 2)->default(0);
            $table->decimal('Later_Collection', 15, 2)->default(0);
            $table->text('Client_Address')->nullable();
            $table->string('Delivery')->nullable();
            $table->string('Delivery_Status')->default('0');
            $table->string('Sale_User')->nullable();
            $table->string('Sent')->default('0');
            
            // Tax and billing
            $table->string('TaxBill')->nullable();
            $table->string('TaxCode')->nullable();
            $table->decimal('ProfitPrecent', 8, 2)->nullable();
            $table->decimal('TaxOnTotal', 15, 2)->default(0);
            $table->string('TaxOnTotalType')->nullable();
            $table->decimal('ProfitTax', 15, 2)->default(0);
            
            // Installment company
            $table->string('InstallCompany')->nullable();
            $table->string('ContractNumber')->nullable();
            $table->decimal('PayFees', 15, 2)->default(0);
            $table->decimal('ServiceFee', 15, 2)->default(0);
            $table->decimal('CompanyPrecent', 8, 2)->nullable();
            
            // Additional fields
            $table->string('Time')->nullable();
            $table->string('CustomerGroup')->nullable();
            $table->decimal('Total_Cost', 15, 2)->default(0);
            $table->string('ShipStatus')->nullable();
            
            // Electronic billing
            $table->string('uuid')->nullable();
            $table->string('longId')->nullable();
            $table->string('hashKey')->nullable();
            $table->string('submissionId')->nullable();
            $table->string('statusBill')->nullable();
            $table->decimal('DiscountTax', 15, 2)->default(0);
            $table->date('RecivedDate')->nullable();
            $table->text('File')->nullable();
            
            // Restaurant specific fields
            $table->string('TakeawayStatus')->default('0');
            $table->string('TakeawayTime')->nullable();
            $table->string('Witer')->nullable();
            $table->string('KitchenEnd')->default('0');
            $table->string('KitchenEndTime')->nullable();
            $table->string('RecivedOrder')->default('0');
            $table->string('RecivedOrderTime')->nullable();
            $table->string('DeliveryTime')->nullable();
            $table->string('ResturantOrderType')->nullable();
            $table->string('Table')->nullable();
            $table->decimal('Total_Wight_Bill', 15, 2)->default(0);
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('sales');
    }
}
