<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Stancl\Tenancy\Database\Concerns\CentralConnection;

class Governrate extends Model
{
    use HasFactory, CentralConnection;
          protected $table = 'governrates';
      protected $fillable = [
        'Arabic_Name',
        'English_Name',
        'Country',
        'SearchCode',

    ];

        public function City()
    {
        return $this->hasOne(City::class);
    }

                               public function Customers()
    {
        return $this->hasOne(Customers::class);
    }

              public function Country()
    {
        return $this->belongsTo(Countris::class,'Country');
    }

}
