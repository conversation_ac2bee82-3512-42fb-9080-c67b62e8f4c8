<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Stancl\Tenancy\Database\Concerns\CentralConnection;

class AcccountingManual extends Model
{
    use HasFactory, CentralConnection;


      protected $table = 'acccounting_manuals';
      protected $fillable = [
        'Code',
        'Name',
        'NameEn',
        'Type',
        'Parent',
        'Note',
        'User',
        'Account_Code',
        'Pro_Group',
        'SearchCode',


    ];


       public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }

      public function Pro_Group()
    {
        return $this->belongsTo(ItemsGroups::class,'Pro_Group');
    }


            public function JournalizingDetails()
    {
        return $this->hasOne(JournalizingDetails::class);
    }

       public function GeneralDaily()
    {
        return $this->hasMany(GeneralDaily::class);
    }

            public function PaymentVoucher()
    {
        return $this->hasOne(PaymentVoucher::class);
    }

              public function PaymentVoucherDetails()
    {
        return $this->hasOne(PaymentVoucherDetails::class);
    }

                   public function ReciptVoucher()
    {
        return $this->hasOne(ReciptVoucher::class);
    }


                      public function ReciptVoucherDetails()
    {
        return $this->hasOne(ReciptVoucherDetails::class);
    }


        public function OpeningEntriesDetails()
    {
        return $this->hasOne(OpeningEntriesDetails::class);
    }

         public function IncomChecks()
    {
        return $this->hasOne(IncomChecks::class);
    }

            public function ExportChecks()
    {
        return $this->hasOne(ExportChecks::class);
    }



                public function SafesBanks()
    {
        return $this->hasOne(SafesBanks::class);
    }


                         public function Stores()
    {
        return $this->hasOne(Stores::class);
    }

                     public function Inventory()
    {
        return $this->hasOne(Inventory::class);
    }
                         public function Settlement()
    {
        return $this->hasOne(Settlement::class);
    }


                          public function SafeTransfers()
    {
        return $this->hasOne(SafeTransfers::class);
    }



                          public function Employess()
    {
        return $this->hasOne(Employess::class);
    }


                          public function Vendors()
    {
        return $this->hasOne(Vendors::class);
    }

          public function PurchasesOrder()
    {
        return $this->hasOne(PurchasesOrder::class);
    }

                     public function Purchases()
    {
        return $this->hasOne(Purchases::class);
    }

                               public function Customers()
    {
        return $this->hasOne(Customers::class);
    }

        public function Quote()
    {
        return $this->hasOne(Quote::class);
    }

         public function SalesOrder()
    {
        return $this->hasOne(SalesOrder::class);
    }

         public function Sales()
    {
        return $this->hasOne(Sales::class);
    }

                  public function Installment()
    {
        return $this->hasOne(Installment::class);
    }


                  public function InstallmentDates()
    {
        return $this->hasOne(InstallmentDates::class);
    }

              public function Borrowa()
    {
        return $this->hasOne(Borrowa::class);
    }

                public function Loan()
    {
        return $this->hasOne(Loan::class);
    }

                  public function PaySalary()
    {
        return $this->hasOne(PaySalary::class);
    }

                     public function ShippingCompany()
    {
        return $this->hasOne(ShippingCompany::class);
    }

                       public function Assets()
    {
        return $this->hasOne(Assets::class);
    }


            public function ReciptMaintaince()
    {
        return $this->hasOne(ReciptMaintaince::class);
    }

}
