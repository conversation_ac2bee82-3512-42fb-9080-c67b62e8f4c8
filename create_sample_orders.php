<?php

require 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use Illuminate\Support\Facades\DB;

try {
    echo "Creating sample shop orders...\n";

    // Create sample shop orders one by one
    DB::table('sales_orders')->insert([
        'Order_Number' => 'ORD-2025-001',
        'Order_Type' => 1,
        'Status' => 'Pending',
        'Order_Date' => '2025-01-15',
        'Customer_Name' => 'Ahmed Ali',
        'Customer_Email' => '<EMAIL>',
        'Customer_Phone' => '+1234567890',
        'Shipping_Address' => '123 Main Street',
        'Shipping_City' => 'Cairo',
        'Shipping_Country' => 'Egypt',
        'Subtotal' => 150.00,
        'Tax_Amount' => 15.00,
        'Total_Amount' => 165.00,
        'Payment_Method' => 'Credit Card',
        'Payment_Status' => 'Pending',
        'Currency' => 'USD',
        'created_at' => now(),
        'updated_at' => now()
    ]);

    DB::table('sales_orders')->insert([
        'Order_Number' => 'ORD-2025-002',
        'Order_Type' => 1,
        'Status' => 'Processing',
        'Order_Date' => '2025-01-14',
        'Customer_Name' => 'Sarah Johnson',
        'Customer_Email' => '<EMAIL>',
        'Customer_Phone' => '+1234567891',
        'Shipping_Address' => '456 Oak Avenue',
        'Shipping_City' => 'Alexandria',
        'Shipping_Country' => 'Egypt',
        'Subtotal' => 89.99,
        'Tax_Amount' => 9.00,
        'Total_Amount' => 98.99,
        'Payment_Method' => 'PayPal',
        'Payment_Status' => 'Paid',
        'Currency' => 'USD',
        'created_at' => now(),
        'updated_at' => now()
    ]);

    DB::table('sales_orders')->insert([
        'Order_Number' => 'ORD-2025-003',
        'Order_Type' => 1,
        'Status' => 'Shipped',
        'Order_Date' => '2025-01-13',
        'Customer_Name' => 'Mohamed Hassan',
        'Customer_Email' => '<EMAIL>',
        'Customer_Phone' => '+**********',
        'Shipping_Address' => '789 Pine Street',
        'Shipping_City' => 'Giza',
        'Shipping_Country' => 'Egypt',
        'Subtotal' => 299.50,
        'Tax_Amount' => 30.00,
        'Total_Amount' => 329.50,
        'Payment_Method' => 'Bank Transfer',
        'Payment_Status' => 'Paid',
        'Tracking_Number' => 'TRK123456789',
        'Currency' => 'USD',
        'created_at' => now(),
        'updated_at' => now()
    ]);

    echo "Successfully created 3 sample shop orders!\n";

    // Verify the data
    $count = DB::table('sales_orders')->where('Order_Type', 1)->count();
    echo "Total shop orders in database: $count\n";

} catch (Exception $e) {
    echo 'Error: ' . $e->getMessage() . "\n";
}
