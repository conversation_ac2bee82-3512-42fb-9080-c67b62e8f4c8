<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Stancl\Tenancy\Database\Concerns\CentralConnection;

class Interviews extends Model
{
    use HasFactory, CentralConnection;
          protected $table = 'interviews';
    protected $fillable = [
        'Code',
        'Date',
        'Time',
        'Status',
        'StatusNote',
        'Rate',
        'Note',
        'Emp',
        'Client',
        'Type',
        'User',
    ];

           public function Emp()
    {
        return $this->belongsTo(Employess::class,'Emp');
    }

              public function Client()
    {
        return $this->belongsTo(Customers::class,'Client');
    }

              public function Type()
    {
        return $this->belongsTo(InterviewsTypes::class,'Type');
    }

              public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }
}
