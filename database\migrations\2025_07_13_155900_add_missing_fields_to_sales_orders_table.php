<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddMissingFieldsToSalesOrdersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('sales_orders', function (Blueprint $table) {
            // Basic order fields
            $table->string('Code')->nullable()->after('id');
            $table->string('Time')->nullable()->after('Order_Time');
            $table->string('Date')->nullable()->after('Order_Date');
            $table->string('Draw')->nullable()->after('Date');
            $table->integer('Delivery_Status')->default(0)->after('Status');
            $table->string('Refernce_Number')->nullable()->after('Payment_Reference');
            $table->text('Note')->nullable()->after('Notes');
            
            // Relationships
            $table->unsignedBigInteger('Executor')->nullable()->after('Created_By');
            $table->unsignedBigInteger('Safe')->nullable()->after('Executor');
            $table->unsignedBigInteger('Client')->nullable()->after('Customer_ID');
            $table->unsignedBigInteger('Delegate')->nullable()->after('Client');
            $table->unsignedBigInteger('Store')->nullable()->after('Delegate');
            $table->unsignedBigInteger('Coin')->nullable()->after('Currency');
            $table->unsignedBigInteger('Cost_Center')->nullable()->after('Coin');
            $table->unsignedBigInteger('User')->nullable()->after('Updated_By');
            
            // Order details
            $table->string('Later_Due')->nullable()->after('Payment_Date');
            $table->string('Sale_Date')->nullable()->after('Later_Due');
            $table->integer('ToSales')->default(0)->after('Sale_Date');
            $table->integer('Hold_Qty')->default(1)->after('ToSales');
            $table->integer('Cancel_Order')->default(0)->after('Hold_Qty');
            $table->integer('Delegate_Recived')->default(0)->after('Cancel_Order');
            $table->timestamp('Delegate_Recived_Time')->nullable()->after('Delegate_Recived');
            $table->timestamp('Cancel_Order_Time')->nullable()->after('Delegate_Recived_Time');
            
            // Product totals
            $table->integer('Product_Numbers')->default(0)->after('Total_Amount');
            $table->decimal('Total_Qty', 10, 2)->default(0)->after('Product_Numbers');
            $table->decimal('Total_Discount', 10, 2)->default(0)->after('Discount_Amount');
            $table->decimal('Total_BF_Taxes', 10, 2)->default(0)->after('Total_Discount');
            $table->decimal('Total_Taxes', 10, 2)->default(0)->after('Tax_Amount');
            $table->string('CuponCode')->nullable()->after('Total_Taxes');
            $table->decimal('Shipping', 10, 2)->default(0)->after('Shipping_Cost');
            $table->decimal('Total_Price', 10, 2)->default(0)->after('Total_Amount');
            $table->decimal('Pay', 10, 2)->default(0)->after('Total_Price');
            
            // Customer details
            $table->string('Name')->nullable()->after('Customer_Name');
            $table->string('Email')->nullable()->after('Customer_Email');
            $table->string('Phone')->nullable()->after('Customer_Phone');
            $table->string('OtherPhone')->nullable()->after('Phone');
            $table->string('Address_Name')->nullable()->after('Shipping_Address');
            $table->string('Special_MarkAdd')->nullable()->after('Address_Name');
            $table->string('StreetAdd')->nullable()->after('Special_MarkAdd');
            $table->string('BulidingAdd')->nullable()->after('StreetAdd');
            $table->string('FloorAdd')->nullable()->after('BulidingAdd');
            $table->string('FlatAdd')->nullable()->after('FloorAdd');
            $table->unsignedBigInteger('Governrate')->nullable()->after('Shipping_Country');
            $table->unsignedBigInteger('City')->nullable()->after('Governrate');
            $table->unsignedBigInteger('Place')->nullable()->after('City');
            $table->string('LocationAdd')->nullable()->after('Place');
            $table->text('Address_DetailsAdd')->nullable()->after('LocationAdd');
            
            // Installment fields
            $table->string('presenter')->nullable()->after('Payment_Method');
            $table->decimal('annual_interest', 5, 2)->nullable()->after('presenter');
            $table->decimal('monthly_installment', 10, 2)->nullable()->after('annual_interest');
            $table->integer('Years_Number')->nullable()->after('monthly_installment');
            $table->decimal('total', 10, 2)->nullable()->after('Years_Number');
            $table->integer('installment_Num')->nullable()->after('total');
            $table->date('Date_First_installment')->nullable()->after('installment_Num');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('sales_orders', function (Blueprint $table) {
            $table->dropColumn([
                'Code', 'Time', 'Date', 'Draw', 'Delivery_Status', 'Refernce_Number', 'Note',
                'Executor', 'Safe', 'Client', 'Delegate', 'Store', 'Coin', 'Cost_Center', 'User',
                'Later_Due', 'Sale_Date', 'ToSales', 'Hold_Qty', 'Cancel_Order', 'Delegate_Recived',
                'Delegate_Recived_Time', 'Cancel_Order_Time', 'Product_Numbers', 'Total_Qty',
                'Total_Discount', 'Total_BF_Taxes', 'Total_Taxes', 'CuponCode', 'Shipping',
                'Total_Price', 'Pay', 'Name', 'Email', 'Phone', 'OtherPhone', 'Address_Name',
                'Special_MarkAdd', 'StreetAdd', 'BulidingAdd', 'FloorAdd', 'FlatAdd', 'Governrate',
                'City', 'Place', 'LocationAdd', 'Address_DetailsAdd', 'presenter', 'annual_interest',
                'monthly_installment', 'Years_Number', 'total', 'installment_Num', 'Date_First_installment'
            ]);
        });
    }
}
