<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAdminsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('admins', function (Blueprint $table) {
            $table->id();
            $table->string('email')->unique();
            $table->string('name')->nullable();
            $table->string('nameEn')->nullable();
            $table->string('password');
            $table->string('image')->nullable();
            $table->string('phone')->nullable();
            $table->string('hidden')->default('0');
            $table->string('emp')->default('0');
            $table->string('ship')->default('0');
            $table->string('vend')->default('0');
            $table->string('cli')->default('0');
            $table->string('safe')->nullable();
            $table->string('store')->nullable();
            $table->string('account')->default('0');
            $table->string('type')->nullable();
            $table->string('status')->default('0');
            $table->string('roles_name')->nullable();
            $table->string('lat')->nullable();
            $table->string('long')->nullable();
            $table->string('code')->nullable();
            $table->string('token')->nullable();
            $table->string('price_sale')->nullable();
            $table->string('discount')->nullable();
            $table->string('price_1')->nullable();
            $table->string('price_2')->nullable();
            $table->string('price_3')->nullable();
            $table->string('pos_pay')->nullable();
            $table->string('executor')->nullable();
            $table->string('cost_price')->nullable();
            $table->string('price_level')->nullable();
            $table->string('guest')->nullable();
            $table->string('pos_stores')->nullable();
            $table->string('pos_hold')->nullable();
            $table->string('cost_price_purch')->nullable();
            $table->string('cost_price_sales')->nullable();
            $table->string('manu_order_precent')->nullable();
            $table->string('package')->nullable();
            $table->string('pos_product')->default('0');
            $table->string('Cash')->default('0');
            $table->string('Delivery')->default('0');
            $table->string('Cash_Collection')->default('0');
            $table->string('Cash_Visa')->default('0');
            $table->string('Installment')->default('0');
            $table->string('Check')->default('0');
            $table->string('Later')->default('0');
            $table->string('InstallmentCompanies')->default('0');
            $table->string('Date')->nullable();
            $table->timestamp('email_verified_at')->nullable();
            $table->rememberToken();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('admins');
    }
}
