<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSafeTransfersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('safe_transfers', function (Blueprint $table) {
            $table->id();
            
            // Basic transfer information
            $table->string('Code')->nullable();
            $table->date('Date')->nullable();
            $table->string('Draw')->nullable();
            $table->decimal('Amount', 15, 2)->default(0);
            $table->text('Note')->nullable();
            
            // Safe/Bank references
            $table->string('From_Safe')->nullable(); // References acccounting_manuals
            $table->string('To_Safe')->nullable();   // References acccounting_manuals
            
            // Currency and cost center
            $table->string('Coin')->nullable();
            $table->string('Cost_Center')->nullable();
            
            // User and status
            $table->string('User')->nullable();
            $table->string('Status')->default('0'); // 0=pending, 1=completed
            
            // Additional fields
            $table->text('File')->nullable();
            $table->decimal('OldAmount', 15, 2)->default(0);
            $table->string('Edit')->nullable();
            $table->string('Delegate')->nullable();
            $table->string('Time')->nullable();
            $table->string('Branch')->nullable();
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('safe_transfers');
    }
}
