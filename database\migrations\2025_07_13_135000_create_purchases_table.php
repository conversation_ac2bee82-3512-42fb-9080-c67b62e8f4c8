<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePurchasesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('purchases', function (Blueprint $table) {
            $table->id();
            
            // Basic purchase information
            $table->string('Code')->nullable();
            $table->date('Date')->nullable();
            $table->string('Draw')->nullable();
            $table->string('Payment_Method')->nullable();
            $table->string('Status')->default('0');
            $table->string('ShipStatus')->nullable();
            $table->date('Vendor_Bill_Date')->nullable();
            $table->string('Refernce_Number')->nullable();
            $table->text('Note')->nullable();
            
            // Quantities and totals
            $table->integer('Product_Numbers')->default(0);
            $table->decimal('Total_Qty', 15, 2)->default(0);
            $table->decimal('Total_Discount', 15, 2)->default(0);
            $table->decimal('Total_BF_Taxes', 15, 2)->default(0);
            $table->decimal('Total_Taxes', 15, 2)->default(0);
            $table->decimal('Total_Price', 15, 2)->default(0);
            $table->decimal('Pay', 15, 2)->default(0);
            
            // Purchase order reference
            $table->string('P_Order_Num')->nullable();
            
            // References to other entities
            $table->string('Safe')->nullable();
            $table->string('Vendor')->nullable();
            $table->string('Delegate')->nullable();
            $table->string('Store')->nullable();
            $table->string('Coin')->nullable();
            $table->string('Cost_Center')->nullable();
            $table->string('User')->nullable();
            $table->string('Ship')->nullable();
            $table->string('Branch')->nullable();
            $table->string('CustomerGroup')->nullable();
            
            // Check information
            $table->string('Check_Type')->nullable();
            $table->date('Due_Date')->nullable();
            $table->string('Check_Number')->nullable();
            
            // Payment terms
            $table->decimal('Later_Due', 15, 2)->default(0);
            
            // Status and control fields
            $table->string('Sent')->default('0');
            
            // Tax and billing
            $table->string('TaxBill')->nullable();
            $table->string('TaxCode')->nullable();
            
            // Additional fields
            $table->string('Time')->nullable();
            $table->text('File')->nullable();
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('purchases');
    }
}
