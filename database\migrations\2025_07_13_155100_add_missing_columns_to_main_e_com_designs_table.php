<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddMissingColumnsToMainEComDesignsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('main_e_com_designs', function (Blueprint $table) {
            // Add missing columns that the model expects
            $table->string('Font_Type')->default('Arial')->nullable();
            $table->string('Pagination_BG_Color')->default('#f8f9fa')->nullable();
            $table->string('Pagination_Txt_Color')->default('#000000')->nullable();
            $table->string('Pagination_Active_BG_Color')->default('#007bff')->nullable();
            $table->string('Pagination_Active_Txt_Color')->default('#ffffff')->nullable();
            $table->string('Body_BG_Type')->default('1')->nullable();
            $table->string('Body_BG_Image')->nullable();
            $table->string('Body_BG_Color')->default('#ffffff')->nullable();
            $table->string('Sub_Page_BG_Color')->default('#f8f9fa')->nullable();
            $table->string('Breadcumb_BG_Color')->default('#e9ecef')->nullable();
            $table->string('Breadcumb_Txt_Color')->default('#6c757d')->nullable();
            $table->string('Modal_BG_Color')->default('#ffffff')->nullable();
            $table->string('Modal_Txt_Color')->default('#000000')->nullable();
            $table->string('Modal_Button_BG_Color')->default('#007bff')->nullable();
            $table->string('Modal_Button_Txt_Color')->default('#ffffff')->nullable();
            $table->string('Table_Header_BG_Color')->default('#f8f9fa')->nullable();
            $table->string('Table_Header_Txt_Color')->default('#000000')->nullable();
            $table->string('Table_Body_BG_Color')->default('#ffffff')->nullable();
            $table->string('Table_Body_Txt_Color')->default('#000000')->nullable();
            $table->string('Table_Button_BG_Color')->default('#007bff')->nullable();
            $table->string('Table_Button_Txt_Color')->default('#ffffff')->nullable();
            $table->string('CopyRights_Txt_Color')->default('#6c757d')->nullable();
            $table->string('CopyRights_Klar_Txt_Color')->default('#007bff')->nullable();
            $table->string('CopyRights_Klar_Hover_Txt_Color')->default('#0056b3')->nullable();
            $table->string('Preloader_BG_Color')->default('#ffffff')->nullable();
            $table->string('Preloader_Small_Circle_Color')->default('#007bff')->nullable();
            $table->string('Preloader_Large_Circle_Color')->default('#0056b3')->nullable();
            $table->string('Footer_Title_Color')->default('#000000')->nullable();
            $table->string('Footer_Txt_Color')->default('#6c757d')->nullable();
            $table->string('Footer_Txt_Hover_Color')->default('#007bff')->nullable();
            $table->string('Footer_Social_Color')->default('#ffffff')->nullable();
            $table->string('Footer_Social_BG_Color')->default('#007bff')->nullable();
            $table->string('Footer_Social_Hover_BG_Color')->default('#0056b3')->nullable();
            $table->string('Footer_Social_Hover_Txt_Color')->default('#ffffff')->nullable();
            $table->string('Header_Top_BG_Color')->default('#f8f9fa')->nullable();
            $table->string('Header_Top_Txt_Color')->default('#000000')->nullable();
            $table->string('Header_Top_Txt_Hover_Color')->default('#007bff')->nullable();
            $table->string('Header_Middle_BG_Color')->default('#ffffff')->nullable();
            $table->string('Header_Middle_Icon_Color')->default('#000000')->nullable();
            $table->string('Header_Middle_Icon_Hover_Color')->default('#007bff')->nullable();
            $table->string('Header_SearchBar_BG_Color')->default('#f8f9fa')->nullable();
            $table->string('Header_SearchBar_Txt_Color')->default('#000000')->nullable();
            $table->string('Header_SearchBar_Icon_BG_Color')->default('#007bff')->nullable();
            $table->string('Header_SearchBar_Icon_Txt_Color')->default('#ffffff')->nullable();
            $table->string('Header_SearchBar_Icon_Hover_BG_Color')->default('#0056b3')->nullable();
            $table->string('Header_SearchBar_Icon_Hover_Txt_Color')->default('#ffffff')->nullable();
            $table->string('Navbar_BG_Color')->default('#ffffff')->nullable();
            $table->string('Navbar_Txt_Color')->default('#000000')->nullable();
            $table->string('Navbar_Hover_BG_Color')->default('#f8f9fa')->nullable();
            $table->string('Navbar_Hover_Txt_Color')->default('#007bff')->nullable();
            $table->string('Navbar_Category_BG_Color')->default('#007bff')->nullable();
            $table->string('Navbar_Category_Txt_Color')->default('#ffffff')->nullable();
            $table->string('Navbar_Category_Box_BG_Color')->default('#ffffff')->nullable();
            $table->string('Navbar_Category_Box_Txt_Color')->default('#000000')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('main_e_com_designs', function (Blueprint $table) {
            $table->dropColumn([
                'Font_Type', 'Pagination_BG_Color', 'Pagination_Txt_Color', 'Pagination_Active_BG_Color',
                'Pagination_Active_Txt_Color', 'Body_BG_Type', 'Body_BG_Image', 'Body_BG_Color',
                'Sub_Page_BG_Color', 'Breadcumb_BG_Color', 'Breadcumb_Txt_Color', 'Modal_BG_Color',
                'Modal_Txt_Color', 'Modal_Button_BG_Color', 'Modal_Button_Txt_Color', 'Table_Header_BG_Color',
                'Table_Header_Txt_Color', 'Table_Body_BG_Color', 'Table_Body_Txt_Color', 'Table_Button_BG_Color',
                'Table_Button_Txt_Color', 'CopyRights_Txt_Color', 'CopyRights_Klar_Txt_Color',
                'CopyRights_Klar_Hover_Txt_Color', 'Preloader_BG_Color', 'Preloader_Small_Circle_Color',
                'Preloader_Large_Circle_Color', 'Footer_Title_Color', 'Footer_Txt_Color', 'Footer_Txt_Hover_Color',
                'Footer_Social_Color', 'Footer_Social_BG_Color', 'Footer_Social_Hover_BG_Color',
                'Footer_Social_Hover_Txt_Color', 'Header_Top_BG_Color', 'Header_Top_Txt_Color',
                'Header_Top_Txt_Hover_Color', 'Header_Middle_BG_Color', 'Header_Middle_Icon_Color',
                'Header_Middle_Icon_Hover_Color', 'Header_SearchBar_BG_Color', 'Header_SearchBar_Txt_Color',
                'Header_SearchBar_Icon_BG_Color', 'Header_SearchBar_Icon_Txt_Color',
                'Header_SearchBar_Icon_Hover_BG_Color', 'Header_SearchBar_Icon_Hover_Txt_Color',
                'Navbar_BG_Color', 'Navbar_Txt_Color', 'Navbar_Hover_BG_Color', 'Navbar_Hover_Txt_Color',
                'Navbar_Category_BG_Color', 'Navbar_Category_Txt_Color', 'Navbar_Category_Box_BG_Color',
                'Navbar_Category_Box_Txt_Color'
            ]);
        });
    }
}
