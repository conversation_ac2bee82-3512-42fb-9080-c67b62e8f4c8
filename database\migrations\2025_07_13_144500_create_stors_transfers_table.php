<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateStorsTransfersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('stors_transfers', function (Blueprint $table) {
            $table->id();
            
            // Basic transfer information
            $table->string('Code')->nullable();
            $table->date('Date')->nullable();
            $table->string('Draw')->nullable();
            $table->decimal('Total', 15, 2)->default(0);
            $table->decimal('TotalQty', 15, 2)->default(0);
            $table->text('Note')->nullable();
            
            // Store references
            $table->string('From_Store')->nullable(); // References stores table
            $table->string('To_Store')->nullable();   // References stores table
            
            // Currency and cost center
            $table->string('Coin')->nullable();
            $table->string('Cost_Center')->nullable();
            
            // User and status
            $table->string('User')->nullable();
            $table->string('Status')->default('0'); // 0=pending, 1=completed
            
            // Shipping information
            $table->string('Ship')->nullable();
            $table->decimal('CostShip', 15, 2)->default(0);
            $table->string('RecivedShip')->nullable();
            
            // Additional fields
            $table->decimal('OldQty', 15, 2)->default(0);
            $table->string('Edit')->nullable();
            $table->string('Delegate')->nullable();
            $table->string('Time')->nullable();
            $table->string('Branch')->nullable();
            $table->decimal('Total_Cost', 15, 2)->default(0);
            $table->string('TypeTransfer')->nullable();
            $table->string('Cost_Store')->nullable();
            $table->text('File')->nullable();
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('stors_transfers');
    }
}
