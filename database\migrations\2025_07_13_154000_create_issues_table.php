<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateIssuesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('issues', function (Blueprint $table) {
            $table->id();
            
            // Issue information
            $table->string('Code')->nullable();
            $table->date('Date')->nullable();
            $table->string('Time')->nullable();
            $table->string('Client')->nullable(); // Client name who reported the issue
            $table->string('Link')->nullable();   // System link where issue occurred
            $table->string('Status')->default('0'); // 0=open, 1=resolved
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('issues');
    }
}
