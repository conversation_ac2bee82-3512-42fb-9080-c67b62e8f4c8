<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class BarcodeShowsTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('barcode_shows')->delete();
        
        \DB::table('barcode_shows')->insert(array (
            0 => 
            array (
                'id' => 1,
                'Company_Name' => '1',
                'Product_Name' => NULL,
                'Product_Price' => NULL,
                'Unit' => NULL,
                'Coin' => NULL,
                'Group' => NULL,
                'created_at' => '2022-03-31 18:05:19',
                'updated_at' => '2022-03-31 18:05:19',
                'Code' => NULL,
                'Logo' => NULL,
            ),
            1 => 
            array (
                'id' => 2,
                'Company_Name' => '1',
                'Product_Name' => '1',
                'Product_Price' => NULL,
                'Unit' => NULL,
                'Coin' => NULL,
                'Group' => NULL,
                'created_at' => '2022-03-31 18:05:19',
                'updated_at' => '2022-03-31 18:05:19',
                'Code' => NULL,
                'Logo' => NULL,
            ),
            2 => 
            array (
                'id' => 3,
                'Company_Name' => '1',
                'Product_Name' => '1',
                'Product_Price' => '1',
                'Unit' => NULL,
                'Coin' => NULL,
                'Group' => NULL,
                'created_at' => '2022-03-31 18:05:19',
                'updated_at' => '2022-03-31 18:05:19',
                'Code' => NULL,
                'Logo' => NULL,
            ),
            3 => 
            array (
                'id' => 4,
                'Company_Name' => '1',
                'Product_Name' => '1',
                'Product_Price' => '1',
                'Unit' => NULL,
                'Coin' => NULL,
                'Group' => NULL,
                'created_at' => '2022-03-31 18:05:19',
                'updated_at' => '2022-03-31 18:05:19',
                'Code' => '1',
                'Logo' => NULL,
            ),
        ));
        
        
    }
}