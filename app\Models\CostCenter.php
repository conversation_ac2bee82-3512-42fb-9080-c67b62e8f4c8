<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Stancl\Tenancy\Database\Concerns\CentralConnection;

class CostCenter extends Model
{
    use HasFactory, CentralConnection;

      protected $table = 'cost_centers';
      protected $fillable = [
        'Arabic_Name',
        'English_Name',

    ];

         public function Journalizing()
    {
        return $this->hasOne(Journalizing::class);
    }

         public function GeneralDaily()
    {
        return $this->hasOne(GeneralDaily::class);
    }


        public function PaymentVoucher()
    {
        return $this->hasOne(PaymentVoucher::class);
    }

                   public function ReciptVoucher()
    {
        return $this->hasOne(ReciptVoucher::class);
    }


                        public function OpeningEntries()
    {
        return $this->hasOne(OpeningEntries::class);
    }

           public function IncomChecks()
    {
        return $this->hasOne(IncomChecks::class);
    }

            public function ExportChecks()
    {
        return $this->hasOne(ExportChecks::class);
    }

             public function Settlement()
    {
        return $this->hasOne(Settlement::class);
    }

                           public function SafeTransfers()
    {
        return $this->hasOne(SafeTransfers::class);
    }

                               public function StorsTransfers()
    {
        return $this->hasOne(StorsTransfers::class);
    }

         public function PurchasesOrder()
    {
        return $this->hasOne(PurchasesOrder::class);
    }

                     public function Purchases()
    {
        return $this->hasOne(Purchases::class);
    }


        public function Quote()
    {
        return $this->hasOne(Quote::class);
    }

           public function SalesOrder()
    {
        return $this->hasOne(SalesOrder::class);
    }

         public function Sales()
    {
        return $this->hasOne(Sales::class);
    }

              public function Borrowa()
    {
        return $this->hasOne(Borrowa::class);
    }

                  public function Loan()
    {
        return $this->hasOne(Loan::class);
    }

                    public function PaySalary()
    {
        return $this->hasOne(PaySalary::class);
    }

            public function ReciptMaintaince()
    {
        return $this->hasOne(ReciptMaintaince::class);
    }


}
