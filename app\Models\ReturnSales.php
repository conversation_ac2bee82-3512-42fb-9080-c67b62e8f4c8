<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Stancl\Tenancy\Database\Concerns\CentralConnection;

class ReturnSales extends Model
{
    use HasFactory, CentralConnection;
       protected $table = 'return_sales';
      protected $fillable = [
        'Code',
        'Date',
        'Total_Return_Qty',
        'Total_Return_Value',
        'Total_BF_Taxes',
        'Total_Taxes',
        'Total_Discount',
        'Sales',
        'Pay',
        'Payment_Method',
        'User',
        'Time',
        'Branch',
        'CustomerGroup',
        'Refernce_Number',
        'Safe',
        'Client',
        'Executor',
        'Delegate',
        'Store',
        'Coin',
        'Cost_Center',
        'Ship',
        'Later_Due',
        'InstallCompany',
        'Delivery',

        'Sent',
        'TaxBill',
        'TaxCode',
        'uuid',
        'longId',
        'hashKey',
        'submissionId',

    ];

           public function Sales()
    {
        return $this->belongsTo(Sales::class,'Sales');
    }

            public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }

        public function ReturnSalesProducts()
    {
        return $this->hasOne(ReturnSalesProducts::class);
    }

            public function Branch()
    {
        return $this->belongsTo(Branches::class,'Branch');
    }


           public function CustomerGroup()
    {
        return $this->belongsTo(CustomersGroup::class,'CustomerGroup');
    }

           public function Safe()
    {
        return $this->belongsTo(AcccountingManual::class,'Safe');
    }
          public function Client()
    {
        return $this->belongsTo(AcccountingManual::class,'Client');
    }
          public function Delegate()
    {
        return $this->belongsTo(Employess::class,'Delegate');
    }

              public function Executor()
    {
        return $this->belongsTo(Employess::class,'Executor');
    }

      public function Store()
    {
        return $this->belongsTo(Stores::class,'Store');
    }
          public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }
    public function Cost_Center()
    {
        return $this->belongsTo(CostCenter::class,'Cost_Center');
    }
          public function Ship()
    {
        return $this->belongsTo(AcccountingManual::class,'Ship');
    }

                public function Delivery()
    {
        return $this->belongsTo(Employess::class,'Delivery');
    }


                           public function InstallCompany()
    {
        return $this->belongsTo(InstallmentCompanies::class,'InstallCompany');
    }

}
