<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateDefaultDataShowHidesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('default_data_show_hides', function (Blueprint $table) {
            $table->id();

            // Basic display settings
            $table->string('Status')->default('1');
            $table->string('Shipping_Company')->default('0');
            $table->string('Vendor_Date')->default('1');
            $table->string('Expire_Date')->default('0');
            $table->string('Total_BF_Taxes')->default('0');
            $table->string('Total_Taxes')->default('0');
            $table->string('Coin')->default('0');
            $table->string('Draw')->default('1');
            $table->string('Delegate_Sale')->default('1');
            $table->string('Executor_Sale')->default('1');
            $table->string('Delegate_Purchase')->default('1');
            $table->string('Note')->default('1');
            $table->string('Refrence_Number')->default('1');
            $table->string('Cost_Center')->default('0');
            $table->string('Branch')->default('1');
            $table->string('Serial_Num')->default('1');
            $table->string('Pass')->default('0');
            $table->string('Pattern_Image')->default('0');

            // Print settings
            $table->string('Barcode_Print')->default('0');
            $table->string('Unit_Print')->default('1');
            $table->string('Total_BF_Print')->default('1');
            $table->string('Discount_Print')->default('1');
            $table->string('Tax_Print')->default('1');
            $table->string('A4')->default('1');
            $table->string('A5')->default('0');
            $table->string('CM8')->default('1');

            // Product and search settings
            $table->string('Search_Typical')->default('1');
            $table->string('Validity_Product')->default('1');
            $table->string('Group_Brand')->default('0');
            $table->string('Patch_Number')->default('0');
            $table->string('Manufacturing_Model_Shortcomings')->default('1');

            // POS and interface settings
            $table->string('Totuch_Screen')->default('0');
            $table->string('Tax_POS')->default('1');
            $table->string('TotalDiscountPrint')->nullable();
            $table->string('TotalTaxPrint')->nullable();
            $table->string('ProductsNumber')->nullable();
            $table->string('TotalQtyPrint')->nullable();
            $table->string('Credit')->nullable();
            $table->string('Barcode')->nullable();
            $table->string('Taknet')->nullable();

            // Contact and company info settings
            $table->string('Address')->nullable();
            $table->string('Phone1')->nullable();
            $table->string('Phone2')->nullable();
            $table->string('Phone3')->nullable();
            $table->string('Phone4')->nullable();
            $table->string('Text')->nullable();
            $table->string('Seal')->nullable();
            $table->string('Code_Report')->nullable();
            $table->string('Unit')->nullable();
            $table->string('Refrence_Number_Print')->nullable();
            $table->string('Icon_Payment_Recipt')->nullable();
            $table->string('SearchCode')->nullable();

            // Tax and financial settings
            $table->string('TaxOnTotal')->nullable();
            $table->string('TotalBfTax')->nullable();
            $table->string('AvQty')->nullable();
            $table->string('Disc')->nullable();
            $table->string('Tax')->nullable();
            $table->string('Store')->nullable();
            $table->string('TaxBill')->nullable();
            $table->string('Change_Way_Stores_Transfer')->nullable();
            $table->string('Note_POS')->nullable();
            $table->string('Open_Drawer')->nullable();
            $table->string('client_delivery')->nullable();
            $table->string('POS_RecivedDate')->nullable();
            $table->string('POS_Qty')->nullable();
            $table->string('POS_Barcode')->nullable();

            // File display settings
            $table->string('Show_File_ReciptVoucher')->nullable();
            $table->string('Show_File_PaymentVoucher')->nullable();
            $table->string('Show_File_Sales')->nullable();
            $table->string('Show_File_Purchases')->nullable();
            $table->string('Show_File_Checks')->nullable();
            $table->string('Show_File_InsurancePaper')->nullable();
            $table->string('Show_File_TransferStores')->nullable();

            // Print dimensions
            $table->string('Thickness_Print')->nullable();
            $table->string('Height_Print')->nullable();
            $table->string('Thickness')->nullable();
            $table->string('Height')->nullable();
            $table->string('Items_Guide_Store_Show')->nullable();
            $table->string('Sales_Pro_Desc')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('default_data_show_hides');
    }
}
