<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\Admin;

try {
    echo "=== Creating Test Admin User ===\n";

    // Check if test admin already exists
    $existingAdmin = Admin::where('email', '<EMAIL>')->first();

    if ($existingAdmin) {
        echo "ℹ️  Test admin already exists. Updating password...\n";
        $existingAdmin->update([
            'password' => bcrypt('password123')
        ]);
        echo "✅ Password <NAME_EMAIL>\n";
    } else {
        // Create new test admin (let ID auto-increment)
        $admin = new Admin([
            'email' => '<EMAIL>',
            'name' => 'Test Admin',
            'nameEn' => 'Test Admin',
            'password' => bcrypt('password123'),
            'phone' => '**********',
            'hidden' => '0',
            'status' => '0',
            'type' => 'Admin',
            'roles_name' => 'Owner',
            'emp' => '0',
            'ship' => '0',
            'vend' => '0',
            'cli' => '0',
            'account' => '0',
            'price_level' => '1',
            'guest' => '0',
            'pos_product' => '0',
            'Cash' => '0',
            'Delivery' => '0',
            'Cash_Collection' => '0',
            'Cash_Visa' => '0',
            'Installment' => '0',
            'Check' => '0',
            'Later' => '0',
            'InstallmentCompanies' => '0',
        ]);
        $admin->save();

        echo "✅ Test admin created successfully!\n";
    }

    echo "\n=== Login Credentials ===\n";
    echo "Email: <EMAIL>\n";
    echo "Password: password123\n";
    echo "\n=== Test admin setup completed! ===\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
