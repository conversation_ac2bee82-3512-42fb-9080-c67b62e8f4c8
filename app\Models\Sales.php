<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Stancl\Tenancy\Database\Concerns\CentralConnection;

class Sales extends Model
{
    use HasFactory, CentralConnection;
     protected $table = 'sales';
      protected $fillable = [

        'Code',
        'Store_Code',
        'Date',
        'Draw',
        'Payment_Method',
        'Status',
        'Refernce_Number',
        'Note',
        'Product_Numbers',
        'Total_Qty',
        'Total_Discount',
        'Total_BF_Taxes',
        'Total_Taxes',
        'Total_Price',
        'Pay',
        'Safe',
        'ShipStatus',
        'Client',
        'Executor',
        'Delegate',
        'Store',
        'Coin',
        'Cost_Center',
        'User',
        'presenter',
        'annual_interest',
        'monthly_installment',
        'Years_Number',
        'total',
        'Quote',
        'SalesOrder',
        'installment_Num',
        'Date_First_installment',
        'Ship',
        'Check_Type',
        'Due_Date',
        'Check_Number',
        'Cash',
        'Visa',
        'Sale_Type',
        'Hold',
        'Shift_Code',
        'emp',
        'Later_Due',
        'Later_Collection',
        'Client_Address',
        'Delivery',
        'Delivery_Status',
        'Sale_User',
        'Sent',
        'TaxBill',
        'TaxCode',
        'ProfitPrecent',
        'TaxOnTotal',
        'TaxOnTotalType',
        'ProfitTax',
        'InstallCompany',
        'ContractNumber',
        'PayFees',
        'ServiceFee',
        'CompanyPrecent',
        'Time',
        'Branch',
        'CustomerGroup',
        'Total_Cost',
        'uuid',
        'longId',
        'hashKey',
        'submissionId',
        'statusBill',
        'DiscountTax',
        'RecivedDate',
          'File',


                   'TakeawayStatus',
       'TakeawayTime',
        'Witer',
        'KitchenEnd',
        'KitchenEndTime',
        'RecivedOrder',
        'RecivedOrderTime',
        'DeliveryTime',
        'ResturantOrderType',
        'Table',
        'Total_Wight_Bill',
    ];




    public function Witer()
    {
        return $this->belongsTo(Employess::class,'Witer');
    }          public function Table()
    {
        return $this->belongsTo(ResturantTables::class,'Table');
    }


         public function Safe()
    {
        return $this->belongsTo(AcccountingManual::class,'Safe');
    }
          public function Client()
    {
        return $this->belongsTo(AcccountingManual::class,'Client');
    }
          public function Delegate()
    {
        return $this->belongsTo(Employess::class,'Delegate');
    }

              public function Executor()
    {
        return $this->belongsTo(Employess::class,'Executor');
    }

                 public function emp()
    {
        return $this->belongsTo(Employess::class,'emp');
    }


          public function Store()
    {
        return $this->belongsTo(Stores::class,'Store');
    }
          public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }
          public function Cost_Center()
    {
        return $this->belongsTo(CostCenter::class,'Cost_Center');
    }
          public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }

              public function ProductSales()
    {
        return $this->hasOne(ProductSales::class);
    }

               public function Installment()
    {
        return $this->hasOne(Installment::class);
    }

                   public function RecivedSales()
    {
        return $this->hasOne(RecivedSales::class);
    }

                     public function ReturnSales()
    {
        return $this->hasOne(ReturnSales::class);
    }

                   public function Ship()
    {
        return $this->belongsTo(AcccountingManual::class,'Ship');
    }

                   public function Check_Type()
    {
        return $this->belongsTo(ChecksTypes::class,'Check_Type');
    }

                     public function Delivery()
    {
        return $this->belongsTo(Employess::class,'Delivery');
    }

                      public function Client_Address()
    {
        return $this->belongsTo(Addressses::class,'Client_Address');
    }

             public function Sale_User()
    {
        return $this->belongsTo(Admin::class,'Sale_User');
    }


               public function TaxOnTotalType()
    {
        return $this->belongsTo(Taxes::class,'TaxOnTotalType');
    }


                           public function InstallCompany()
    {
        return $this->belongsTo(InstallmentCompanies::class,'InstallCompany');
    }



        public function Branch()
    {
        return $this->belongsTo(Branches::class,'Branch');
    }


           public function CustomerGroup()
    {
        return $this->belongsTo(CustomersGroup::class,'CustomerGroup');
    }




}
