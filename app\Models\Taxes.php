<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Stancl\Tenancy\Database\Concerns\CentralConnection;

class Taxes extends Model
{
    use HasFactory, CentralConnection;
          protected $table = 'taxes';
      protected $fillable = [
        'Code',
        'Name',
        'NameEn',
        'Rate',
        'Type',
        'Hide',
        'Account',
        'CodeTax',
        'SubType',


    ];

            public function Account()
    {
        return $this->belongsTo(AcccountingManual::class,'Account');
    }

              public function Products()
    {
        return $this->hasOne(Products::class);
    }

                 public function ProductsPurchases()
    {
        return $this->hasOne(ProductsPurchases::class);
    }

}
