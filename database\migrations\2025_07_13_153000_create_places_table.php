<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePlacesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('places', function (Blueprint $table) {
            $table->id();

            // Place information
            $table->string('Arabic_Name')->nullable();
            $table->string('English_Name')->nullable();
            $table->string('Ship_Price')->nullable();
            $table->string('SearchCode')->nullable();

            // References
            $table->string('City')->nullable();
            $table->string('Delivery')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('places');
    }
}
