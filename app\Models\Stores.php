<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Stancl\Tenancy\Database\Concerns\CentralConnection;

class Stores extends Model
{
    use HasFactory, CentralConnection;


    protected $table = 'stores';
      protected $fillable = [
        'Code',
        'Date',
        'Time',
        'Name',
        'NameEn',
        'Phone',
        'Address',
        'Account',
        'User',
        'Branch',
        'Letter',
        'Account_Client',

    ];

             public function Branch()
    {
        return $this->belongsTo(Branches::class,'Branch');
    }

         public function Account()
    {
        return $this->belongsTo(AcccountingManual::class,'Account');
    }

             public function Account_Client()
    {
        return $this->belongsTo(AcccountingManual::class,'Account_Client');
    }


         public function User()
    {
        return $this->belongsTo(Admin::class,'User');
    }

                           public function ProductMoves()
    {
        return $this->hasOne(ProductMoves::class);
    }

           public function StartPeriods()
    {
        return $this->hasOne(StartPeriods::class);
    }

                public function ProductsQty()
    {
        return $this->hasMany(ProductsQty::class);
    }

               public function ProductsStores()
    {
        return $this->hasOne(ProductsStores::class);
    }


                   public function Inventory()
    {
        return $this->hasOne(Inventory::class);
    }

                          public function Settlement()
    {
        return $this->hasOne(Settlement::class);
    }

                           public function StorsTransfers()
    {
        return $this->hasOne(StorsTransfers::class);
    }

        public function PurchasesOrder()
    {
        return $this->hasOne(PurchasesOrder::class);
    }

          public function ProductsPurchasesOrder()
    {
        return $this->hasOne(ProductsPurchasesOrder::class);
    }

                 public function ProductsPurchases()
    {
        return $this->hasOne(ProductsPurchases::class);
    }

                     public function Purchases()
    {
        return $this->hasOne(Purchases::class);
    }

                          public function RecivedPurchProducts()
    {
        return $this->hasOne(RecivedPurchProducts::class);
    }

        public function ReturnPurchProducts()
    {
        return $this->hasOne(ReturnPurchProducts::class);
    }

              public function ProductsQuote()
    {
        return $this->hasOne(ProductsQuote::class);
    }

           public function SalesOrder()
    {
        return $this->hasOne(SalesOrder::class);
    }

         public function Sales()
    {
        return $this->hasOne(Sales::class);
    }

           public function ProductSales()
    {
        return $this->hasOne(ProductSales::class);
    }

         public function ProductSalesOrder()
    {
        return $this->hasOne(ProductSalesOrder::class);
    }

           public function RecivedSalesProducts()
    {
        return $this->hasOne(RecivedSalesProducts::class);
    }

          public function ReturnSalesProducts()
    {
        return $this->hasOne(ReturnSalesProducts::class);
    }




}
