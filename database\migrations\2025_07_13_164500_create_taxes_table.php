<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTaxesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('taxes', function (Blueprint $table) {
            $table->id();
            
            // Tax information
            $table->string('Code')->nullable();         // Tax code
            $table->string('Name')->nullable();         // Tax name (Arabic)
            $table->string('NameEn')->nullable();       // Tax name (English)
            $table->decimal('Rate', 5, 2)->nullable();  // Tax rate percentage
            $table->string('Type')->nullable();         // Tax type
            $table->string('Hide')->nullable();         // Hide flag
            $table->string('CodeTax')->nullable();      // Tax code reference
            $table->string('SubType')->nullable();      // Tax sub type
            
            // Account reference
            $table->string('Account')->nullable();      // Account reference for tax
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('taxes');
    }
}
