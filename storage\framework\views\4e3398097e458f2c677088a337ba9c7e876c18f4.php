<?php $__env->startSection('content'); ?>
<?php
use App\Models\AcccountingManual;
use App\Models\AccountsDefaultData;
$Def=AccountsDefaultData::orderBy('id','desc')->first();
?>
  <title><?php echo e(trans('admin.Accounting_Manual')); ?></title>

   <main id="js-page-content" role="main" class="page-content">
                        <ol class="breadcrumb page-breadcrumb no-print">
                            <li class="breadcrumb-item"><a href="index.html"><?php echo e(trans('admin.Accounts')); ?></a></li>  
                            <li class="breadcrumb-item active"><?php echo e(trans('admin.Accounting_Manual')); ?></li>
                            <li class="position-absolute pos-top pos-right d-none d-sm-block"><span class="js-get-date"></span></li>
                        </ol>
       
       <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('اضافه حساب في الدليل المحاسبي')): ?>
                        <div class="row">
                            <div class="col-lg-12">
                                <div id="panel-2" class="panel">
                                    <div class="panel-hdr no-print">
                                        <h2>
                                            <span class="fw-300"><i><?php echo e(trans('admin.AddNew')); ?></i></span>
                                        </h2>
                                    </div>
                                    <div class="panel-container show">         
                                        <div class="panel-content">
                                            <form class="form-row" action="<?php echo e(url('AddAccount')); ?>" method="post">
                                                <?php echo csrf_field(); ?>

                                                <?php echo view('honeypot::honeypotFormFields'); ?>
                                                <div class="form-group col-md-2 col-2-print">
                                    <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Arabic_Name')); ?></label>
                    <input type="text" name="Name" value="<?php echo e(old('Name')); ?>" id="simpleinput" class="form-control" required>
                                                </div>
                                                                  <div class="form-group col-md-2 col-2-print">
                                    <label class="form-label" for="simpleinput"><?php echo e(trans('admin.English_Name')); ?></label>
                    <input type="text" name="NameEn" value="<?php echo e(old('NameEn')); ?>" id="simpleinput" class="form-control" >
                                                </div>
                                                
                                                
                                                <div class="form-group col-md-2 col-2-print">
                                                    <label class="form-label" for="">  <?php echo e(trans('admin.Account_Type')); ?></label>
                                         <select class="select2 form-control w-100" name="Type" required>
                                                        <option value="0"><?php echo e(trans('admin.Main')); ?></option>
                                                        <option value="1"><?php echo e(trans('admin.Sub')); ?></option>
                                                    </select>
                                                </div>
                                                
                                                <div class="form-group col-md-2 col-2-print">
                                                    <label class="form-label" for=""> <?php echo e(trans('admin.Main_Account')); ?> </label>
                           <select id="account" class="select2 form-control w-100"  name="Parent">
                                      
                                              
                                                    </select>
                                                </div>
                                                               <div class="form-group col-md-2 col-2-print">
                                    <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Code')); ?></label>
                    <input type="text" name="Account_Code" value="<?php echo e(old('Account_Code')); ?>" id="simpleinput" class="form-control" >
                                                </div>
 
                                                <div class="form-group col-md-2 col-2-print">
                                     <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Notes')); ?></label>
                          <input type="text" name="Note" id="simpleinput" class="form-control" value="<?php echo e(old('Note')); ?>">
                                                </div>
                                                
                                                <?php if($Def->Show_Group == 1): ?>
                                                                 <div class="form-group col-md-2 col-2-print">
                                     <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Group')); ?></label>
                          <select class="select2 form-control" name="Pro_Group">
                                        <option value=""><?php echo e(trans('admin.Group')); ?></option>     
                              <?php $__currentLoopData = $Groups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $grop): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                              
                                     <option value="<?php echo e($grop->id); ?>"><?php echo e($grop->Name); ?></option>   
                              <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </select>
                                                </div>
                                                <?php endif; ?>
            
                                                <div class="form-group col-md-2 buttons mt-2 pt-3 no-print">
                              <button type="submit" class="btn btn-primary"> <i class="fal fa-folder"></i> <?php echo e(trans('admin.Save')); ?></button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
       <?php endif; ?>
       
                        <div class="row">
                            <div class="col-lg-12">
                                <div id="panel-1" class="panel">
                                    <div class="panel-hdr">
                                        <h2>
                                         <span class="fw-300"><i><?php echo e(trans('admin.Accounting_Manual')); ?></i></span>
                                         <span class="fw-300" style="color: darkred"><i>(<?php echo e(trans('admin.AlertAccounting')); ?>)</i></span>
                                        </h2>
                  
                                    </div>
                                    <div class="panel-container show">
                                     <span id="ex"> <?php echo $__env->make('admin.layouts.messages', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>          
                                        <div class="panel-content">
                                            <div class="tree">
                                                <ul>
                                                    <?php $__currentLoopData = $parents; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $par): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <li>
                                 <span><i class="fal fa-folder"></i> <?php echo e($par->Code); ?> -  <?php echo e(app()->getLocale() == 'ar' ?$par->Name :$par->NameEn); ?>

                              
                         
                                                        
                                                        </span>
                                                        
                                                  
                                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('حذف حساب في الدليل المحاسبي')): ?>   
                                                        <button type="button" class="btn btn-default" data-toggle="modal" data-target="#default-example-modal-center<?php echo e($par->id); ?>"><i class="fal fa-trash-alt"></i></button>
                                                        <?php endif; ?>
                                                        
                                                       <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تعديل حساب في الدليل المحاسبي')): ?>  
                                                        <button type="button" class="btn btn-default" data-toggle="modal" data-target="#default-example-modal-lg-edit<?php echo e($par->id); ?>"><i class="fal fa-edit"></i></button>
                                                        <?php endif; ?>
                                                    
                                         <?php    $Childs=AcccountingManual::orderBy('Code','asc')->where('Parent',$par->id)->limit(100)->get();   ?>
                                                        
                                                <?php echo $__env->make('admin.Accounts.Childs',['Childs' => $Childs,'Groups'=>$Groups], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
 
                                                    </li>
                                                  
                                               
                                     <!-- Modal Edit-->
                        <div class="modal fade" id="default-example-modal-lg-edit<?php echo e($par->id); ?>" tabindex="-1" role="dialog" aria-hidden="true">
                            <div class="modal-dialog modal-lg" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title"><?php echo e(trans('admin.Edit')); ?>  </h5>
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                            <span aria-hidden="true"><i class="fal fa-times"></i></span>
                                        </button>
                                    </div>
                                    <div class="modal-body">
                                        <form action="<?php echo e(url('EditAccount')); ?>" method="post">
                                        <?php echo csrf_field(); ?>

                                            <?php echo view('honeypot::honeypotFormFields'); ?>
                                    <input type="hidden" name="ID" value="<?php echo e($par->id); ?>">        
                                            <div class="form-row">
                                                <div class="form-group col-lg-12">
                                                    <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Arabic_Name')); ?> </label>
                     <input type="text" name="Name" value="<?php echo e($par->Name); ?>" id="simpleinput" class="form-control" required>
                                                </div>
                                                
                                                
                                                                                <div class="form-group col-md-12 col-12-print">
                                    <label class="form-label" for="simpleinput"><?php echo e(trans('admin.English_Name')); ?></label>
                    <input type="text" name="NameEn" value="<?php echo e($par->NameEn); ?>" id="simpleinput" class="form-control" required>
                                                </div>
                                               <div class="form-group col-lg-12">
                        <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Code')); ?> </label>
                        <input type="text" name="Account_Code" value="<?php echo e($par->Account_Code); ?>" id="simpleinput" class="form-control" required>
                     </div>
                                                <div class="form-group col-md-12">
                              <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Notes')); ?></label>
                    <input type="text" name="Note" id="simpleinput" class="form-control" value="<?php echo e($par->Note); ?>">
                                                </div>
                                                
                                                    <?php if($Def->Show_Group == 1): ?>
                                                <div class="form-group col-md-12 col-12-print">
                                     <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Group')); ?></label>
                          <select class="select2 form-control" name="Pro_Group">
                                        <option value=""><?php echo e(trans('admin.Group')); ?></option>     
                              <?php $__currentLoopData = $Groups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $grop): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                              
                                     <option value="<?php echo e($grop->id); ?>" <?php if($grop->id == $par->Pro_Group): ?> selected <?php endif; ?>><?php echo e($grop->Name); ?></option>   
                              <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </select>
                                                </div>
                                                <?php endif; ?>
                                                
                                            </div>
                                  <div class="modal-footer">
                      <button type="button" class="btn btn-secondary" data-dismiss="modal"><?php echo e(trans('admin.Name')); ?> </button>
                                  <button type="submit" class="btn btn-primary"><?php echo e(trans('admin.SaveChanges')); ?> </button>
                                    </div>                    
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>                    
                                                    
                               <!-- Modal Delete -->
                        <div class="modal fade" id="default-example-modal-center<?php echo e($par->id); ?>" tabindex="-1" role="dialog" aria-hidden="true">
                            <div class="modal-dialog modal-dialog-centered" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h4 class="modal-title">
                                            
                                            <?php echo e(trans('admin.RUSWDT')); ?> <strong> <?php echo e(app()->getLocale() == 'ar' ?$par->Name :$par->NameEn); ?></strong>   
                                        </h4>
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                            <span aria-hidden="true"><i class="fal fa-times"></i></span>
                                        </button>
                                    </div>
                                    
                                    <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal"><?php echo e(trans('admin.No')); ?></button>
                        <a href="<?php echo e(url('DeleteAccount/'.$par->id)); ?>" class="btn btn-primary"><?php echo e(trans('admin.Yes')); ?></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                                   
                                                    
                                                   <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                      <div class="text-center">
                                                 <button type="button" class="btn btn-primary"  onclick="window.print()">
                                                   <i class="fal fa-print"></i>  <?php echo e(trans('admin.Print')); ?>

                                                 </button>   
                                                 </div>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
       
                   
                 
                    </main>


<?php $__env->stopSection(); ?>


<?php $__env->startPush('js'); ?>
     <!-- DEMO related CSS below -->
        <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/fa-brands.css')); ?>">
        <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/miscellaneous/treeview/treeview.css')); ?>">
        <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/formplugins/select2/select2.bundle.css')); ?>">


    <script src="<?php echo e(asset('Admin/js/miscellaneous/treeview/treeview.js')); ?>"></script>
        <script src="<?php echo e(asset('Admin/js/formplugins/select2/select2.bundle.js')); ?>"></script>
        <script>
            document.addEventListener("DOMContentLoaded", function()
            {
                // Handler when the DOM is fully loaded
            });

        </script>
      <!-- Search Selecet -->
 <script>
        $(document).ready(function()
        {
            $(function()
            {
                $('.select2').select2();

                $(".select2-placeholder-multiple").select2(
                {
                    placeholder: "Select State"
                });
                $(".js-hide-search").select2(
                {
                    minimumResultsForSearch: 1 / 0
                });
                $(".js-max-length").select2(
                {
                    maximumSelectionLength: 2,
                    placeholder: "Select maximum 2 items"
                });
                $(".select2-placeholder").select2(
                {
                    placeholder: "Select a state",
                    allowClear: true
                });

                $(".js-select2-icons").select2(
                {
                    minimumResultsForSearch: 1 / 0,
                    templateResult: icon,
                    templateSelection: icon,
                    escapeMarkup: function(elm)
                    {
                        return elm
                    }
                });

                function icon(elm)
                {
                    elm.element;
                    return elm.id ? "<i class='" + $(elm.element).data("icon") + " mr-2'></i>" + elm.text : elm.text
                }

    
        
  $('#account').select2({
    placeholder: "select...",
    ajax: {
        type: "GET",
        dataType: 'json',
        url: 'AllMainAccounts',
        processResults: function (data) {
          return {
            results: $.map(data, function(obj, index) {
              return { id: index, text: obj };
            })
          };
            
            	console.log(data);
            
        },
        data: function (params) {  
   
            
                   var query = {
                            search: params.term,
                        };
                                
            
              $.ajax({
                              url: 'MainAccountss/'+params.term,
                              type:"GET",
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },

                              success:function(data) {
                                          $('#account').empty();  
                                  $.each(data, function(key, value){
   
                         $('#account').append('<option value="'+ key +'">' + value + '</option>');
                   
                                  });
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });
            
            
            
        }
    }
  });

    
$('#account').on('select2:select', function (e) {
	console.log("select done", e.params.data);
});
           
     

             
            });
        });
    </script>
    
    <style>
        .form-control {
           border: 2px solid #584576 !important;
        }
        .btn i {
            /*color: red;*/
        }
    </style>

<?php $__env->stopPush(); ?>
<?php echo $__env->make('admin.index', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp2\htdocs\erp\resources\views/admin/Accounts/AccountingManual.blade.php ENDPATH**/ ?>