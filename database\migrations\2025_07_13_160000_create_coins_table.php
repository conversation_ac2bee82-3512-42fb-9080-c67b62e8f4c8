<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCoinsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('coins', function (Blueprint $table) {
            $table->id();
            
            // Currency information
            $table->string('Arabic_Name')->nullable();  // Arabic currency name
            $table->string('English_Name')->nullable(); // English currency name
            $table->string('Symbol')->nullable();       // Currency symbol (EGP, USD, SAR, etc.)
            $table->string('Draw')->nullable();         // Drawing/document settings
            $table->string('Code')->nullable();         // Currency code
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('coins');
    }
}
