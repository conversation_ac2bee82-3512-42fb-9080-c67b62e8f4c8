<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateGovernratesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('governrates', function (Blueprint $table) {
            $table->id();
            
            // Governorate information
            $table->string('Arabic_Name')->nullable();
            $table->string('English_Name')->nullable();
            $table->string('Country')->nullable(); // References countris table
            $table->string('SearchCode')->nullable();
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('governrates');
    }
}
