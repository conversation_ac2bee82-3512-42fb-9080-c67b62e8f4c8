<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateStoresTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('stores', function (Blueprint $table) {
            $table->id();
            
            // Store basic information
            $table->string('Code')->nullable();         // Store code
            $table->date('Date')->nullable();           // Creation date
            $table->string('Time')->nullable();         // Creation time
            $table->string('Name')->nullable();         // Store name (Arabic)
            $table->string('NameEn')->nullable();       // Store name (English)
            $table->string('Phone')->nullable();        // Store phone
            $table->string('Address')->nullable();      // Store address
            $table->string('Letter')->nullable();       // Store letter code
            
            // Account references
            $table->string('Account')->nullable();       // Store account reference
            $table->string('Account_Client')->nullable(); // Client account reference
            
            // User and branch references
            $table->string('User')->nullable();         // User who created the store
            $table->string('Branch')->nullable();       // Branch reference
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('stores');
    }
}
