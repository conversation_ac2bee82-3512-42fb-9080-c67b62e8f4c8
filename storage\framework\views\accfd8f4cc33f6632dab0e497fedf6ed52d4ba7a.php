<?php $__env->startSection('content'); ?>
<?php
use App\Models\ProductSalesOrder;
use App\Models\CompanyData;
use App\Models\GeneralDaily;
$Def=CompanyData::orderBy('id','desc')->first();
use App\Models\DefaultDataShowHide;
$show=DefaultDataShowHide::orderBy('id','desc')->first();
?>
  <title><?php echo e(trans('admin.Orders')); ?></title>


     <main id="js-page-content" role="main" class="page-content">
                    <ol class="breadcrumb page-breadcrumb">
                        <li class="breadcrumb-item"><a href="javascript:void(0);"><?php echo e(trans('admin.Website')); ?></a></li>
                        <li class="breadcrumb-item active"> <?php echo e(trans('admin.Orders')); ?> </li>
                        <li class="position-absolute pos-top pos-right d-none d-sm-block"><span
                                class="js-get-date"></span></li>
                    </ol>
           
            <!-- Data -->
                    <div class="row hide-table">
                        <div class="col-xl-12">
                            <div id="panel-1" class="panel">
                                <div class="panel-hdr">
                                    <h2>
                                    <?php echo e(trans('admin.SalesOrderSechdule')); ?>

                                    </h2>
                                  
                                    <div class="panel-toolbar">
                                        <button class="btn btn-primary btn-sm" data-toggle="dropdown">Table
                                            Style</button>
                                        <div
                                            class="dropdown-menu dropdown-menu-animated dropdown-menu-right position-absolute pos-top">
                                            <button class="dropdown-item active" data-action="toggle"
                                                data-class="table-bordered" data-target="#dt-basic-example"> Bordered
                                                Table </button>
                                            <button class="dropdown-item" data-action="toggle" data-class="table-sm"
                                                data-target="#dt-basic-example"> Smaller Table </button>
                                            <button class="dropdown-item" data-action="toggle" data-class="table-dark"
                                                data-target="#dt-basic-example"> Table Dark </button>
                                            <button class="dropdown-item active" data-action="toggle"
                                                data-class="table-hover" data-target="#dt-basic-example"> Table Hover
                                            </button>
                                            <button class="dropdown-item active" data-action="toggle"
                                                data-class="table-stripe" data-target="#dt-basic-example"> Table
                                                Stripped </button>
                                            <div class="dropdown-divider m-0"></div>
                                            <div class="dropdown-multilevel dropdown-multilevel-left">
                                                <div class="dropdown-item">
                                                    tbody color
                                                </div>
                                                <div class="dropdown-menu no-transition-delay">
                                                    <div class="js-tbody-colors dropdown-multilevel dropdown-multilevel-left d-flex flex-wrap"
                                                        style="width: 15.9rem; padding: 0.5rem">
                                                        <a href="javascript:void(0);" data-bg="bg-primary-100"
                                                            class="btn d-inline-block bg-primary-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-200"
                                                            class="btn d-inline-block bg-primary-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-300"
                                                            class="btn d-inline-block bg-primary-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-400"
                                                            class="btn d-inline-block bg-primary-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-500"
                                                            class="btn d-inline-block bg-primary-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-600"
                                                            class="btn d-inline-block bg-primary-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-700"
                                                            class="btn d-inline-block bg-primary-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-800"
                                                            class="btn d-inline-block bg-primary-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-900"
                                                            class="btn d-inline-block bg-primary-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-100"
                                                            class="btn d-inline-block bg-success-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-200"
                                                            class="btn d-inline-block bg-success-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-300"
                                                            class="btn d-inline-block bg-success-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-400"
                                                            class="btn d-inline-block bg-success-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-500"
                                                            class="btn d-inline-block bg-success-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-600"
                                                            class="btn d-inline-block bg-success-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-700"
                                                            class="btn d-inline-block bg-success-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-800"
                                                            class="btn d-inline-block bg-success-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-900"
                                                            class="btn d-inline-block bg-success-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-100"
                                                            class="btn d-inline-block bg-info-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-200"
                                                            class="btn d-inline-block bg-info-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-300"
                                                            class="btn d-inline-block bg-info-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-400"
                                                            class="btn d-inline-block bg-info-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-500"
                                                            class="btn d-inline-block bg-info-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-600"
                                                            class="btn d-inline-block bg-info-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-700"
                                                            class="btn d-inline-block bg-info-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-800"
                                                            class="btn d-inline-block bg-info-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-900"
                                                            class="btn d-inline-block bg-info-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-100"
                                                            class="btn d-inline-block bg-danger-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-200"
                                                            class="btn d-inline-block bg-danger-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-300"
                                                            class="btn d-inline-block bg-danger-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-400"
                                                            class="btn d-inline-block bg-danger-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-500"
                                                            class="btn d-inline-block bg-danger-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-600"
                                                            class="btn d-inline-block bg-danger-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-700"
                                                            class="btn d-inline-block bg-danger-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-800"
                                                            class="btn d-inline-block bg-danger-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-900"
                                                            class="btn d-inline-block bg-danger-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-100"
                                                            class="btn d-inline-block bg-warning-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-200"
                                                            class="btn d-inline-block bg-warning-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-300"
                                                            class="btn d-inline-block bg-warning-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-400"
                                                            class="btn d-inline-block bg-warning-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-500"
                                                            class="btn d-inline-block bg-warning-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-600"
                                                            class="btn d-inline-block bg-warning-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-700"
                                                            class="btn d-inline-block bg-warning-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-800"
                                                            class="btn d-inline-block bg-warning-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-900"
                                                            class="btn d-inline-block bg-warning-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-100"
                                                            class="btn d-inline-block bg-fusion-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-200"
                                                            class="btn d-inline-block bg-fusion-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-300"
                                                            class="btn d-inline-block bg-fusion-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-400"
                                                            class="btn d-inline-block bg-fusion-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-500"
                                                            class="btn d-inline-block bg-fusion-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-600"
                                                            class="btn d-inline-block bg-fusion-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-700"
                                                            class="btn d-inline-block bg-fusion-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-800"
                                                            class="btn d-inline-block bg-fusion-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-900"
                                                            class="btn d-inline-block bg-fusion-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg=""
                                                            class="btn d-inline-block bg-white border width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="dropdown-multilevel dropdown-multilevel-left">
                                                <div class="dropdown-item">
                                                    thead color
                                                </div>
                                                <div class="dropdown-menu no-transition-delay">
                                                    <div class="js-thead-colors dropdown-multilevel dropdown-multilevel-left d-flex flex-wrap"
                                                        style="width: 15.9rem; padding: 0.5rem">
                                                        <a href="javascript:void(0);" data-bg="bg-primary-100"
                                                            class="btn d-inline-block bg-primary-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-200"
                                                            class="btn d-inline-block bg-primary-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-300"
                                                            class="btn d-inline-block bg-primary-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-400"
                                                            class="btn d-inline-block bg-primary-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-500"
                                                            class="btn d-inline-block bg-primary-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-600"
                                                            class="btn d-inline-block bg-primary-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-700"
                                                            class="btn d-inline-block bg-primary-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-800"
                                                            class="btn d-inline-block bg-primary-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-900"
                                                            class="btn d-inline-block bg-primary-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-100"
                                                            class="btn d-inline-block bg-success-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-200"
                                                            class="btn d-inline-block bg-success-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-300"
                                                            class="btn d-inline-block bg-success-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-400"
                                                            class="btn d-inline-block bg-success-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-500"
                                                            class="btn d-inline-block bg-success-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-600"
                                                            class="btn d-inline-block bg-success-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-700"
                                                            class="btn d-inline-block bg-success-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-800"
                                                            class="btn d-inline-block bg-success-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-900"
                                                            class="btn d-inline-block bg-success-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-100"
                                                            class="btn d-inline-block bg-info-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-200"
                                                            class="btn d-inline-block bg-info-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-300"
                                                            class="btn d-inline-block bg-info-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-400"
                                                            class="btn d-inline-block bg-info-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-500"
                                                            class="btn d-inline-block bg-info-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-600"
                                                            class="btn d-inline-block bg-info-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-700"
                                                            class="btn d-inline-block bg-info-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-800"
                                                            class="btn d-inline-block bg-info-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-900"
                                                            class="btn d-inline-block bg-info-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-100"
                                                            class="btn d-inline-block bg-danger-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-200"
                                                            class="btn d-inline-block bg-danger-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-300"
                                                            class="btn d-inline-block bg-danger-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-400"
                                                            class="btn d-inline-block bg-danger-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-500"
                                                            class="btn d-inline-block bg-danger-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-600"
                                                            class="btn d-inline-block bg-danger-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-700"
                                                            class="btn d-inline-block bg-danger-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-800"
                                                            class="btn d-inline-block bg-danger-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-900"
                                                            class="btn d-inline-block bg-danger-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-100"
                                                            class="btn d-inline-block bg-warning-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-200"
                                                            class="btn d-inline-block bg-warning-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-300"
                                                            class="btn d-inline-block bg-warning-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-400"
                                                            class="btn d-inline-block bg-warning-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-500"
                                                            class="btn d-inline-block bg-warning-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-600"
                                                            class="btn d-inline-block bg-warning-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-700"
                                                            class="btn d-inline-block bg-warning-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-800"
                                                            class="btn d-inline-block bg-warning-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-900"
                                                            class="btn d-inline-block bg-warning-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-100"
                                                            class="btn d-inline-block bg-fusion-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-200"
                                                            class="btn d-inline-block bg-fusion-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-300"
                                                            class="btn d-inline-block bg-fusion-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-400"
                                                            class="btn d-inline-block bg-fusion-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-500"
                                                            class="btn d-inline-block bg-fusion-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-600"
                                                            class="btn d-inline-block bg-fusion-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-700"
                                                            class="btn d-inline-block bg-fusion-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-800"
                                                            class="btn d-inline-block bg-fusion-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-900"
                                                            class="btn d-inline-block bg-fusion-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg=""
                                                            class="btn d-inline-block bg-white border width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                                <div class="panel-container show">
                              <span id="ex"> <?php echo $__env->make('admin.layouts.messages', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>  
                                    <div class="panel-content">
                                        
                                        <!-- datatable start -->
                                        <div style="overflow:auto;">
        <table id="dt-basic-example" class="table table-bordered table-hover table-striped " style="width:120%;">
                                            <thead class="bg-highlight">
                                                <tr>
                                                    <th><?php echo e(trans('admin.Code')); ?></th>
                                                    <th><?php echo e(trans('admin.Time')); ?></th>
                                                    <th><?php echo e(trans('admin.Date')); ?></th>
                                                    <th> <?php echo e(trans('admin.Draw')); ?> </th>  
                                                    <th> <?php echo e(trans('admin.Payment_Method')); ?></th>
                                                    <th><?php echo e(trans('admin.Status')); ?> </th>
                                                    <th> <?php echo e(trans('admin.Executor')); ?> </th>
                                                    <th> <?php echo e(trans('admin.Refernce_Number')); ?> </th>
                                                    <th> <?php echo e(trans('admin.Notes')); ?> </th>
                                                    <th> <?php echo e(trans('admin.Safe')); ?> </th>
                                                    <th> <?php echo e(trans('admin.Client')); ?> </th>
                                                    <th> <?php echo e(trans('admin.Delegate')); ?> </th>
                                                    <th> <?php echo e(trans('admin.Store')); ?> </th>
                                                    <th> <?php echo e(trans('admin.Later_Due')); ?> </th>
                                                    <th> <?php echo e(trans('admin.Sale_Date')); ?> </th>
                                                    <th> <?php echo e(trans('admin.Coin')); ?> </th>
                                                    <th> <?php echo e(trans('admin.Cost_Center')); ?> </th>
                                                    <th> <?php echo e(trans('admin.User')); ?> </th>
                                                    <th><?php echo e(trans('admin.Details')); ?></th>
                                                    <th><?php echo e(trans('admin.Address')); ?></th>
                                                    <th><?php echo e(trans('admin.Actions')); ?> </th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php $__currentLoopData = $items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <tr>
                                                    <td><?php echo e($item->Code); ?></td>
                                                    <td><?php echo e($item->Time); ?></td>
                                                    <td><?php echo e($item->Date); ?></td>
                                             
                                                      <td><?php echo e($item->Draw); ?></td>  
                                                    <td>
                                                    
                                                    <?php if($item->Payment_Method == 'Cash'): ?>
                                                       <?php echo e(trans('admin.Cash')); ?> 
                                                    <?php elseif($item->Payment_Method == 'Later'): ?>
                                                       <?php echo e(trans('admin.Later')); ?> 
                                                    <?php elseif($item->Payment_Method == 'Installment'): ?>
           
                                                <button type="button" class="btn btn-default" data-toggle="modal" data-target="#Installment<?php echo e($item->id); ?>"><?php echo e(trans('admin.Installment')); ?> </button>            
                                                    <?php endif; ?>    
                                                    </td>
                                                        <td>
                                                    
                                                         <?php if($item->Delivery_Status == 0): ?>
                                                       <button type="button" class="btn btn-default" data-toggle="modal" data-target="#Delivery_Status<?php echo e($item->id); ?>"><?php echo e(trans('admin.Pending')); ?> </button>
                                                    <?php elseif($item->Status == 1): ?>
                                         <button type="button" class="btn btn-default" data-toggle="modal" data-target="#Delivery_Status<?php echo e($item->id); ?>"><?php echo e(trans('admin.Processing')); ?> </button>                 
                                                    <?php elseif($item->Status == 2): ?>
                                             <button type="button" class="btn btn-default" data-toggle="modal" data-target="#Delivery_Status<?php echo e($item->id); ?>"><?php echo e(trans('admin.Recived')); ?> </button>
                                                    <?php endif; ?>    
                                                    </td>
                                                        <td>
                                                            <?php if(!empty($item->Executor)): ?>    
                                                            <?php echo e($item->Executor()->first()->Name); ?>

                                                    <?php endif; ?>
                                                    </td>
                                                        <td><?php echo e($item->Refernce_Number); ?></td>
                                                        <td><?php echo e($item->Note); ?></td>
                                                   
                                                      <td>
                                              
                                                   <?php echo e($item->Safe()->first()->Name); ?>      
                                                       </td>
                                                        <td>
                                              
                                                 <?php echo e($item->Client()->first()->Name); ?>              
                                                    </td>
                                                        <td>
                                                  <?php if(!empty($item->Delegate)): ?>   
                                            
                                                 <?php echo e($item->Delegate()->first()->Name); ?>              
                                                    <?php endif; ?>
                                                    </td>
                                                        <td>
                                                    
                                               <?php echo e($item->Store()->first()->Name); ?>    
                                                    </td>
                                                           <td><?php echo e($item->Later_Due); ?></td>
                                                    <td><?php echo e($item->Sale_Date); ?></td>
                                                        <td>
                                                    
                                        
                                                    <?php echo e($item->Coin()->first()->Arabic_Name); ?>              
                                                    </td>
                                                            <td>
                                                     <?php if(!empty($item->Cost_Center)): ?>        
                                                      <?php echo e($item->Cost_Center()->first()->Arabic_Name); ?>  
                                                    <?php endif; ?>
                                                    </td>
                                                             <td>
                                     
                                                <?php echo e($item->User()->first()->name); ?>  
                                                    </td>
                                      
                                                    <td>
                                                        <button type="button" class="btn btn-default" data-toggle="modal" data-target="#default-example-modal-center-open<?php echo e($item->id); ?>">
                                                            <?php echo e(trans('admin.Details')); ?>

                                                        </button>
                                                    </td>
                                                     <td>
                                                        <button type="button" class="btn btn-default" data-toggle="modal" data-target="#Address<?php echo e($item->id); ?>">
                                                            <?php echo e(trans('admin.Address')); ?>

                                                        </button>
                                                    </td>            
                                                    
                                                    <td class="text-center">
                                                                      
                        <a href="<?php echo e(url('SalesOrderPrint/'.$item->id)); ?>" class="btn btn-default" >
                                                    <i class="fal fa-print"></i>    
                                                    </a>                                                 
                    
                         <?php if($item->ToSales != 1): ?>  
                      
                           
                                 <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('حذف امر البيع')): ?>                          
            <button type="button" class="btn btn-default" data-toggle="modal" data-target="#default-example-modal-center<?php echo e($item->id); ?>"><i class="fal fa-trash"></i></button>
                                 <?php endif; ?>
                                                        
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تعديل امر البيع')): ?>                               
             <a href="<?php echo e(url('EditSalesOrder/'.$item->id)); ?>" class="btn btn-default" ><i class="fal fa-edit"></i></a>
                            <?php endif; ?>
                                                        
                                                        
                                 <?php if($item->Hold_Qty == 1): ?>   
                                 <?php if($item->Cancel_Order == 0): ?>   
                                                        
                                 <?php if($item->Delegate_Recived == 0): ?>                              
                                      <a href="<?php echo e(url('DelegateRecivedAccept/'.$item->id)); ?>" class="btn btn-default" ><i class="fal fa-handshake"></i></a>     
                                                        
                                       <a href="<?php echo e(url('CancelSalesOrder/'.$item->id)); ?>" class="btn btn-default" ><i class="fal fa-times"></i></a>                         
                                <?php endif; ?>      
                                                        
                                           <?php if($item->Delegate_Recived == 1): ?>   
                                          
                                        (<?php echo e($item->Delegate_Recived_Time); ?>)                    
                                                        
                                                        
                                     <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تحويل امر البيع')): ?>                       
           <a href="<?php echo e(url('TransferToSalesSO/'.$item->id)); ?>" class="btn btn-default" ><i class="fal fa-arrow-left"></i></a>                               
                                        <?php endif; ?>                        
                                                        
                                            <?php endif; ?>            
                                                        
                                                        
                                                        
                                   <?php else: ?>
                                          
                                       
                 
                                    (<?php echo e($item->Cancel_Order_Time); ?>)
                                    <?php echo e(trans('admin.Canceled')); ?>

                                                           
                                 <?php endif; ?>                       
                                                        
                                <?php else: ?>                        
                                      <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تحويل امر البيع')): ?>                       
           <a href="<?php echo e(url('TransferToSalesSO/'.$item->id)); ?>" class="btn btn-default" ><i class="fal fa-arrow-left"></i></a>                               
                                        <?php endif; ?>  
                                                        
                                <?php endif; ?>                        
                                                        
                                                        
                                        <?php endif; ?>
                                                    </td>
                                                </tr>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                
                                            </tbody>
                                            <tfoot>
                                                <tr>
                                                    <th><?php echo e(trans('admin.Code')); ?></th>
                                                    <th><?php echo e(trans('admin.Time')); ?></th>
                                                    <th><?php echo e(trans('admin.Date')); ?></th>
                                                    <th> <?php echo e(trans('admin.Draw')); ?> </th>  
                                                    <th> <?php echo e(trans('admin.Payment_Method')); ?></th>
                                                    <th><?php echo e(trans('admin.Status')); ?> </th>
                                                    <th> <?php echo e(trans('admin.Executor')); ?> </th>
                                                    <th> <?php echo e(trans('admin.Refernce_Number')); ?> </th>
                                                    <th> <?php echo e(trans('admin.Notes')); ?> </th>
                                                    <th> <?php echo e(trans('admin.Safe')); ?> </th>
                                                    <th> <?php echo e(trans('admin.Client')); ?> </th>
                                                    <th> <?php echo e(trans('admin.Delegate')); ?> </th>
                                                    <th> <?php echo e(trans('admin.Store')); ?> </th>
                                                    <th> <?php echo e(trans('admin.Later_Due')); ?> </th>
                                                    <th> <?php echo e(trans('admin.Sale_Date')); ?> </th>
                                                    <th> <?php echo e(trans('admin.Coin')); ?> </th>
                                                    <th> <?php echo e(trans('admin.Cost_Center')); ?> </th>
                                                    <th> <?php echo e(trans('admin.User')); ?> </th>
                                                    <th><?php echo e(trans('admin.Details')); ?></th>
                                                    <th><?php echo e(trans('admin.Address')); ?></th>
                                                    <th><?php echo e(trans('admin.Actions')); ?> </th>
                                                </tr>
                                            </tfoot>
                        
                                        </table>
                                       
                                        <?php echo e($items->Links()); ?>

                                        </div>
                                        <!-- datatable end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </main>

  <?php $__currentLoopData = $items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

                  <!-- Modal Delete -->
    <div class="modal fade" id="default-example-modal-center<?php echo e($item->id); ?>" tabindex="-1" role="dialog" aria-hidden="true">
                        <div class="modal-dialog modal-dialog-centered" role="document">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h4 class="modal-title">
                                         <?php echo e(trans('admin.RUSWDT')); ?> <strong><?php echo e($item->Code); ?></strong>
                                    </h4>
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                        <span aria-hidden="true"><i class="fal fa-times"></i></span>
                                    </button>
                                </div>
                                
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-dismiss="modal"><?php echo e(trans('admin.No')); ?></button>
                                    <a href="<?php echo e(url('DeleteSalesOrder/'.$item->id)); ?>" class="btn btn-primary"><?php echo e(trans('admin.Yes')); ?></a>
                                </div>
                            </div>
                        </div>
                    </div>


                        <!-- Modal Details -->
    <div class="modal fade" id="default-example-modal-center-open<?php echo e($item->id); ?>" tabindex="-1" role="dialog"
                aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered modal-xl" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h4 class="modal-title">
                               <?php echo e(trans('admin.Details')); ?> 
                            </h4>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true"><i class="fal fa-times"></i></span>
                            </button>
                        </div>
                        <div class="modal-body">
     
                            
                <?php
                $details=ProductSalesOrder::where('SalesOrder',$item->id)->get();                         
                ?>            
                        <div class="mt-3">
                             <div style="overflow:auto">
                            <table id=""
                            class="table table-bordered table-hover table-striped" style="width:160%">
                            <thead>
                                <tr>
                                    <th><?php echo e(trans('admin.Code')); ?> </th>
                                                <?php if($show->Patch_Number == 1): ?>  
                                            <th><?php echo e(trans('admin.Patch_Number')); ?></th>
                                            <?php endif; ?> 
                                    <th><?php echo e(trans('admin.Name')); ?> </th>
                                            <th><?php echo e(trans('admin.Group')); ?> </th>
                                    <th><?php echo e(trans('admin.Brand')); ?> </th>
                                     <th><?php echo e(trans('admin.Unit')); ?> </th>
                                    <th><?php echo e(trans('admin.AvQty')); ?> </th>
                                    <th><?php echo e(trans('admin.Qty')); ?> </th>
                                    <th><?php echo e(trans('admin.Price')); ?> </th>
                                    <th><?php echo e(trans('admin.Discount')); ?> </th>
                                    <th><?php echo e(trans('admin.Total_Bf_Tax')); ?> </th>
                                    <th><?php echo e(trans('admin.Total_Tax')); ?> </th>
                                    <th><?php echo e(trans('admin.Total')); ?> </th>
                                    <th><?php echo e(trans('admin.Store')); ?> </th>
                                    <th><?php echo e(trans('admin.Exp_Date')); ?> </th>
                                </tr>
                            </thead>
                            <tbody id="">
                                <?php $__currentLoopData = $details; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $detail): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td><?php echo e($detail->Product_Code); ?></td>
                                                     <?php if($show->Patch_Number == 1): ?>  
                                         <td><?php echo e($detail->Patch_Number); ?></td>
                                            <?php endif; ?> 
                                    <td>
                                        <?php echo e($detail->Product()->first()->P_Ar_Name); ?>

                                    
                                <?php if(!empty($detail->V1)): ?>    (<?php echo e($detail->V1()->first()->Name); ?>)  <?php endif; ?> 
                                  <?php if(!empty($detail->V2)): ?>    (<?php echo e($detail->V2()->first()->Name); ?>)  <?php endif; ?>          
                                    </td>
                                                <td>
                                       <?php echo e($detail->Product()->first()->Group()->first()->Name); ?>

                                    </td>
                                         <td>
                                            <?php if(!empty($detail->Product()->first()->Brand)): ?> 
                                       <?php echo e($detail->Product()->first()->Brand()->first()->Name); ?>

                                            <?php endif; ?> 
                                    </td>
                                    <td><?php echo e($detail->Unit()->first()->Name); ?></td>
                                    <td><?php echo e($detail->AvQty); ?></td>
                                    <td><?php echo e($detail->Qty); ?></td>
                                    <td><?php echo e($detail->Price); ?></td>
                                    <td><?php echo e($detail->Discount); ?></td>
                                    <td><?php echo e($detail->Total_Bf_Tax); ?></td>
                                    <td><?php echo e($detail->Total_Tax); ?></td>
                                    <td><?php echo e($detail->Total); ?></td>
                                    <td><?php echo e($detail->Store()->first()->Name); ?></td>
                                    <td><?php echo e($detail->Exp_Date); ?></td>

                                </tr>
                                 <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                            <tfoot>
                                <tr>
                                  <th><?php echo e(trans('admin.Code')); ?> </th>
                                                <?php if($show->Patch_Number == 1): ?>  
                                            <th><?php echo e(trans('admin.Patch_Number')); ?></th>
                                            <?php endif; ?> 
                                    <th><?php echo e(trans('admin.Name')); ?> </th>
                                            <th><?php echo e(trans('admin.Group')); ?> </th>
                                    <th><?php echo e(trans('admin.Brand')); ?> </th>
                                     <th><?php echo e(trans('admin.Unit')); ?> </th>
                                    <th><?php echo e(trans('admin.AvQty')); ?> </th>
                                    <th><?php echo e(trans('admin.Qty')); ?> </th>
                                    <th><?php echo e(trans('admin.Price')); ?> </th>
                                    <th><?php echo e(trans('admin.Discount')); ?> </th>
                                    <th><?php echo e(trans('admin.Total_Bf_Tax')); ?> </th>
                                    <th><?php echo e(trans('admin.Total_Tax')); ?> </th>
                                    <th><?php echo e(trans('admin.Total')); ?> </th>
                                    <th><?php echo e(trans('admin.Store')); ?> </th>
                                    <th><?php echo e(trans('admin.Exp_Date')); ?> </th>
                                </tr>
                            </tfoot>
                        </table>
                        </div>
                         <div style="overflow:auto">
                        <table class="table table-bordered table-hover table-striped mt-4" style="width:140%">
                            <tbody>
                                <tr>
                                    <td><?php echo e(trans('admin.Product_Numbers')); ?></td>
                                    <td><?php echo e($item->Product_Numbers); ?></td>
                                    
                                    <td><?php echo e(trans('admin.Total_Qty')); ?></td>
                                    <td><?php echo e($item->Total_Qty); ?></td>
                                    
                                    <td><?php echo e(trans('admin.Total_Discount')); ?></td>
                                    <td><?php echo e($item->Total_Discount); ?></td>
                                    
                                     <td><?php echo e(trans('admin.Total_Bf_Taxes')); ?></td>
                                    <td><?php echo e($item->Total_BF_Taxes); ?></td>
                                    
                                    <td><?php echo e(trans('admin.Total_Taxes')); ?></td>
                                    <td><?php echo e($item->Total_Taxes); ?></td>
                                    
                                        <td><?php echo e(trans('admin.CuponCode')); ?></td>
                                    <td><?php echo e($item->CuponCode); ?></td>
                                    
                                    
                                        <td><?php echo e(trans('admin.Shipping')); ?></td>
                                    <td><?php echo e($item->Shipping); ?></td>
                                    
                                    
                                    
                                    <td><?php echo e(trans('admin.Total_Price')); ?></td>
                                    <td><?php echo e($item->Total_Price); ?></td>
                                    
                                    <td><?php echo e(trans('admin.Paid')); ?></td>
                                    <td><?php echo e($item->Pay); ?></td> 
                            
                                </tr>
                            </tbody>
                        </table>
                        </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal"><?php echo e(trans('admin.Close')); ?></button>
                            <a class="btn btn-primary" href="#" onclick="window.print()"> <i class="fal fa-print"></i> <?php echo e(trans('admin.Print')); ?></a>
                        </div>
                    </div>
                </div>
            </div>
             
            </div>

                                <!-- Modal Address -->
    <div class="modal fade" id="Address<?php echo e($item->id); ?>" tabindex="-1" role="dialog"
                aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered modal-xl" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h4 class="modal-title">
                               <?php echo e(trans('admin.Address')); ?> 
                            </h4>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true"><i class="fal fa-times"></i></span>
                            </button>
                        </div>
                        <div class="modal-body">
             
                        <div class="mt-3">
                             <div style="overflow:auto">
                            <table id=""
                            class="table table-bordered table-hover table-striped" style="width:160%">
                            <thead>
                                <tr>
                                    <th><?php echo e(trans('admin.Name')); ?> </th>
                                    <th><?php echo e(trans('admin.Email')); ?> </th>
                                    <th><?php echo e(trans('admin.Phone')); ?> </th>
                                    <th><?php echo e(trans('admin.Other_Phone')); ?> </th>
                                    <th><?php echo e(trans('admin.Address_Name')); ?> </th>
                                    <th><?php echo e(trans('admin.Special_Mark')); ?> </th>
                                    <th><?php echo e(trans('admin.Street')); ?> </th>
                                    <th><?php echo e(trans('admin.Buliding')); ?> </th>
                                    <th><?php echo e(trans('admin.Floor')); ?> </th>
                                    <th><?php echo e(trans('admin.Flat')); ?> </th>
                                    <th><?php echo e(trans('admin.Governrate')); ?> </th>
                                    <th><?php echo e(trans('admin.City')); ?> </th>
                                    <th><?php echo e(trans('admin.Place')); ?> </th>
                                    <th><?php echo e(trans('admin.Location')); ?> </th>
                                    <th><?php echo e(trans('admin.Address_Details')); ?> </th>

                                </tr>
                            </thead>
                            <tbody id="">
                      <tr>
                        <td><?php echo e($item->Name); ?></td>  
                        <td><?php echo e($item->Email); ?></td>  
                        <td><?php echo e($item->Phone); ?></td>  
                        <td><?php echo e($item->OtherPhone); ?></td>  
                        <td><?php echo e($item->Address_Name); ?></td>  
                        <td><?php echo e($item->Special_MarkAdd); ?></td>  
                        <td><?php echo e($item->StreetAdd); ?></td>  
                        <td><?php echo e($item->BulidingAdd); ?></td>  
                        <td><?php echo e($item->FloorAdd); ?></td>  
                        <td><?php echo e($item->FlatAdd); ?></td>  
                        <td>
                            <?php if(!empty($item->Governrate)): ?>
                            <?php echo e($item->Governrate()->first()->Arabic_Name); ?>

                            <?php endif; ?>
                          </td>  
                        <td>
                             <?php if(!empty($item->City)): ?>
                            <?php echo e($item->City()->first()->Arabic_Name); ?>

                            <?php endif; ?>
                               </td>  
                        <td>
                             <?php if(!empty($item->Place)): ?>
                            <?php echo e($item->Place()->first()->Arabic_Name); ?>

                            <?php endif; ?>
                      
                          </td>  
                        <td><?php echo e($item->LocationAdd); ?></td>  
                        <td><?php echo e($item->Address_DetailsAdd); ?></td>  
                      </tr>
                            </tbody>
                        </table>
                        </div>

                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal"><?php echo e(trans('admin.Close')); ?></button>
                        </div>
                    </div>
                </div>
            </div>
             
            </div>
     

                             <!-- Modal Delivery_Status -->
    <div class="modal fade" id="Delivery_Status<?php echo e($item->id); ?>" tabindex="-1" role="dialog"
                aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered modal-xl" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h4 class="modal-title">
                               <?php echo e(trans('admin.Status')); ?> 
                            </h4>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true"><i class="fal fa-times"></i></span>
                            </button>
                        </div>
                        <form action="<?php echo e(url('ChangeStatusShop')); ?>" method="post">
                            <?php echo csrf_field(); ?>
                       
                        <div class="modal-body">
             <input type="hidden" name="ID" value="<?php echo e($item->id); ?>">
                            <select class="select2 form-control" name="Delivery_Status" required>
            <option value="0" <?php if($item->Delivery_Status == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Pending')); ?> </option>      
            <option value="1" <?php if($item->Delivery_Status == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Processing')); ?></option>      
            <option value="2" <?php if($item->Delivery_Status == 2): ?> selected <?php endif; ?>><?php echo e(trans('admin.Recived')); ?></option>      
                            </select>
                      
                        <div class="modal-footer">
                 <button type="submit" class="btn btn-secondary"><?php echo e(trans('admin.Submit')); ?></button>
                 <button type="button" class="btn btn-secondary" data-dismiss="modal"><?php echo e(trans('admin.Close')); ?></button>
                        </div>
                    </div>
                         </form>    
                </div>
            </div>
             
            </div>
                          
                                     


               <!-- Modal Installment -->
    <div class="modal fade" id="Installment<?php echo e($item->id); ?>" tabindex="-1" role="dialog" aria-hidden="true">
                        <div class="modal-dialog modal-dialog-centered" role="document">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h4 class="modal-title">
                         
                                    <div class="row">
                                                <div class="col-md-3">
                                            <label><?php echo e(trans('admin.Presenter')); ?></label>     
                                                <span><?php echo e($item->presenter); ?></span>    
                                                    </div>
                                                    
                                                          <div class="col-md-3">
                                            <label><?php echo e(trans('admin.annual_interest')); ?></label>     
                                                <span><?php echo e($item->annual_interest); ?></span>    
                                                    </div>  
                                                    
                                                          <div class="col-md-3">
                                            <label><?php echo e(trans('admin.monthly_installment')); ?></label>     
                                                <span><?php echo e($item->monthly_installment); ?></span>    
                                                    </div>  
                                                    
                                                          <div class="col-md-3">
                                            <label><?php echo e(trans('admin.Years_Number')); ?></label>     
                                                <span><?php echo e($item->Years_Number); ?></span>    
                                                    </div>  
                                                    
                                                          <div class="col-md-3">
                                            <label><?php echo e(trans('admin.total')); ?></label>     
                                                <span><?php echo e($item->total); ?></span>    
                                                    </div>  
                                                    
                                                          <div class="col-md-3">
                                            <label><?php echo e(trans('admin.installment_Num')); ?></label>     
                                                <span><?php echo e($item->installment_Num); ?></span>    
                                                    </div>  
                                                    
                                                          <div class="col-md-3">
                                            <label><?php echo e(trans('admin.Date_First_installment')); ?></label>     
                                                <span><?php echo e($item->Date_First_installment); ?></span>    
                                                    </div>  
                                                        
                                                        </div>      
                                        
                                        
                                        
                                    </h4>
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                        <span aria-hidden="true"><i class="fal fa-times"></i></span>
                                    </button>
                                </div>
                                
                                <div class="modal-footer">
                         <button type="button" class="btn btn-secondary" data-dismiss="modal"><?php echo e(trans('admin.No')); ?></button>
                                </div>
                            </div>
                        </div>
                    </div>

<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>



<?php $__env->stopSection(); ?>


<?php $__env->startPush('js'); ?>
    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/datagrid/datatables/datatables.bundle.css')); ?>">
    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/formplugins/summernote/summernote.css')); ?>">
    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/formplugins/select2/select2.bundle.css')); ?>">


    <style>
        th{
            width:135px!important;
        }
    </style>

 <script src="<?php echo e(asset('Admin/js/datagrid/datatables/datatables.export.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/datagrid/datatables/datatables.bundle.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/formplugins/summernote/summernote.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/formplugins/select2/select2.bundle.js')); ?>"></script>
    <script>
        //_fnFeatureHtmlLength();
        $(document).ready(function () {
            // Setup - add a text input to each footer cell
            $('#dt-basic-example thead tr').clone(true).appendTo('#dt-basic-example thead');
            $('#dt-basic-example thead tr:eq(1) th').each(function (i) {
                var title = $(this).text();
                $(this).html('<input type="text" class="form-control form-control-sm" placeholder="Search '  + '" />');

                $('input', this).on('keyup change', function () {
                    if (table.column(i).search() !== this.value) {
                        table
                            .column(i)
                            .search(this.value)
                            .draw();
                    }
                });
            });
            var table = $('#dt-basic-example').DataTable(
                {
                    // responsive: true,
                    orderCellsTop: true,
                    fixedHeader: true,
                    lengthChange: true,

                    dom: "<'row mb-3'<'col-sm-12 col-md-3 d-flex align-items-center justify-content-start'f><'col-sm-12 col-md-9 d-flex align-items-center justify-content-end'B>>" +
                        "<'row'<'col-sm-12'tr>>" +
                        "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",

                    buttons: [
                        {
                            extend: 'pageLength',
                            className: 'btn-outline-default'
                        },
                        {
                            extend: 'colvis',
                            text: 'Column Visibility',
                            titleAttr: 'Col visibility',
                            className: 'btn-outline-default'
                        },
                        {
                            extend: 'pdfHtml5',
                            text: 'PDF',
                            titleAttr: 'Generate PDF',
                            className: 'btn-outline-danger btn-sm mr-1'
                        },
                        {
                            extend: 'excelHtml5',
                            text: 'Excel',
                            titleAttr: 'Generate Excel',
                            className: 'btn-outline-success btn-sm mr-1'
                        },
                        {
                            extend: 'csvHtml5',
                            text: 'CSV',
                            titleAttr: 'Generate CSV',
                            className: 'btn-outline-primary btn-sm mr-1'
                        },
                        {
                            extend: 'copyHtml5',
                            text: 'Copy',
                            titleAttr: 'Copy to clipboard',
                            className: 'btn-outline-primary btn-sm mr-1'
                        },
                        {
                            extend: 'print',
                            text: 'Print',
                            titleAttr: 'Print Table',
                            className: 'btn-outline-primary btn-sm'
                        }
                    ],
                });
            $('.js-thead-colors a').on('click', function () {
                var theadColor = $(this).attr("data-bg");
                console.log(theadColor);
                $('#dt-basic-example thead').removeClassPrefix('bg-').addClass(theadColor);
            });

            $('.js-tbody-colors a').on('click', function () {
                var theadColor = $(this).attr("data-bg");
                console.log(theadColor);
                $('#dt-basic-example').removeClassPrefix('bg-').addClass(theadColor);
            });

        });

    </script>
  
 
<script>
    var autoSave = $('#autoSave');
    var interval;
    var timer = function()
    {
        interval = setInterval(function()
        {
            //start slide...
            if (autoSave.prop('checked'))
                saveToLocal();

            clearInterval(interval);
        }, 3000);
    };

    //save
    var saveToLocal = function()
    {
        localStorage.setItem('summernoteData', $('#saveToLocal').summernote("code"));
        console.log("saved");
    }

    //delete 
    var removeFromLocal = function()
    {
        localStorage.removeItem("summernoteData");
        $('#saveToLocal').summernote('reset');
    }

    $(document).ready(function()
    {
        //init default
        $('.js-summernote').summernote(
        {
            height: 200,
            tabsize: 2,
            placeholder: "Type here...",
            dialogsFade: true,
            toolbar: [
                ['style', ['style']],
                ['font', ['strikethrough', 'superscript', 'subscript']],
                ['font', ['bold', 'italic', 'underline', 'clear']],
                ['fontsize', ['fontsize']],
                ['fontname', ['fontname']],
                ['color', ['color']],
                ['para', ['ul', 'ol', 'paragraph']],
                ['height', ['height']]
                ['table', ['table']],
                ['insert', ['link', 'picture', 'video']],
                ['view', ['fullscreen', 'codeview', 'help']]
            ],
            callbacks:
            {
                //restore from localStorage
                onInit: function(e)
                {
                    $('.js-summernote').summernote("code", localStorage.getItem("summernoteData"));
                },
                onChange: function(contents, $editable)
                {
                    clearInterval(interval);
                    timer();
                }
            }
        });

        //load emojis
        $.ajax(
        {
            url: 'https://api.github.com/emojis',
            async: false
        }).then(function(data)
        {
            window.emojis = Object.keys(data);
            window.emojiUrls = data;
        });

        //init emoji example
        $(".js-hint2emoji").summernote(
        {
            height: 100,
            toolbar: false,
            placeholder: 'type starting with : and any alphabet',
            hint:
            {
                match: /:([\-+\w]+)$/,
                search: function(keyword, callback)
                {
                    callback($.grep(emojis, function(item)
                    {
                        return item.indexOf(keyword) === 0;
                    }));
                },
                template: function(item)
                {
                    var content = emojiUrls[item];
                    return '<img src="' + content + '" width="20" /> :' + item + ':';
                },
                content: function(item)
                {
                    var url = emojiUrls[item];
                    if (url)
                    {
                        return $('<img />').attr('src', url).css('width', 20)[0];
                    }
                    return '';
                }
            }
        });

        //init mentions example
        $(".js-hint2mention").summernote(
        {
            height: 100,
            toolbar: false,
            placeholder: "type starting with @",
            hint:
            {
                mentions: ['jayden', 'sam', 'alvin', 'david'],
                match: /\B@(\w*)$/,
                search: function(keyword, callback)
                {
                    callback($.grep(this.mentions, function(item)
                    {
                        return item.indexOf(keyword) == 0;
                    }));
                },
                content: function(item)
                {
                    return '@' + item;
                }
            }
        });

    });

</script>
<script type="text/javascript">


    $(".show-table").click(function(){
        $(".hide-table").show();
    });
    
</script>

	<style>
	@media  print {
		body * {
			visibility: hidden;
		}
		.modal-content * {
			visibility: visible;
			overflow: visible;
		}
		.main-page * {
			display: none;
		}
		.modal {
			position: absolute;
			left: 0;
			top: -180px;
			margin: 0;
			padding: 0;
			min-height: 550px;
			visibility: visible;
			overflow: visible !important; /* Remove scrollbar for printing. */
		}
		.modal-dialog {
			visibility: visible !important;
			overflow: visible !important; /* Remove scrollbar for printing. */
		}
		
		.page-content{
		    display:none;
		}
         @page  {
		
		size: a4;
       
    }

	}
	</style>

    <!-- Search Selecet -->
<script>
    $(document).ready(function () {
        $(function () {
            $(".select2").select2();

            $(".select2-placeholder-multiple").select2({
                placeholder: "Select State",
            });
            $(".js-hide-search").select2({
                minimumResultsForSearch: 1 / 0,
            });
            $(".js-max-length").select2({
                maximumSelectionLength: 2,
                placeholder: "Select maximum 2 items",
            });
            $(".select2-placeholder").select2({
                placeholder: "Select a state",
                allowClear: true,
            });

            $(".js-select2-icons").select2({
                minimumResultsForSearch: 1 / 0,
                templateResult: icon,
                templateSelection: icon,
                escapeMarkup: function (elm) {
                    return elm;
                },
            });

            function icon(elm) {
                elm.element;
                return elm.id ? "<i class='" + $(elm.element).data("icon") + " mr-2'></i>" + elm.text : elm.text;
            }

            $("#Emp").select2({
                placeholder: "select...",
                ajax: {
                    type: "GET",
                    dataType: "json",
                    url: "AllEmps",
                    processResults: function (data) {
                        return {
                            results: $.map(data, function (obj, index) {
                                return { id: index, text: obj };
                            }),
                        };

                        console.log(data);
                    },
                    data: function (params) {
                        var query = {
                            search: params.term,
                        };
                        if (params.term == "*") query.items = [];
                        return { json: JSON.stringify(query) };
                    },
                },
            });

            $("#Emp").on("select2:select", function (e) {
                console.log("select done", e.params.data);
            });
        });
    });
</script>



<?php $__env->stopPush(); ?>
<?php echo $__env->make('admin.index', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp2\htdocs\erp\resources\views/admin/Website/ShopOrders.blade.php ENDPATH**/ ?>