<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Stancl\Tenancy\Database\Concerns\CentralConnection;

class Modules extends Model
{
    use HasFactory, CentralConnection;
      protected $table = 'modules';
      protected $fillable = [
        'Capital',
        'Accounts',
        'Stores',
        'CRM',
        'HR',
        'Manufacturing',
        'Maintenance',
        'Secretariat',
        'Petrol',
        'ECommerce',
        'Shipping',
        'Bill_Electronic',
        'Hotels',
        'Resturant',
        'Traning_Center',


    ];
}
