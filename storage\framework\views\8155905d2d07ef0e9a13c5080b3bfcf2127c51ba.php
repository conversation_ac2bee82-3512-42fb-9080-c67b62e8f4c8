<?php $__env->startSection('content'); ?>

  <title><?php echo e(trans('admin.ContactUS')); ?></title>
     <main id="js-page-content" role="main" class="page-content">
                    <ol class="breadcrumb page-breadcrumb">
                        <li class="breadcrumb-item"><a href="javascript:void(0);"><?php echo e(trans('admin.Website')); ?></a></li>
                      
                        <li class="breadcrumb-item active"> <?php echo e(trans('admin.ContactUS')); ?>   </li>
                        <li class="position-absolute pos-top pos-right d-none d-sm-block"><span
                                class="js-get-date"></span></li>
                    </ol>
                    
                    <!-- data entry -->
                    <div class="row">
                        <div class="col-lg-12">
                            <div id="panel-2" class="panel">
                                <div class="panel-hdr">

                                </div>
                                <div class="panel-container show">
                                  <span id="ex"> <?php echo $__env->make('admin.layouts.messages', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>     
                                    <div class="panel-content">
                                        <ul class="nav nav-tabs" role="tablist">
                                            <li class="nav-item">
                                                <a class="nav-link active" data-toggle="tab" href="#tab_borders_icons-8" role="tab">   <?php echo e(trans('admin.ContactUS')); ?>  </a>
                                            </li>
                                        </ul>
                                        <div class="tab-content border border-top-0 p-3">
                           <div class="tab-pane fade show active" id="tab_borders_icons-8" role="tabpanel">
                                                                          
     <form action="<?php echo e(url('ContactUSUpdate/'.$item->id)); ?>" method="post" enctype="multipart/form-data" class="form-row">
                                  <?php echo csrf_field(); ?>

                   <?php echo view('honeypot::honeypotFormFields'); ?>
 

                                                    <div class="col-md-12">
                                                        <div class="data-def">
                                                            <div class="form-row">
                                 
                              
                                                                
                                                                                    <div class="form-group col-md-4">
                                          <label class="form-label" for=""> <?php echo e(trans('admin.Arabic_Title')); ?>   </label>
                   <input type="text" name="Arabic_Title" class="form-control" value="<?php echo e($item->Arabic_Title); ?>">        
                                                                </div>
                                                                
                                                                                    <div class="form-group col-md-4">
                                          <label class="form-label" for=""> <?php echo e(trans('admin.English_Title')); ?>   </label>
                   <input type="text" name="English_Title" class="form-control" value="<?php echo e($item->English_Title); ?>">        
                                                                </div>
                                                                
                                                                
                                                                                    <div class="form-group col-md-4">
                                          <label class="form-label" for=""> <?php echo e(trans('admin.Arabic_Desc')); ?>   </label>
                   <input type="text" name="Arabic_Desc" class="form-control" value="<?php echo e($item->Arabic_Desc); ?>">        
                                                                </div>
                                                                
                                                                                    <div class="form-group col-md-4">
                                          <label class="form-label" for=""> <?php echo e(trans('admin.English_Desc')); ?>   </label>
                   <input type="text" name="English_Desc" class="form-control" value="<?php echo e($item->English_Desc); ?>">        
                                                                </div>
                                                                
                                                                                    <div class="form-group col-md-4">
                                          <label class="form-label" for=""> <?php echo e(trans('admin.Opening_Hours')); ?>   </label>
                   <input type="text" name="Opening_Hours" class="form-control" value="<?php echo e($item->Opening_Hours); ?>">        
                                                                </div>
                                                                
                                     <div class="form-group col-md-4">
                                          <label class="form-label" for=""> <?php echo e(trans('admin.Phone1')); ?>   </label>
                   <input type="text" name="Phone1" class="form-control" value="<?php echo e($item->Phone1); ?>">        
                                                                </div>
                                                                                    <div class="form-group col-md-4">
                                          <label class="form-label" for=""> <?php echo e(trans('admin.Phone2')); ?>   </label>
                   <input type="text" name="Phone2" class="form-control" value="<?php echo e($item->Phone2); ?>">        
                                                                </div>
                                                                
                                                                                    <div class="form-group col-md-4">
                                          <label class="form-label" for=""> <?php echo e(trans('admin.Phone_Header')); ?>   </label>
                   <input type="text" name="Phone_Header" class="form-control" value="<?php echo e($item->Phone_Header); ?>">        
                                                                </div>
                                                                
                                                                
                                                                                    <div class="form-group col-md-4">
                                          <label class="form-label" for=""> <?php echo e(trans('admin.Email')); ?>   </label>
                   <input type="text" name="Email" class="form-control" value="<?php echo e($item->Email); ?>">        
                                                                </div>
                                                                
                                                                                    <div class="form-group col-md-4">
                                          <label class="form-label" for=""> <?php echo e(trans('admin.Arabic_Address')); ?>   </label>
                   <input type="text" name="Arabic_Address" class="form-control" value="<?php echo e($item->Arabic_Address); ?>">        
                                                                </div>
                                                                
                                
                                                                
                                                            <div class="form-group col-md-4">
                                          <label class="form-label" for=""> <?php echo e(trans('admin.English_Address')); ?>   </label>
                   <input type="text" name="English_Address" class="form-control" value="<?php echo e($item->English_Address); ?>">        
                                                                </div>                        
                   
                   
                                          <div class="form-group col-md-12">
                                          <label class="form-label" for=""> <?php echo e(trans('admin.Map')); ?>   </label>
                   <input type="text" name="Map" class="form-control" value="<?php echo e($item->Map); ?>">        
                                                                </div>
                                                                
                                                                
                                                <div class="form-group col-md-12">
           
                         <?php echo $item->Map; ?>                      
                                     
                                                                </div>        
                                                                
                                                       <hr>    
                                            
       
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-12">
                                <button type="submit" class="btn btn-primary mt-2"><?php echo e(trans('admin.Save')); ?></button>
                                                    </div>
                                                </form>
 
                                            </div>
                                        </div>
                                      
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                 
                </main>
 
<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
  <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/datagrid/datatables/datatables.bundle.css')); ?>">
    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/formplugins/summernote/summernote.css')); ?>">
    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/formplugins/select2/select2.bundle.css')); ?>">

 <script src="<?php echo e(asset('Admin/js/datagrid/datatables/datatables.bundle.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/datagrid/datatables/datatables.export.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/formplugins/summernote/summernote.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/formplugins/select2/select2.bundle.js')); ?>"></script>
    <script>
        //_fnFeatureHtmlLength();
        $(document).ready(function () {
            // Setup - add a text input to each footer cell
            $('#dt-basic-example thead tr').clone(true).appendTo('#dt-basic-example thead');
            $('#dt-basic-example thead tr:eq(1) th').each(function (i) {
                var title = $(this).text();
                $(this).html('<input type="text" class="form-control form-control-sm" placeholder="Search ' + title + '" />');

                $('input', this).on('keyup change', function () {
                    if (table.column(i).search() !== this.value) {
                        table
                            .column(i)
                            .search(this.value)
                            .draw();
                    }
                });
            });
            var table = $('#dt-basic-example').DataTable(
                {
                    responsive: true,
                    orderCellsTop: true,
                    fixedHeader: true,
                    lengthChange: true,

                    dom: "<'row mb-3'<'col-sm-12 col-md-3 d-flex align-items-center justify-content-start'f><'col-sm-12 col-md-9 d-flex align-items-center justify-content-end'B>>" +
                        "<'row'<'col-sm-12'tr>>" +
                        "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",

                    buttons: [
                        {
                            extend: 'pageLength',
                            className: 'btn-outline-default'
                        },
                        {
                            extend: 'colvis',
                            text: 'Column Visibility',
                            titleAttr: 'Col visibility',
                            className: 'btn-outline-default'
                        },
                        {
                            extend: 'pdfHtml5',
                            text: 'PDF',
                            titleAttr: 'Generate PDF',
                            className: 'btn-outline-danger btn-sm mr-1'
                        },
                        {
                            extend: 'excelHtml5',
                            text: 'Excel',
                            titleAttr: 'Generate Excel',
                            className: 'btn-outline-success btn-sm mr-1'
                        },
                        {
                            extend: 'csvHtml5',
                            text: 'CSV',
                            titleAttr: 'Generate CSV',
                            className: 'btn-outline-primary btn-sm mr-1'
                        },
                        {
                            extend: 'copyHtml5',
                            text: 'Copy',
                            titleAttr: 'Copy to clipboard',
                            className: 'btn-outline-primary btn-sm mr-1'
                        },
                        {
                            extend: 'print',
                            text: 'Print',
                            titleAttr: 'Print Table',
                            className: 'btn-outline-primary btn-sm'
                        }
                    ],
                });
            $('.js-thead-colors a').on('click', function () {
                var theadColor = $(this).attr("data-bg");
                console.log(theadColor);
                $('#dt-basic-example thead').removeClassPrefix('bg-').addClass(theadColor);
            });

            $('.js-tbody-colors a').on('click', function () {
                var theadColor = $(this).attr("data-bg");
                console.log(theadColor);
                $('#dt-basic-example').removeClassPrefix('bg-').addClass(theadColor);
            });

        });

    </script>


<?php $__env->stopPush(); ?>


<?php echo $__env->make('admin.index', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp2\htdocs\erp\resources\views/admin/Website/ContactUS.blade.php ENDPATH**/ ?>