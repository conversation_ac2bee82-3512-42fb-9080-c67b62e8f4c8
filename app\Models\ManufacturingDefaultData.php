<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Stancl\Tenancy\Database\Concerns\CentralConnection;

class ManufacturingDefaultData extends Model
{
    use HasFactory, CentralConnection;
           protected $table = 'manufacturing_default_data';
      protected $fillable = [
        'Coin',
        'Draw',
        'Hall',
        'Manu_Type',
        'Executing_Qty',
    ];

         public function Hall()
    {
        return $this->belongsTo(ManufacturingHalls::class,'Hall');
    }
      public function Coin()
    {
        return $this->belongsTo(Coins::class,'Coin');
    }
}
