<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Stancl\Tenancy\Database\Concerns\CentralConnection;

class ModuleSettingsNum extends Model
{
    use HasFactory, CentralConnection;
      protected $table = 'module_settings_nums';
      protected $fillable = [
        'Branch_Select',
        'Branch_Num',
        'Store_Select',
        'Store_Num',
        'Users_Select',
        'Users_Num',
        'System',
        'Expire_Date',
        'Type',
        'Price',

    ];
}
