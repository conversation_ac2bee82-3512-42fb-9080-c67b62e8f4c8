<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMainEComDesignsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('main_e_com_designs', function (Blueprint $table) {
            $table->id();
            
            // Design configuration
            $table->string('Header_Color')->default('#ffffff')->nullable();
            $table->string('Footer_Color')->default('#000000')->nullable();
            $table->string('Primary_Color')->default('#007bff')->nullable();
            $table->string('Secondary_Color')->default('#6c757d')->nullable();
            $table->string('Text_Color')->default('#000000')->nullable();
            $table->string('Background_Color')->default('#ffffff')->nullable();
            $table->string('Button_Color')->default('#007bff')->nullable();
            $table->string('Link_Color')->default('#007bff')->nullable();
            $table->text('Custom_CSS')->nullable();
            $table->text('Custom_JS')->nullable();
            $table->string('Font_Family')->default('Arial')->nullable();
            $table->string('Layout_Type')->default('default')->nullable();
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('main_e_com_designs');
    }
}
