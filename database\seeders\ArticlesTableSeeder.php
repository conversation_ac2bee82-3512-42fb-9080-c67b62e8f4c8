<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class ArticlesTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('articles')->delete();
        
        \DB::table('articles')->insert(array (
            0 => 
            array (
                'id' => 1,
                'Image' => 'ArticlesImages/Tc4BdtjP1euOSuW9OmoC.jpeg',
                'Sub_Image' => 'ArticlesImages/8pWQWSpnZFPjdliv8uuk.jpeg',
                'Date' => 'May 02, 2017',
                'Arabic_Title' => 'It\'s all about how you wear',
                'English_Title' => 'It\'s all about how you wear',
                'Arabic_Desc' => 'On sait depuis longtemps que travailler avec du texte lisible et contenant du sens est source de distractions, et empêche de se concentrer sur la mise en page elle-même. L\'avantage du Lorem Ipsum sur un texte générique comme \'Du texte. Du texte. Du texte.\' est qu\'il possède une distribution de lettres plus ou moins normale, et en tout cas comparable avec celle du français standard. De nombreuses suites logicielles de.

Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.

Sample Text Listing

Donec et lacus mattis ipsum feugiat interdum non id sapien.

Quisque et mauris eget nisi vestibulum rhoncus molestie a ante.

Curabitur pulvinar ex at tempus sodales.

Mauris efficitur magna quis lectus lobortis venenatis.

Nunc id enim eget augue molestie lobortis in a purus.

Donec maximus quam at lectus bibendum, non suscipit nunc tristique.

The standard chunk of Lorem Ipsum used since the 1500s is reproduced below for those interested. Sections 1.10.32 and 1.10.33 from "de Finibus Bonorum et Malorum" by Cicero are also reproduced in their exact original form, accompanied by English versions from the 1914 translation by H. Rackham.',
                'English_Desc' => 'On sait depuis longtemps que travailler avec du texte lisible et contenant du sens est source de distractions, et empêche de se concentrer sur la mise en page elle-même. L\'avantage du Lorem Ipsum sur un texte générique comme \'Du texte. Du texte. Du texte.\' est qu\'il possède une distribution de lettres plus ou moins normale, et en tout cas comparable avec celle du français standard. De nombreuses suites logicielles de.

Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.

Sample Text Listing

Donec et lacus mattis ipsum feugiat interdum non id sapien.

Quisque et mauris eget nisi vestibulum rhoncus molestie a ante.

Curabitur pulvinar ex at tempus sodales.

Mauris efficitur magna quis lectus lobortis venenatis.

Nunc id enim eget augue molestie lobortis in a purus.

Donec maximus quam at lectus bibendum, non suscipit nunc tristique.

The standard chunk of Lorem Ipsum used since the 1500s is reproduced below for those interested. Sections 1.10.32 and 1.10.33 from "de Finibus Bonorum et Malorum" by Cicero are also reproduced in their exact original form, accompanied by English versions from the 1914 translation by H. Rackham.',
                'created_at' => '2022-06-04 15:34:21',
                'updated_at' => '2022-06-04 15:34:21',
            ),
            1 => 
            array (
                'id' => 2,
                'Image' => 'ArticlesImages/hFg38z7lhXhdL5p3YGS4.jpeg',
                'Sub_Image' => 'ArticlesImages/0JIkTM2ovwsZJsVLPTag.jpeg',
                'Date' => 'May 02, 2017',
                'Arabic_Title' => 'IT\'S ALL ABOUT HOW YOU WEAR',
                'English_Title' => 'IT\'S ALL ABOUT HOW YOU WEAR',
                'Arabic_Desc' => 'qweqweqwe',
                'English_Desc' => 'qweqeqweqwe',
                'created_at' => '2022-06-06 20:02:53',
                'updated_at' => '2022-06-06 20:02:53',
            ),
        ));
        
        
    }
}