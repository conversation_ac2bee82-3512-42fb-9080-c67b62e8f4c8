<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateBefroeFootersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('befroe_footers', function (Blueprint $table) {
            $table->id();
            
            // Footer information
            $table->text('Image')->nullable();
            $table->string('Arabic_Title')->nullable();
            $table->text('Arabic_Desc')->nullable();
            $table->string('English_Title')->nullable();
            $table->text('English_Desc')->nullable();
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('befroe_footers');
    }
}
